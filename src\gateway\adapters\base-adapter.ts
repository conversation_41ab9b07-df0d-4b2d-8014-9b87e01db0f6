import { Injectable, Logger } from '@nestjs/common';
import {
  IProviderAdapter,
  IProvider,
  IProviderConfig,
  ProviderOperation,
} from '../interfaces/provider.interface';

/**
 * Classe base abstrata para todos os adapters de provedores
 */
@Injectable()
export abstract class BaseProviderAdapter implements IProviderAdapter {
  protected readonly logger = new Logger(this.constructor.name);
  
  constructor(protected readonly config: IProviderConfig) {}

  abstract readonly name: string;
  abstract readonly version: string;

  /**
   * Método abstrato para transformar requisições
   */
  abstract transformRequest<T>(rawRequest: any, operation: string): T;

  /**
   * Método abstrato para transformar respostas
   */
  abstract transformResponse<T>(standardResponse: any, operation: string): T;

  /**
   * Validação básica da requisição
   */
  validateRequest(rawRequest: any, operation: string): boolean {
    if (!rawRequest) {
      this.logger.warn(`Empty request for operation: ${operation}`);
      return false;
    }

    if (!this.isValidOperation(operation)) {
      this.logger.warn(`Invalid operation: ${operation}`);
      return false;
    }

    return this.validateSpecificRequest(rawRequest, operation);
  }

  /**
   * Validação específica do provedor (deve ser implementada pelas classes filhas)
   */
  protected abstract validateSpecificRequest(rawRequest: any, operation: string): boolean;

  /**
   * Verifica se a operação é válida
   */
  protected isValidOperation(operation: string): boolean {
    return Object.values(ProviderOperation).includes(operation as ProviderOperation);
  }

  /**
   * Obtém informações do provedor
   */
  getProviderInfo(): IProvider {
    return {
      name: this.name,
      version: this.version,
    };
  }

  /**
   * Log de requisição
   */
  protected logRequest(operation: string, request: any): void {
    this.logger.log(`[${this.name}] ${operation} request:`, {
      operation,
      provider: this.name,
      request: this.sanitizeForLog(request),
    });
  }

  /**
   * Log de resposta
   */
  protected logResponse(operation: string, response: any): void {
    this.logger.log(`[${this.name}] ${operation} response:`, {
      operation,
      provider: this.name,
      response: this.sanitizeForLog(response),
    });
  }

  /**
   * Log de erro
   */
  protected logError(operation: string, error: any): void {
    this.logger.error(`[${this.name}] ${operation} error:`, {
      operation,
      provider: this.name,
      error: error.message || error,
    });
  }

  /**
   * Remove dados sensíveis dos logs
   */
  protected sanitizeForLog(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
    const sanitized = { ...data };

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '***';
      }
    }

    return sanitized;
  }

  /**
   * Valida campos obrigatórios
   */
  protected validateRequiredFields(data: any, requiredFields: string[]): boolean {
    for (const field of requiredFields) {
      if (!data[field] && data[field] !== 0) {
        this.logger.warn(`Missing required field: ${field}`);
        return false;
      }
    }
    return true;
  }

  /**
   * Converte timestamp para formato padrão
   */
  protected normalizeTimestamp(timestamp: any): Date {
    if (!timestamp) {
      return new Date();
    }

    if (timestamp instanceof Date) {
      return timestamp;
    }

    if (typeof timestamp === 'string' || typeof timestamp === 'number') {
      return new Date(timestamp);
    }

    return new Date();
  }

  /**
   * Formata valor monetário
   */
  protected formatAmount(amount: any): number {
    if (typeof amount === 'number') {
      return Math.round(amount * 100) / 100; // 2 casas decimais
    }

    if (typeof amount === 'string') {
      const parsed = parseFloat(amount);
      return isNaN(parsed) ? 0 : Math.round(parsed * 100) / 100;
    }

    return 0;
  }

  /**
   * Gera ID único para transação
   */
  protected generateTransactionId(): string {
    return `${this.name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
