import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';

export enum TransactionType {
  BET = 'bet',
  WIN = 'win',
}

class JackpotBreakdownDto {
  @ApiProperty({ example: '2.2' })
  @IsString()
  contribution: string;

  @ApiProperty({ example: 'string' })
  @IsString()
  id: string;

  @ApiProperty({ example: '2.2' })
  @IsString()
  win: string;
}

class JackpotDetailsDto {
  @ApiProperty({ type: [JackpotBreakdownDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => JackpotBreakdownDto)
  breakdown: JackpotBreakdownDto[];

  @ApiProperty({ example: '1.5' })
  @IsString()
  totalContribution: string;

  @ApiProperty({ example: '2.2' })
  @IsString()
  totalWin: string;
}

export class TransactionDto {
  @ApiProperty({ example: '0.01' })
  @IsString()
  amount: string;

  @ApiProperty({ example: 'bonusBuy' })
  @IsString()
  @IsOptional()
  betType?: string;

  @ApiProperty({ example: 'a4_30044768_int' })
  @IsString()
  externalId: string;

  @ApiProperty({ example: '0.00001' })
  @IsString()
  @IsOptional()
  jackpotContribution?: string;

  // Note that this property will be deprecated on 01.01.2027, use jackpotDetails.breakdown.contribution instead.
  @ApiProperty({ type: JackpotDetailsDto, required: false })
  @ValidateNested()
  @IsOptional()
  @Type(() => JackpotDetailsDto)
  jackpotDetails?: JackpotDetailsDto;

  //Note that this property will be deprecated on 01.01.2027, use jackpotDetails.breakdown.win instead.
  @ApiProperty({ example: '0.01' })
  @IsString()
  @IsOptional()
  jackpotWin?: string;

  @ApiProperty({ enum: TransactionType, example: TransactionType.BET })
  @IsEnum(TransactionType)
  type: TransactionType;
}

export class CreateBetSettleDto {
  @ApiProperty({
    description: 'ID da sessão do jogador',
    example: 'session-123',
    required: true,
  })
  @IsString()
  sessionId: string;

  @ApiProperty({
    description: 'Id do jogador',
    example: 'player-123',
    required: true,
  })
  @IsString()
  playerId: string;

  @ApiProperty({
    description: 'Id da rodada',
    example: 'tx-123',
    required: true,
  })
  @IsString()
  roundId: string;

  @ApiProperty({
    description: 'Id do jogo',
    example: 'e3e54764-96b9-484e-946f-fb576e43850f',
    required: true,
  })
  @IsUUID()
  gameId: string;

  @ApiProperty({
    description: 'Indica se a rodada foi finalizada',
    example: true,
    required: true,
  })
  @IsBoolean()
  roundEnded: boolean;

  @ApiProperty({
    description: 'Array de transações (bet e win)',
    example: [
      { id: 'a4_30044767_int', type: 'bet', amount: '15' },
      { id: 'a4_30044768_int', type: 'win', amount: '120' },
    ],
    required: false,
    type: [TransactionDto],
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => TransactionDto)
  transactions?: TransactionDto[];

  @ApiProperty({
    description: 'Moeda da transação',
    example: 'BRL',
    required: false,
  })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiProperty({
    description: 'Código do agregador',
    example: 'SOFTSWISS',
    required: true,
  })
  @IsString()
  aggregatorCode: string;
}
