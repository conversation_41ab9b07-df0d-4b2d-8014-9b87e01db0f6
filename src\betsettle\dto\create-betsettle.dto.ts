import { Transform } from "class-transformer";
import { IsBoolean, IsDecimal, IsNumber, IsObject, IsString } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class CreateBetSettleDto {
    @ApiProperty({
        description: 'ID da sessão do jogador',
        example: 'session-123',
        required: true
    })
    @IsString()
    sessionId: string;

    @ApiProperty({
        description: 'ID do jogador',
        example: 'player-123',
        required: true
    })
    @IsString()
    playerId: string;

    @ApiProperty({
        description: 'ID da transação',
        example: 'tx-123',
        required: true
    })
    @IsString()
    transactionId: string;

    @ApiProperty({
        description: 'Tipo da requisição',
        example: 'RealMoney',
        required: true
    })
    @IsString()
    requestType: string;

    @ApiProperty({
        description: 'Valor da aposta',
        example: 100.50,
        required: true
    })
    @IsNumber({ maxDecimalPlaces: 2 })
    @Transform(({ value }) => parseFloat(parseFloat(value).toFixed(2)))
    amount: number;

    @ApiProperty({
        description: 'Valor do ganho',
        example: 150.75,
        required: true
    })
    @IsNumber({ maxDecimalPlaces: 2 })
    @Transform(({ value }) => parseFloat(parseFloat(value).toFixed(2)))
    winAmount: number;

    @ApiProperty({
        description: 'ID do jogo',
        example: 123,
        required: true
    })
    @IsNumber()
    gameId: number;

    @ApiProperty({
        description: 'ID da rodada',
        example: 'round-123',
        required: true
    })
    @IsString()
    roundId: string;

    @ApiProperty({
        description: 'Indica se a rodada foi finalizada',
        example: true,
        required: true
    })
    @IsBoolean()
    roundEnded: boolean;

    @ApiProperty({
        description: 'Indica se é um jackpot',
        example: false,
        required: false
    })
    @IsBoolean()
    isJackpot?: boolean;

    @ApiProperty({
        description: 'Informações do jackpot',
        example: { type: 'mega', amount: 10000 },
        required: false
    })
    @IsObject()
    jackpotInfo?: object;

    @ApiProperty({
        description: 'Informações de free spins',
        example: { spins: 10, value: 1.00 },
        required: false
    })
    @IsObject()
    freeSpinInfo?: object;

    @ApiProperty({
        description: 'Informações da aposta',
        example: { type: 'normal', multiplier: 1 },
        required: false
    })
    @IsObject()
    wagerInfo?: object;
}