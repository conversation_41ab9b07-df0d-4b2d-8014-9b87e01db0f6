import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class RequestDepositDto {
  @ApiProperty({
    description: 'Valor do depósito',
    example: 100,
  })
  @IsNumber()
  @IsNotEmpty()
  amount: number;

  @ApiProperty({
    description: 'ID do jogo',
    example: '-600',
  })
  @IsString()
  @IsNotEmpty()
  gameId: string;

  @ApiProperty({
    description: 'ID do jogador',
    example: 'a477dc13-2b4a-4108-aa31-a...',
  })
  @IsString()
  @IsNotEmpty()
  playerId: string;

  @ApiProperty({
    description: 'ID da transação',
    example: 1234567890,
  })
  @IsNumber()
  @IsNotEmpty()
  transactionId: number;

  @ApiProperty({
    description: 'ID do tipo de transação',
    example: 0,
  })
  @IsNumber()
  @IsNotEmpty()
  typeId: number;
}
