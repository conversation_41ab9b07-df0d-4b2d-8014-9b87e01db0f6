import { HttpException, Injectable, Logger } from '@nestjs/common';
import { FreeChipFinishDto } from './dto/finish-freechip.dto';
import { ResponseFinishFreeChipDto } from './dto/response-finish-freechip.dto';
import { firstValueFrom } from 'rxjs';
import { HttpService } from '@nestjs/axios';
import { IssueFreeChipsDto } from './dto/issue-freechip.dto';
import { FreechipCancelDto } from './dto/cancel-freechip.dto';

@Injectable()
export class FreechipService {
  private readonly logger = new Logger(FreechipService.name);
  constructor(private readonly httpService: HttpService) {}
  private urlGateway = process.env.GATEWAY_URL;
  async finish(
    finishFreechipDto: FreeChipFinishDto,
  ): Promise<ResponseFinishFreeChipDto> {
    try {
      this.logger.log('FreechipService', JSON.stringify(finishFreechipDto));
      return { balance: '0.01' };
    } catch (e) {
      this.logger.error('Error to finish freechip', e);
      throw new HttpException(e.message, e.status);
    }
  }

  async cancel(cancelFreechipDto: FreechipCancelDto) {
    try {
      this.logger.log('FreechipService', JSON.stringify(cancelFreechipDto));
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.urlGateway}/casino/freechip/cancel`,
          cancelFreechipDto,
          {
            headers: {
              'Content-Type': 'application/json',
              'x-aggregator': 'softswiss',
            },
          },
        ),
      );
      return response.data;
    } catch (e) {
      this.logger.error('Error to cancel freechip', e);
      throw new HttpException(e.message, e.status);
    }
  }

  async issue(createFreechipDto: IssueFreeChipsDto) {
    try {
      this.logger.log('FreechipService', JSON.stringify(createFreechipDto));
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.urlGateway}/casino/freechip/issue`,
          createFreechipDto,
          {
            headers: {
              'Content-Type': 'application/json',
              'x-aggregator': 'softswiss',
            },
          },
        ),
      );
      return response.data;
    } catch (e) {
      this.logger.error('Error to issue freechip', e);
      throw new HttpException(e.message, e.status);
    }
  }
}
