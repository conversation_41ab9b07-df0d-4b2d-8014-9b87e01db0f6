import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsString, IsUUID } from 'class-validator';

export class ResponsePromoBetDto {
  @ApiProperty({ example: '0.01' })
  @IsString()
  balance: string;

  @ApiProperty({ example: '0.01' })
  @IsString()
  bonus_amount: string;

  @ApiProperty({ example: '1fb7a3e6-6d9a-4e0a-aff9-9b94e9382795' })
  @IsUUID()
  id: string;

  @ApiProperty({ example: 'fd9b4706-5599-47cb-9832-9474c69de4a5' })
  @IsUUID()
  id_casino: string;

  @ApiProperty({ example: '2022-06-15T15:30:00.000054Z' })
  @IsDateString()
  processed_at: string;
}
