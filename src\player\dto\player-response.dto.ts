import { ApiProperty } from '@nestjs/swagger';

export class GenderDto {
  @ApiProperty({ description: 'ID do gênero' })
  id: string;

  @ApiProperty({ description: 'Código do gênero', example: 'Masculino' })
  code: string;
}

export class PersonalInformationDto {
  @ApiProperty({ description: 'ID da informação pessoal' })
  id: string;

  @ApiProperty({ description: 'ID do jogador' })
  playerId: string;

  @ApiProperty({ description: 'Nome do jogador' })
  name: string;

  @ApiProperty({ description: 'Nome do meio', nullable: true })
  middleName: string | null;

  @ApiProperty({ description: 'Sobrenome' })
  lastName: string;

  @ApiProperty({ description: 'Data de nascimento' })
  birthDate: string;

  @ApiProperty({ description: 'Cidade' })
  city: string;

  @ApiProperty({ description: 'ID do gênero' })
  genderId: string;

  @ApiProperty({ description: 'Número do documento' })
  documentNumber: string;

  @ApiProperty({ description: 'ID pessoal' })
  personalId: string;

  @ApiProperty({ description: 'Data de expiração' })
  expirationDate: string;

  @ApiProperty({ description: 'Data de emissão' })
  emissionDate: string;

  @ApiProperty({ description: 'Emitido por', nullable: true })
  emittedBy: string | null;

  @ApiProperty({ description: 'Código de emissão do documento', nullable: true })
  codeEmissionDocument: string | null;

  @ApiProperty({ description: 'Data de criação' })
  createdAt: string;

  @ApiProperty({ description: 'Data de atualização' })
  updatedAt: string;

  @ApiProperty({ description: 'Data de exclusão', nullable: true })
  deletedAt: string | null;

  @ApiProperty({ description: 'Indica se foi deletado', nullable: true })
  isDeleted: boolean | null;

  @ApiProperty({ description: 'Informações de gênero', type: GenderDto })
  gender: GenderDto;
}

export class SocialPreferencesDto {
  @ApiProperty({ description: 'ID das preferências sociais' })
  id: string;

  @ApiProperty({ description: 'ID do jogador' })
  playerId: string;

  @ApiProperty({ description: 'Inscrito para e-mail' })
  isSubscribeEmail: boolean;

  @ApiProperty({ description: 'Inscrito para notificações' })
  isSubscribeNotification: boolean;

  @ApiProperty({ description: 'Inscrito para SMS' })
  isSubscribeSms: boolean;

  @ApiProperty({ description: 'Inscrito para chamada telefônica' })
  isSubscribePhonecall: boolean;

  @ApiProperty({ description: 'Inscrito para newsletter', nullable: true })
  isSubscribedNewsletter: boolean | null;

  @ApiProperty({
    description: 'Data local de aceitação da versão dos T&C',
    nullable: true,
  })
  tcVersionAcceptanceLocaldate: string | null;

  @ApiProperty({ description: 'Inscrito para mensagem interna' })
  isSubscribeInternalMessage: boolean;

  @ApiProperty({ description: 'Versão dos termos e condições', nullable: true })
  termsConditionsVersion: string | null;

  @ApiProperty({ description: 'Data de criação' })
  createdAt: string;

  @ApiProperty({ description: 'Data de atualização' })
  updatedAt: string;

  @ApiProperty({ description: 'Data de exclusão', nullable: true })
  deletedAt: string | null;

  @ApiProperty({ description: 'Indica se foi deletado', nullable: true })
  isDeleted: boolean | null;
}

export class FinancialInformationDto {
  @ApiProperty({ description: 'ID da informação financeira' })
  id: string;

  @ApiProperty({ description: 'ID do jogador' })
  playerId: string;

  @ApiProperty({ description: 'Nome do banco' })
  bankName: string;

  @ApiProperty({ description: 'Agência bancária' })
  bankBranch: string;

  @ApiProperty({ description: 'Conta' })
  account: string;

  @ApiProperty({ description: 'IBAN', nullable: true })
  iban: string | null;

  @ApiProperty({ description: 'Código SWIFT', nullable: true })
  swiftCode: string | null;

  @ApiProperty({ description: 'Titular da conta' })
  accountHolder: string;

  @ApiProperty({ description: 'Data de criação' })
  createdAt: string;

  @ApiProperty({ description: 'Data de atualização' })
  updatedAt: string;

  @ApiProperty({ description: 'Data de exclusão', nullable: true })
  deletedAt: string | null;

  @ApiProperty({ description: 'Indica se foi deletado', nullable: true })
  isDeleted: boolean | null;
}

export class AdditionalInformationDto {
  @ApiProperty({ description: 'ID da informação adicional' })
  id: string;

  @ApiProperty({ description: 'ID do jogador' })
  playerId: string;

  @ApiProperty({ description: 'ID do cliente', nullable: true })
  clientId: string | null;

  @ApiProperty({ description: 'Salário mínimo', nullable: true })
  salaryMin: number | null;

  @ApiProperty({ description: 'Salário máximo', nullable: true })
  salaryMax: number | null;

  @ApiProperty({ description: 'Descrição', nullable: true })
  description: string | null;

  @ApiProperty({ description: 'Ocupação', nullable: true })
  occupation: string | null;

  @ApiProperty({ description: 'ID externo de exclusão', nullable: true })
  exclusionExternalId: string | null;

  @ApiProperty({ description: 'Tag UTM', nullable: true })
  utmTag: string | null;

  @ApiProperty({ description: 'PIN RFID', nullable: true })
  rfidPin: string | null;

  @ApiProperty({ description: 'ID do Telegram', nullable: true })
  telegramId: string | null;

  @ApiProperty({ description: 'ID do agente social', nullable: true })
  socialAgentId: string | null;

  @ApiProperty({ description: 'Nome do sistema de pagamento', nullable: true })
  paymentSystemName: string | null;

  @ApiProperty({ description: 'Tem doença mental', nullable: true })
  hasMentalIllness: boolean | null;

  @ApiProperty({ description: 'Data da doença mental', nullable: true })
  mentalIllnessDate: string | null;

  @ApiProperty({ description: 'Nome de usuário', nullable: true })
  userName: string | null;

  @ApiProperty({ description: 'Confirmação de restrições', nullable: true })
  confirmationRestrictions: string | null;

  @ApiProperty({ description: 'Informação do dispositivo', nullable: true })
  deviceInfo: string | null;

  @ApiProperty({ description: 'Data de criação' })
  createdAt: string;

  @ApiProperty({ description: 'Data de atualização', nullable: true })
  updatedAt: string | null;

  @ApiProperty({ description: 'Data de exclusão', nullable: true })
  deletedAt: string | null;

  @ApiProperty({ description: 'Indica se foi deletado', nullable: true })
  isDeleted: boolean | null;
}

export class RegionDto {
  @ApiProperty({ description: 'País' })
  country: string;

  @ApiProperty({ description: 'Código do país' })
  code: string;

  @ApiProperty({ description: 'Idioma' })
  language: string;

  @ApiProperty({ description: 'Nome' })
  name: string;
}

export class ContactInfoDto {
  @ApiProperty({ description: 'ID da informação de contato' })
  id: string;

  @ApiProperty({ description: 'ID do jogador' })
  playerId: string;

  @ApiProperty({ description: 'Telefone' })
  phone: string;

  @ApiProperty({ description: 'Celular' })
  mobile: string;

  @ApiProperty({ description: 'E-mail' })
  email: string;

  @ApiProperty({ description: 'ID da região' })
  regionId: string;

  @ApiProperty({ description: 'CEP' })
  zipCode: string;

  @ApiProperty({ description: 'Cidade' })
  city: string;

  @ApiProperty({ description: 'Endereço' })
  address: string;

  @ApiProperty({ description: 'Estado' })
  state: string;

  @ApiProperty({ description: 'Data de criação' })
  createdAt: string;

  @ApiProperty({ description: 'Data de atualização' })
  updatedAt: string;

  @ApiProperty({ description: 'Informações da região', type: RegionDto })
  region: RegionDto;
}

export class AccountInfoDto {
  @ApiProperty({ description: 'ID da informação da conta' })
  id: string;

  @ApiProperty({ description: 'ID do jogador' })
  playerId: string;

  @ApiProperty({ description: 'ID do parceiro' })
  partnerId: string;

  @ApiProperty({ description: 'ID externo' })
  externalId: string;

  @ApiProperty({ description: 'Status da conta' })
  status: boolean;

  @ApiProperty({ description: 'Conta verificada', nullable: true })
  isVerified: boolean | null;

  @ApiProperty({ description: 'Cassino bloqueado' })
  isCassinoBlocked: boolean;

  @ApiProperty({ description: 'Sport bloqueado' })
  isSportBlocked: boolean;

  @ApiProperty({ description: 'RMT bloqueado' })
  isRmtBlocked: boolean;

  @ApiProperty({ description: 'É conta de teste', nullable: true })
  isTest: boolean | null;

  @ApiProperty({ description: 'BTAG', nullable: true })
  btag: string | null;

  @ApiProperty({ description: 'Autenticação multifator habilitada' })
  enableMultiFactor: boolean;

  @ApiProperty({ description: 'É pessoa exposta politicamente', nullable: true })
  isPep: boolean | null;

  @ApiProperty({ description: 'Data de criação' })
  createdAt: string;

  @ApiProperty({ description: 'Data de atualização' })
  updatedAt: string;
}

export class TermsConditionsDto {
  @ApiProperty({ description: 'ID dos termos e condições' })
  id: string;

  @ApiProperty({ description: 'ID do parceiro' })
  partnerId: string;

  @ApiProperty({ description: 'ID do usuário' })
  userId: string;

  @ApiProperty({ description: 'Versão dos termos' })
  version: number;

  @ApiProperty({ description: 'URL dos termos', nullable: true })
  url: string | null;

  @ApiProperty({ description: 'Data de criação' })
  createdAt: string;

  @ApiProperty({ description: 'Data de atualização' })
  updatedAt: string;

  @ApiProperty({ description: 'Data de exclusão', nullable: true })
  deletedAt: string | null;

  @ApiProperty({ description: 'Descrição do usuário' })
  userDescription: string;

  @ApiProperty({ description: 'Texto dos termos' })
  term: string;

  @ApiProperty({ description: 'Termos estão ativos' })
  isActive: boolean;

  @ApiProperty({ description: 'Termos precisam ser aceitos' })
  isRequiredToAccepted: boolean;
}

export class TermsAcceptedDto {
  @ApiProperty({ description: 'ID do termo aceito' })
  id: string;

  @ApiProperty({ description: 'ID do jogador' })
  playerId: string;

  @ApiProperty({ description: 'ID dos termos e condições' })
  idTermsConditions: string;

  @ApiProperty({ description: 'ID da sessão', nullable: true })
  idSession: string | null;

  @ApiProperty({ description: 'Termos foram aceitos' })
  isAccepted: boolean;

  @ApiProperty({ description: 'Data de criação' })
  createdAt: string;

  @ApiProperty({ description: 'Data de atualização' })
  updatedAt: string;

  @ApiProperty({
    description: 'Detalhes dos termos e condições',
    type: TermsConditionsDto,
  })
  termsConditions: TermsConditionsDto;
}

export class PlayerStateDto {
  @ApiProperty({ description: 'ID do estado' })
  id: string;

  @ApiProperty({ description: 'Descrição do estado' })
  description: string;

  @ApiProperty({ description: 'Data de criação' })
  createdAt: string;

  @ApiProperty({ description: 'Data de atualização', nullable: true })
  updatedAt: string | null;

  @ApiProperty({ description: 'Data de exclusão', nullable: true })
  deletedAt: string | null;

  @ApiProperty({ description: 'Indica se foi deletado', nullable: true })
  isDeleted: boolean | null;
}

export class PlayerCategoryDto {
  @ApiProperty({ description: 'ID da categoria' })
  id: string;

  @ApiProperty({ description: 'Descrição da categoria' })
  description: string;

  @ApiProperty({ description: 'Data de criação' })
  createdAt: string;

  @ApiProperty({ description: 'Data de atualização', nullable: true })
  updatedAt: string | null;

  @ApiProperty({ description: 'Data de exclusão', nullable: true })
  deletedAt: string | null;

  @ApiProperty({ description: 'Indica se foi deletado', nullable: true })
  isDeleted: boolean | null;
}

export class PlayerCapabilitiesDto {
  @ApiProperty({ description: 'ID das capacidades' })
  id: string;

  @ApiProperty({ description: 'ID do jogador' })
  playerId: string;

  @ApiProperty({ description: 'Pode fazer login' })
  canLogin: boolean;

  @ApiProperty({ description: 'Pode sacar' })
  canWithdraw: boolean;

  @ApiProperty({ description: 'Pode aumentar limite' })
  canIncreaseLimit: boolean;

  @ApiProperty({ description: 'Pode reivindicar bônus' })
  canClaimBonus: boolean;

  @ApiProperty({ description: 'Pode fazer login no cassino' })
  canCasinoLogin: boolean;

  @ApiProperty({ description: 'Pode apostar' })
  canBet: boolean;

  @ApiProperty({ description: 'Pode depositar' })
  canDeposit: boolean;

  @ApiProperty({ description: 'Última atualização por', nullable: true })
  lastUpdatedBy: string | null;

  @ApiProperty({
    description: 'E-mail de quem fez a última atualização',
    nullable: true,
  })
  lastUpdatedByEmail: string | null;

  @ApiProperty({ description: 'Data da última atualização' })
  lastUpdateAt: string;
}

export class CurrencyDto {
  @ApiProperty({ description: 'ID da moeda' })
  id: string;

  @ApiProperty({ description: 'Código da moeda', example: 'BRL' })
  code: string;

  @ApiProperty({ description: 'Nome da moeda', example: 'Real Brasileiro' })
  name: string;

  @ApiProperty({ description: 'Símbolo da moeda', example: 'R$' })
  symbol: string;

  @ApiProperty({ description: 'Data de criação' })
  createdAt: string;

  @ApiProperty({ description: 'Data de atualização', nullable: true })
  updatedAt: string | null;
}

export class PlayerResponseDto {
  @ApiProperty({ description: 'ID do jogador' })
  id: string;

  @ApiProperty({ description: 'ID do Payment Hub' })
  paymentHubId: number;

  @ApiProperty({ description: 'ID do cliente' })
  customerId: string;

  @ApiProperty({ description: 'ID do estado' })
  stateId: string;

  @ApiProperty({ description: 'ID da categoria' })
  categoryId: string;

  @ApiProperty({ description: 'ID do afiliado' })
  affiliateId: string;

  @ApiProperty({ description: 'Jogador está offline', nullable: true })
  isOffline: boolean | null;

  @ApiProperty({ description: 'Data de criação' })
  createdAt: string;

  @ApiProperty({ description: 'Data de atualização' })
  updatedAt: string;

  @ApiProperty({ description: 'Data de exclusão', nullable: true })
  deletedAt: string | null;

  @ApiProperty({ description: 'Indica se foi deletado', nullable: true })
  isDeleted: boolean | null;

  @ApiProperty({ description: 'Código de confirmação de e-mail' })
  codeEmailConfirmed: number;
  
  @ApiProperty({ description: 'E-mail confirmado' })
  isEmailConfirmed: boolean;

  @ApiProperty({ description: 'Código de reset de senha' })  
  resetCode: number;

  @ApiProperty({ description: 'Data de expiração do código de reset' })
  resetExpires: string;

  @ApiProperty({ description: 'ID da moeda' })
  currencyId: string;

  @ApiProperty({ description: 'ID do perfil do cassino' })
  casinoProfileId: string;

  @ApiProperty({
    description: 'Preferências sociais do jogador',
    type: SocialPreferencesDto,
  })
  socialPreferences: SocialPreferencesDto;

  @ApiProperty({
    description: 'Informações pessoais do jogador',
    type: PersonalInformationDto,
  })
  personalInformation: PersonalInformationDto;

  @ApiProperty({
    description: 'Informações financeiras do jogador',
    type: FinancialInformationDto,
  })
  financialInformation: FinancialInformationDto;

  @ApiProperty({
    description: 'Informações adicionais do jogador',
    type: AdditionalInformationDto,
  })
  additionalInformation: AdditionalInformationDto;

  @ApiProperty({
    description: 'Informações de contato do jogador',
    type: ContactInfoDto,
  })
  contactInfo: ContactInfoDto;

  @ApiProperty({
    description: 'Informações da conta do jogador',
    type: AccountInfoDto,
  })
  accountInfo: AccountInfoDto;

  @ApiProperty({
    description: 'Termos aceitos pelo jogador',
    type: TermsAcceptedDto,
  })
  termsAccepted: TermsAcceptedDto;

  @ApiProperty({ description: 'Estado do jogador', type: PlayerStateDto })
  state: PlayerStateDto;

  @ApiProperty({ description: 'Categoria do jogador', type: PlayerCategoryDto })
  category: PlayerCategoryDto;

  @ApiProperty({
    description: 'Capacidades/permissões do jogador',
    type: PlayerCapabilitiesDto,
  })
  capabilities: PlayerCapabilitiesDto;

  @ApiProperty({ description: 'Moeda do jogador', type: CurrencyDto })
  currency: CurrencyDto;
}
