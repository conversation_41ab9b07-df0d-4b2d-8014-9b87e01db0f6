import { Test, TestingModule } from '@nestjs/testing';
import { BonusController } from './bonus.controller';
import { BonusService } from './bonus.service';

describe('BonusController', () => {
  let controller: BonusController;
  let service: {
    finish: jest.Mock;
    cancel: jest.Mock;
    issue: jest.Mock;
    types: jest.Mock;
  };

  beforeEach(async () => {
    service = {
      finish: jest.fn(),
      cancel: jest.fn(),
      issue: jest.fn(),
      types: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [BonusController],
      providers: [{ provide: BonusService, useValue: service }],
    }).compile();

    controller = module.get<BonusController>(BonusController);
  });

  it('finish should call service.finish and return result', async () => {
    const dto: any = { amount: '10' };
    const result = { balance: '0.01' };
    service.finish.mockResolvedValue(result);

    await expect(controller.finish(dto)).resolves.toEqual(result);
    expect(service.finish).toHaveBeenCalledWith(dto);
  });

  it('cancel should call service.cancel and return result', async () => {
    const dto: any = { promotionId: 'p1' };
    const result = { ok: true };
    service.cancel.mockResolvedValue(result);

    await expect(controller.cancel(dto)).resolves.toEqual(result);
    expect(service.cancel).toHaveBeenCalledWith(dto);
  });

  it('issue should call service.issue and return result', async () => {
    const dto: any = { type: 'WELCOME' };
    const result = { bonusId: 'b1' };
    service.issue.mockResolvedValue(result);

    await expect(controller.issue(dto)).resolves.toEqual(result);
    expect(service.issue).toHaveBeenCalledWith(dto);
  });

  it('types should call service.types and return result', async () => {
    const dto: any = { brandId: 'x' };
    const result = { types: ['WELCOME'] };
    service.types.mockResolvedValue(result);

    await expect(controller.types(dto)).resolves.toEqual(result);
    expect(service.types).toHaveBeenCalledWith(dto);
  });
});
