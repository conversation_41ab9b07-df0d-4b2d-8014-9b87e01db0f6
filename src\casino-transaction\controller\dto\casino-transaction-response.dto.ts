import { ApiProperty } from '@nestjs/swagger';

export class CasinoTransactionResponseDto {
  @ApiProperty({ description: 'ID da transação' })
  id: string;

  @ApiProperty({ description: 'ID do jogador' })
  playerId: string;

  @ApiProperty({ description: 'ID do jogo' })
  gameId: string;

  @ApiProperty({ description: 'ID do parceiro' })
  partnerId: string;

  @ApiProperty({ description: 'ID da rodada' })
  roundId: string;

  @ApiProperty({ description: 'Valor da aposta' })
  amount: number;

  @ApiProperty({ description: 'Valor do ganho' })
  winAmount: number;

  @ApiProperty({ description: 'Tipo da transação' })
  type: string;

  @ApiProperty({ description: 'Status da rodada' })
  roundEnded: boolean;

  @ApiProperty({ description: 'Data de criação' })
  createdAt: Date;

  @ApiProperty({ description: 'Data de atualização' })
  updatedAt: Date;
}

export const CasinoTransactionResponseDtoExample = {
  id: 'transaction-123',
  playerId: 'player-123',
  gameId: 'game-123',
  partnerId: 'partner-123',
  roundId: 'round-123',
};
