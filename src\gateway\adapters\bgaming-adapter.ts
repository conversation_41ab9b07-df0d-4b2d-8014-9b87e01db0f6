import { Injectable } from '@nestjs/common';
import { BaseProviderAdapter } from './base-adapter';
import {
  IBalanceRequest,
  IBalanceResponse,
  IBetRequest,
  IBetResponse,
  IWinRequest,
  IWinResponse,
  IProviderConfig,
  ProviderOperation,
} from '../interfaces/provider.interface';

/**
 * Adapter para o provedor BGaming
 */
@Injectable()
export class BgamingAdapter extends BaseProviderAdapter {
  readonly name = 'BGAMING';
  readonly version = '1.0.0';

  constructor(config: IProviderConfig) {
    super(config);
  }

  /**
   * Transforma requisição do BGaming para formato padrão
   */
  transformRequest<T>(rawRequest: any, operation: string): T {
    this.logRequest(operation, rawRequest);

    switch (operation) {
      case ProviderOperation.BALANCE:
        return this.transformBalanceRequest(rawRequest) as T;
      case ProviderOperation.BET:
        return this.transformBetRequest(rawRequest) as T;
      case ProviderOperation.WIN:
        return this.transformWinRequest(rawRequest) as T;
      default:
        throw new Error(`Unsupported operation: ${operation}`);
    }
  }

  /**
   * Transforma resposta padrão para formato BGaming
   */
  transformResponse<T>(standardResponse: any, operation: string): T {
    this.logResponse(operation, standardResponse);

    switch (operation) {
      case ProviderOperation.BALANCE:
        return this.transformBalanceResponse(standardResponse) as T;
      case ProviderOperation.BET:
        return this.transformBetResponse(standardResponse) as T;
      case ProviderOperation.WIN:
        return this.transformWinResponse(standardResponse) as T;
      default:
        throw new Error(`Unsupported operation: ${operation}`);
    }
  }

  /**
   * Validação específica para BGaming
   */
  protected validateSpecificRequest(rawRequest: any, operation: string): boolean {
    switch (operation) {
      case ProviderOperation.BALANCE:
        return this.validateRequiredFields(rawRequest, ['playerId', 'gameCode']);
      case ProviderOperation.BET:
        return this.validateRequiredFields(rawRequest, [
          'playerId',
          'gameCode',
          'betAmount',
          'roundId',
        ]);
      case ProviderOperation.WIN:
        return this.validateRequiredFields(rawRequest, [
          'playerId',
          'gameCode',
          'winAmount',
          'roundId',
          'betId',
        ]);
      default:
        return false;
    }
  }

  /**
   * Transforma requisição de saldo BGaming
   */
  private transformBalanceRequest(rawRequest: any): IBalanceRequest {
    return {
      playerId: rawRequest.playerId,
      gameId: rawRequest.gameCode,
      currency: rawRequest.currency || 'BRL',
      sessionId: rawRequest.sessionToken,
      timestamp: this.normalizeTimestamp(rawRequest.requestTime),
      metadata: {
        provider: this.name,
        operatorId: rawRequest.operatorId,
        originalRequest: rawRequest,
      },
    };
  }

  /**
   * Transforma requisição de aposta BGaming
   */
  private transformBetRequest(rawRequest: any): IBetRequest {
    return {
      playerId: rawRequest.playerId,
      gameId: rawRequest.gameCode,
      amount: this.formatAmount(rawRequest.betAmount),
      currency: rawRequest.currency || 'BRL',
      roundId: rawRequest.roundId,
      sessionId: rawRequest.sessionToken,
      timestamp: this.normalizeTimestamp(rawRequest.requestTime),
      metadata: {
        provider: this.name,
        operatorId: rawRequest.operatorId,
        gameRoundId: rawRequest.gameRoundId,
        originalRequest: rawRequest,
      },
    };
  }

  /**
   * Transforma requisição de ganho BGaming
   */
  private transformWinRequest(rawRequest: any): IWinRequest {
    return {
      playerId: rawRequest.playerId,
      gameId: rawRequest.gameCode,
      amount: this.formatAmount(rawRequest.winAmount),
      currency: rawRequest.currency || 'BRL',
      roundId: rawRequest.roundId,
      betTransactionId: rawRequest.betId,
      sessionId: rawRequest.sessionToken,
      timestamp: this.normalizeTimestamp(rawRequest.requestTime),
      metadata: {
        provider: this.name,
        operatorId: rawRequest.operatorId,
        gameRoundId: rawRequest.gameRoundId,
        originalRequest: rawRequest,
      },
    };
  }

  /**
   * Transforma resposta de saldo para formato BGaming
   */
  private transformBalanceResponse(standardResponse: IBalanceResponse): any {
    return {
      playerId: standardResponse.playerId,
      balance: standardResponse.balance,
      currency: standardResponse.currency,
      requestTime: standardResponse.timestamp.toISOString(),
      status: standardResponse.success ? 'OK' : 'ERROR',
      errorCode: standardResponse.success ? null : 1001,
      errorDescription: standardResponse.error || null,
    };
  }

  /**
   * Transforma resposta de aposta para formato BGaming
   */
  private transformBetResponse(standardResponse: IBetResponse): any {
    return {
      playerId: standardResponse.playerId,
      transactionId: standardResponse.transactionId,
      balance: standardResponse.balance,
      currency: standardResponse.currency,
      requestTime: standardResponse.timestamp.toISOString(),
      status: standardResponse.success ? 'OK' : 'ERROR',
      errorCode: standardResponse.success ? null : 1002,
      errorDescription: standardResponse.error || null,
    };
  }

  /**
   * Transforma resposta de ganho para formato BGaming
   */
  private transformWinResponse(standardResponse: IWinResponse): any {
    return {
      playerId: standardResponse.playerId,
      transactionId: standardResponse.transactionId,
      balance: standardResponse.balance,
      currency: standardResponse.currency,
      requestTime: standardResponse.timestamp.toISOString(),
      status: standardResponse.success ? 'OK' : 'ERROR',
      errorCode: standardResponse.success ? null : 1003,
      errorDescription: standardResponse.error || null,
    };
  }
}
