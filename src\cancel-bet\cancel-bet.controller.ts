import {
  ALREADY_PROCESSED,
  BET_NOT_FOUND,
  GAME_NOT_FOUND,
  GENERIC_ERROR,
  PLAYER_NOT_FOUND,
  ROUND_NOT_FOUND,
  SESSION_NOT_FOUND,
  SUCCESS,
} from '@/common/constants/message-codes';
import { SwaggerApiCodeResponses } from '@/common/decorators/swagger-api-code-responses';
import {
  ApiHmacHeader,
  ApiHmacUnauthorizedResponse,
} from '@/common/decorators/swagger.decorator';
import { Body, Controller, HttpCode, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CancelBetService } from './cancel-bet.service';
import { CancelBetDto } from './dto/create-cancel-bet.dto';

@Controller('cancel-bet')
@ApiTags('CancelBet')
export class CancelBetController {
  constructor(private readonly cancelBetService: CancelBetService) {}

  //@UseGuards(HMACGuard)
  @Post()
  @HttpCode(200)
  @ApiOperation({
    summary: 'Cancelar aposta',
    description: 'Endpoint para cancelar uma aposta realizada',
  })
  @ApiHmacHeader()
  @ApiHmacUnauthorizedResponse()
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
      BET_NOT_FOUND.code,
    ],
    null,
    null,
  )
  @ApiResponse({
    status: 404,
    description: 'Aposta não encontrada',
    schema: {
      type: 'object',
      properties: {
        errorCode: {
          type: 'number',
          example: 1115,
          description: 'Código de erro de aposta não encontrada',
        },
        errorMsg: {
          type: 'string',
          example: 'Bet not found',
          description: 'Mensagem de erro de aposta não encontrada',
        },
      },
    },
  })
  async cancelBet(@Body() cancelBet: CancelBetDto) {
    return this.cancelBetService.cancelBet(cancelBet);
  }
}
