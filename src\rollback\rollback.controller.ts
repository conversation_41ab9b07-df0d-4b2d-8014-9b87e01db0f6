import { BusinessExceptionFilter } from '@/common/filters/business-exception.filter';
import { Body, Controller, Post, UseFilters } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { RollbackRequestDto } from './dto/rollback-request.dto';
import { RollbackResponseDto } from './dto/rollback-response.dto';
import { RollbackService } from './rollback.service';

@ApiTags('Rollback')
@Controller('rollback')
export class RollbackController {
  constructor(private readonly rollbackService: RollbackService) {}

  @Post()
  @UseFilters(BusinessExceptionFilter)
  @ApiOperation({
    summary: 'Process rollback transaction',
    description:
      'Revert bet or win transactions from game provider. All transactions are processed idempotently.',
  })
  @ApiResponse({
    status: 200,
    description: 'Rollback processed successfully',
    type: RollbackResponseDto,
  })
  async processRollback(
    @Body() dto: RollbackRequestDto,
  ): Promise<RollbackResponseDto> {
    return this.rollbackService.processRollback(dto);
  }
}
