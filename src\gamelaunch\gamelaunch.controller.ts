import {
  ALREADY_PROCESSED,
  GAME_NOT_FOUND,
  GENERIC_ERROR,
  PLAYER_NOT_FOUND,
  ROUND_NOT_FOUND,
  SESSION_NOT_FOUND,
  SUCCESS,
} from '@/common/constants/message-codes';
import { SwaggerApiCodeResponses } from '@/common/decorators/swagger-api-code-responses';
import {
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@/common/decorators/swagger.decorator';
import { JwtAuthPlayerGuard } from '@/common/guards/player/jwt-auth-player.guard';
import { LoggingInterceptor } from '@/common/interceptors/logging.interceptor';
import {
  Body,
  Controller,
  HttpCode,
  Logger,
  Post,
  Req,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import {
  GameLaunchResponseDTO,
  GameLaunchResponseDTOExample,
} from './dto/game-launch-response.dto';
import { CreateLauncherDto } from './dto/game-launch.dto';
import { GameModeEnum } from './enum/game-mode.enum';
import { GameLaunchService } from './gamelaunch.service';

@Controller('game-launch')
@ApiTags('GameLaunch')
@ApiBearerAuth('access-token')
export class GameLaunchController {
  private readonly logger = new Logger(GameLaunchController.name);
  constructor(private readonly gameLaunchService: GameLaunchService) {}

  @UseGuards(JwtAuthPlayerGuard)
  @Post()
  @UseInterceptors(LoggingInterceptor)
  @HttpCode(200)
  @ApiOperation({
    summary: 'Iniciar jogo',
    description: 'Endpoint para iniciar um jogo em modo real ou free_play',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    GameLaunchResponseDTO,
    GameLaunchResponseDTOExample,
  )
  @ApiForbiddenResponse()
  @ApiInternalServerErrorResponse()
  async play(
    @Body() body: CreateLauncherDto,
    @Req() req,
  ): Promise<{
    launchUrl?: string;
  }> {
    if (body.gameMode === GameModeEnum.REAL_PLAY) {
      this.logger.log(`Game launch request real_play: ${JSON.stringify(body)}`);
      return this.gameLaunchService.getRealPlayURL(body, req);
    }
    this.logger.log(`Game launch request free_play: ${JSON.stringify(body)}`);
    return this.gameLaunchService.getFreeUrl(body, req);
  }
}
