import {
  Body,
  Controller,
  HttpCode,
  Logger,
  Post,
  Req,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { GameLaunchService } from './gamelaunch.service';
import { LoggingInterceptor } from '@/common/interceptors/logging.interceptor';
import { GameLaunchDTO } from './dto/game-launch.dto';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { RequireCapabilities } from '@/common/decorators/require-capabilities.decorator';
import { JwtAuthPlayerGuard } from '@/common/guards/player/jwt-auth-player.guard';
import { CapabilitiesGuard } from '@/common/guards/capabilities/capabilities.guard';
import { typeCapabilitiesEnum } from '@/common/emuns/type-capabilities.enum';
import {
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@/common/decorators/swagger.decorator';
import { SwaggerApiCodeResponses } from '@/common/decorators/swagger-api-code-responses';
import {
  SUCCESS,
  ALREADY_PROCESSED,
  GENERIC_ERROR,
  PLAYER_NOT_FOUND,
  SESSION_NOT_FOUND,
  GAME_NOT_FOUND,
  ROUND_NOT_FOUND,
} from '@/common/constants/message-codes';
import {
  GameLaunchResponseDTO,
  GameLaunchResponseDTOExample,
} from './dto/game-launch-response.dto';

@Controller('game-launch')
@ApiTags('GameLaunch')
@ApiBearerAuth('access-token')
export class GameLaunchController {
  private readonly logger = new Logger(GameLaunchController.name);
  constructor(private readonly gameLaunchService: GameLaunchService) {}

  @UseGuards(JwtAuthPlayerGuard, CapabilitiesGuard)
  @RequireCapabilities(typeCapabilitiesEnum.CanCasinoLogin)
  @Post()
  @UseInterceptors(LoggingInterceptor)
  @HttpCode(200)
  @ApiOperation({
    summary: 'Iniciar jogo',
    description: 'Endpoint para iniciar um jogo em modo real ou free-play',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    GameLaunchResponseDTO,
    GameLaunchResponseDTOExample
  )
  @ApiForbiddenResponse()
  @ApiInternalServerErrorResponse()
  async play(
    @Body() body: GameLaunchDTO,
    @Req() req
  ): Promise<{
    errorCode: number;
    errorMsg?: string;
    launchToken?: string;
    launchUrl?: string;
  }> {
    if (body.gameMode !== 'free-play') {
      this.logger.log(`Game launch request real-play: ${JSON.stringify(body)}`);
      return this.gameLaunchService.getURL(body, req.headers.authorization);
    }
    this.logger.log(`Game launch request free-play: ${JSON.stringify(body)}`);
    return this.gameLaunchService.getFreeUrl(body);
  }
}
