import { Test, TestingModule } from '@nestjs/testing';
import { DailyReportController } from './daily-report.controller';
import { DailyReportService } from '../services/daily-report.service';
import { BackofficeGuard } from '@/common/guards/backoffice/backoffice.guard';
import { JwtService } from '@nestjs/jwt';

describe('DailyReportController', () => {
  let controller: DailyReportController;

  const mockDailyReportService = {
    saveDailyReportGame: jest.fn(),
    saveDailyReportGamePlayerPartner: jest.fn(),
    saveDailyReportPlayer: jest.fn(),
    saveDailyReportProviders: jest.fn(),
    saveDailyReportPartner: jest.fn(),
    getDailyReportGamePagination: jest.fn(),
    getDailyReportGame: jest.fn(),
    getDailyReportGamePlayerPartnerPagination: jest.fn(),
    getDailyReportGamePlayerPartner: jest.fn(),
    getDailyReportPlayerById: jest.fn(),
    getDailyReportPlayer: jest.fn(),
    getDailyReportProviders: jest.fn(),
    getDailyReportProvidersPagination: jest.fn(),
    getDailyReportPartner: jest.fn(),
    getDailyReportPartnerPagination: jest.fn(),
    getGameProfitability: jest.fn(),
  };
  const mockBackofficeGuard = { canActivate: jest.fn(() => true) };
  const mockJwtService = {};

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DailyReportController],
      providers: [
        { provide: DailyReportService, useValue: mockDailyReportService },
        { provide: JwtService, useValue: mockJwtService },
      ],
    })
      .overrideGuard(BackofficeGuard)
      .useValue(mockBackofficeGuard)
      .compile();

    controller = module.get<DailyReportController>(DailyReportController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('forceDailyReport', () => {
    it('deve executar todos os métodos de relatório e retornar mensagem de sucesso', async () => {
      await expect(controller.forceDailyReport()).resolves.toBe(
        'Cron job run successfully',
      );
      expect(mockDailyReportService.saveDailyReportGame).toHaveBeenCalled();
      expect(
        mockDailyReportService.saveDailyReportGamePlayerPartner,
      ).toHaveBeenCalled();
      expect(mockDailyReportService.saveDailyReportPlayer).toHaveBeenCalled();
      expect(
        mockDailyReportService.saveDailyReportProviders,
      ).toHaveBeenCalled();
      expect(mockDailyReportService.saveDailyReportPartner).toHaveBeenCalled();
    });
    it('deve lançar erro se algum método falhar', async () => {
      mockDailyReportService.saveDailyReportGame.mockRejectedValueOnce(
        new Error('Erro'),
      );
      await expect(controller.forceDailyReport()).rejects.toThrow('Erro');
      mockDailyReportService.saveDailyReportGame.mockResolvedValue(undefined); // reset para outros testes
    });
  });

  describe('getDailyReportGamePagination', () => {
    it('deve retornar o resultado do serviço', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        partners: 'p1',
        fromDate: '2024-01-01',
        toDate: '2024-01-31',
        gameId: 'g1',
        provider: 'prov1',
        accountType: 'real',
      };
      const req = {};
      const expected = { data: [], total: 0 };
      mockDailyReportService.getDailyReportGamePagination.mockResolvedValue(
        expected,
      );
      const result = await controller.getDailyReportGamePagination(filter, req);
      expect(result).toBe(expected);
      expect(
        mockDailyReportService.getDailyReportGamePagination,
      ).toHaveBeenCalledWith(filter, req);
    });
    it('deve lançar erro se o serviço falhar', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        partners: 'p1',
        fromDate: '2024-01-01',
        toDate: '2024-01-31',
        gameId: 'g1',
        provider: 'prov1',
        accountType: 'real',
      };
      const req = {};
      mockDailyReportService.getDailyReportGamePagination.mockRejectedValueOnce(
        new Error('Erro'),
      );
      await expect(
        controller.getDailyReportGamePagination(filter, req),
      ).rejects.toThrow('Erro');
    });
  });

  describe('getDailyReportGame', () => {
    it('deve retornar o resultado do serviço', async () => {
      const fromDate = '2024-01-01';
      const toDate = '2024-01-31';
      const req = {};
      const expected = [{ id: 1 }];
      mockDailyReportService.getDailyReportGame.mockResolvedValue(expected);
      const result = await controller.getDailyReportGame(fromDate, toDate, req);
      expect(result).toBe(expected);
      expect(mockDailyReportService.getDailyReportGame).toHaveBeenCalledWith(
        fromDate,
        toDate,
        req,
      );
    });
  });

  describe('getDailyReportPlayerById', () => {
    it('deve retornar o resultado do serviço', async () => {
      const playerId = 'player1';
      const req = {};
      const expected = { id: 'player1' };
      mockDailyReportService.getDailyReportPlayerById.mockResolvedValue(
        expected,
      );
      const result = await controller.getDailyReportPlayerById(playerId, req);
      expect(result).toBe(expected);
      expect(
        mockDailyReportService.getDailyReportPlayerById,
      ).toHaveBeenCalledWith(playerId, req);
    });
  });
});
