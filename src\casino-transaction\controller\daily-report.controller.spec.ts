import { Test, TestingModule } from '@nestjs/testing';
import { DailyReportController } from './daily-report.controller';
import { DailyReportService } from '../services/daily-report.service';
import { BackofficeGuard } from '@/common/guards/backoffice/backoffice.guard';
import { JwtService } from '@nestjs/jwt';

describe('DailyReportController', () => {
  let controller: DailyReportController;

  const mockDailyReportService = {};
  const mockBackofficeGuard = { canActivate: jest.fn(() => true) };
  const mockJwtService = {};

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DailyReportController],
      providers: [
        { provide: DailyReportService, useValue: mockDailyReportService },
        { provide: JwtService, useValue: mockJwtService },
      ],
    })
      .overrideGuard(BackofficeGuard)
      .useValue(mockBackofficeGuard)
      .compile();

    controller = module.get<DailyReportController>(DailyReportController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
}); 