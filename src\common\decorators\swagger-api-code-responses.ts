import { applyDecorators, Type } from '@nestjs/common';
import { ApiExtraModels, ApiResponse, getSchemaPath } from '@nestjs/swagger';
import { MessageCodes } from '../constants/message-codes';

/**
 * Gera decorators do Swagger para os erros informados.
 * @param errorKeys Array de chaves das constantes de erro que você quer documentar no endpoint
 */
export function SwaggerApiCodeResponses(
  status = 400,
  messageKeys: number[],
  model?: Type<any>,
  successExample?: object,
) {
  const examples = messageKeys.reduce(
    (example, key) => {
      const keyName = Object.keys(MessageCodes).find(
        k => MessageCodes[k].code === key,
      );
      if (!keyName) {
        throw new Error(`Código ${key} não encontrado em MessageCodes`);
      }
      const addditionalInfo =
        key === MessageCodes.SUCCESS.code
          ? successExample
          : { errorMsg: MessageCodes[keyName].message };
      example[keyName] = {
        value: {
          errorCode: MessageCodes[keyName].code,
          ...addditionalInfo,
        },
      };
      return example;
    },
    {} as Record<string, { value: any }>,
  );

  return applyDecorators(
    ...(model ? [ApiExtraModels(model)] : []),
    ApiResponse({
      status,
      description: 'Possíveis respostas',
      content: {
        'application/json': {
          schema: model
            ? {
                allOf: [
                  { $ref: getSchemaPath(model) },
                  {
                    type: 'object',
                    properties: {
                      errorCode: { type: 'number', example: 0 },
                      errorMsg: {
                        type: 'string',
                        example: 'Mensagem de erro ou sucesso',
                      },
                    },
                  },
                ],
              }
            : {
                type: 'object',
                properties: {
                  errorCode: { type: 'number' },
                  errorMsg: { type: 'string' },
                },
              },
          examples,
        },
      },
    }),
  );
}
