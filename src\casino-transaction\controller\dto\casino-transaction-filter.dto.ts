import { GenericFilter } from '@/common/filters/generic-filter.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class CasinoTransactionFilterDto extends GenericFilter {
  @ApiPropertyOptional({
    type: String,
    description: 'Cancel transaction',
    example: true,
  })
  @IsString()
  cancelTransaction?: string;

  @ApiPropertyOptional({
    type: String,
    description: 'session id',
    example: '207d45ae-898f-412f-8698-bb64c08b5a3d',
  })
  @IsString()
  sessionId?: string;

  @ApiPropertyOptional({
    type: String,
    description: 'player id',
    example: '207d45ae-898f-412f-8698-bb64c08b5a3d',
  })
  @IsString()
  playerId?: string;

  @ApiPropertyOptional({
    type: String,
    description: 'from date',
    example: '2022-01-01',
  })
  @IsString()
  fromDate?: string;

  @ApiPropertyOptional({
    type: String,
    description: 'to date',
    example: '2022-01-01',
  })
  @IsString()
  toDate?: string;

  @ApiPropertyOptional({
    type: String,
    description: 'game id',
    example: '-8000',
  })
  @IsString()
  gameId?: string;

  @ApiPropertyOptional({
    type: String,
    description: 'win or loose',
    example: true,
  })
  @IsString()
  isWin?: string;

  @ApiPropertyOptional({
    type: String,
  })
  @IsString()
  transactionId?: string;

  @ApiPropertyOptional({
    type: String,
  })
  @IsString()
  betId?: string;
}
