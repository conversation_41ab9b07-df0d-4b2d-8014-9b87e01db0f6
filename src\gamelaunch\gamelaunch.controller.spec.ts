import { Test, TestingModule } from '@nestjs/testing';
import { GameLaunchController } from './gamelaunch.controller';
import { GameLaunchService } from './gamelaunch.service';
import { JwtAuthPlayerGuard } from '@/common/guards/player/jwt-auth-player.guard';
import { CapabilitiesGuard } from '@/common/guards/capabilities/capabilities.guard';
import { Logger } from '@nestjs/common';
import { CreateLauncherDto } from './dto/game-launch.dto';
import { GameModeEnum } from './enum/game-mode.enum';

describe('GameLaunchController', () => {
  let controller: GameLaunchController;
  let gameLaunchService: jest.Mocked<GameLaunchService>;

  const mockGameLaunchService = {
    getRealPlayURL: jest.fn(),
    getFreeUrl: jest.fn(),
  };

  const mockJwtAuthPlayerGuard = { canActivate: jest.fn(() => true) };
  const mockCapabilitiesGuard = { canActivate: jest.fn(() => true) };

  const mockRequest = {
    headers: {
      authorization: 'Bearer test-token',
    },
  } as any;

  const mockSuccessResponse = {
    launchUrl: 'https://test-launch-url.com',
  };

  const mockErrorResponse = {
    launchUrl: undefined,
  };

  const mockCreateLauncherDto: CreateLauncherDto = {
    gameMode: GameModeEnum.REAL_PLAY,
    game: 'test-game-uuid',
    locale: 'pt',
    ip: '***********',
    clientType: 'desktop',
    urls: {
      returnUrl: 'https://example.com/return',
      depositUrl: 'https://example.com/deposit',
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GameLaunchController],
      providers: [
        { provide: GameLaunchService, useValue: mockGameLaunchService },
      ],
    })
      .overrideGuard(JwtAuthPlayerGuard)
      .useValue(mockJwtAuthPlayerGuard)
      .overrideGuard(CapabilitiesGuard)
      .useValue(mockCapabilitiesGuard)
      .compile();

    controller = module.get<GameLaunchController>(GameLaunchController);
    gameLaunchService = module.get(GameLaunchService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('play - Real Play Mode', () => {
    const realPlayDTO: CreateLauncherDto = {
      ...mockCreateLauncherDto,
      gameMode: GameModeEnum.REAL_PLAY,
    };

    beforeEach(() => {
      gameLaunchService.getRealPlayURL.mockResolvedValue(mockSuccessResponse);
    });

    it('should successfully launch real_play game', async () => {
      // Act
      const result = await controller.play(realPlayDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getRealPlayURL).toHaveBeenCalledWith(
        realPlayDTO,
        mockRequest,
      );
      expect(gameLaunchService.getFreeUrl).not.toHaveBeenCalled();
    });

    it('should handle real_play game launch with different game ID', async () => {
      // Arrange
      const differentGameDTO: CreateLauncherDto = {
        ...mockCreateLauncherDto,
        game: 'different-game-uuid',
        gameMode: GameModeEnum.REAL_PLAY,
      };

      // Act
      const result = await controller.play(differentGameDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getRealPlayURL).toHaveBeenCalledWith(
        differentGameDTO,
        mockRequest,
      );
    });

    it('should handle service error response for real_play', async () => {
      // Arrange
      gameLaunchService.getRealPlayURL.mockResolvedValue(mockErrorResponse);

      // Act
      const result = await controller.play(realPlayDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockErrorResponse);
      expect(gameLaunchService.getRealPlayURL).toHaveBeenCalledWith(
        realPlayDTO,
        mockRequest,
      );
    });

    it('should handle service exception for real_play', async () => {
      // Arrange
      const error = new Error('Service unavailable');
      gameLaunchService.getRealPlayURL.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.play(realPlayDTO, mockRequest)).rejects.toThrow(
        'Service unavailable',
      );
      expect(gameLaunchService.getRealPlayURL).toHaveBeenCalledWith(
        realPlayDTO,
        mockRequest,
      );
    });

    it('should log real_play game launch request', async () => {
      // Arrange
      const loggerSpy = jest
        .spyOn(Logger.prototype, 'log')
        .mockImplementation();

      // Act
      await controller.play(realPlayDTO, mockRequest);

      // Assert
      expect(loggerSpy).toHaveBeenCalledWith(
        `Game launch request real_play: ${JSON.stringify(realPlayDTO)}`,
      );

      loggerSpy.mockRestore();
    });

    it('should handle request without authorization header', async () => {
      // Arrange
      const requestWithoutAuth = { headers: {} } as any;

      // Act
      const result = await controller.play(realPlayDTO, requestWithoutAuth);

      // Assert
      expect(result).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getRealPlayURL).toHaveBeenCalledWith(
        realPlayDTO,
        requestWithoutAuth,
      );
    });
  });

  describe('play - Free Play Mode', () => {
    const freePlayDTO: CreateLauncherDto = {
      ...mockCreateLauncherDto,
      gameMode: GameModeEnum.FREE_PLAY,
    };

    const mockFreePlayResponse = {
      launchUrl: 'https://free_play-url.com',
    };

    beforeEach(() => {
      gameLaunchService.getFreeUrl.mockResolvedValue(mockFreePlayResponse);
    });

    it('should successfully launch free_play game', async () => {
      // Act
      const result = await controller.play(freePlayDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockFreePlayResponse);
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(
        freePlayDTO,
        mockRequest,
      );
      expect(gameLaunchService.getRealPlayURL).not.toHaveBeenCalled();
    });

    it('should handle free_play game launch with different game ID', async () => {
      // Arrange
      const differentFreePlayDTO: CreateLauncherDto = {
        ...mockCreateLauncherDto,
        game: 'different-free-game-id',
        gameMode: GameModeEnum.FREE_PLAY,
      };

      // Act
      const result = await controller.play(differentFreePlayDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockFreePlayResponse);
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(
        differentFreePlayDTO,
        mockRequest,
      );
    });

    it('should handle service error response for free_play', async () => {
      // Arrange
      const freePlayErrorResponse = {
        launchUrl: undefined,
      };
      gameLaunchService.getFreeUrl.mockResolvedValue(freePlayErrorResponse);

      // Act
      const result = await controller.play(freePlayDTO, mockRequest);

      // Assert
      expect(result).toEqual(freePlayErrorResponse);
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(
        freePlayDTO,
        mockRequest,
      );
    });

    it('should handle service exception for free_play', async () => {
      // Arrange
      const error = new Error('Free_play service unavailable');
      gameLaunchService.getFreeUrl.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.play(freePlayDTO, mockRequest)).rejects.toThrow(
        'Free_play service unavailable',
      );
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(
        freePlayDTO,
        mockRequest,
      );
    });

    it('should log free_play game launch request', async () => {
      // Arrange
      const loggerSpy = jest
        .spyOn(Logger.prototype, 'log')
        .mockImplementation();

      // Act
      await controller.play(freePlayDTO, mockRequest);

      // Assert
      expect(loggerSpy).toHaveBeenCalledWith(
        `Game launch request free_play: ${JSON.stringify(freePlayDTO)}`,
      );

      loggerSpy.mockRestore();
    });

    it('should not require authorization header for free_play', async () => {
      // Arrange
      const requestWithoutAuth = { headers: {} } as any;

      // Act
      const result = await controller.play(freePlayDTO, requestWithoutAuth);

      // Assert
      expect(result).toEqual(mockFreePlayResponse);
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(
        freePlayDTO,
        requestWithoutAuth,
      );
      // Authorization header should not be passed to getFreeUrl
    });
  });

  describe('play - Input Validation and Edge Cases', () => {
    it('should handle empty gameId', async () => {
      // Arrange
      const emptyGameIdDTO: CreateLauncherDto = {
        ...mockCreateLauncherDto,
        game: '',
        gameMode: GameModeEnum.REAL_PLAY,
      };
      gameLaunchService.getRealPlayURL.mockResolvedValue(mockSuccessResponse);

      // Act
      const result = await controller.play(emptyGameIdDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getRealPlayURL).toHaveBeenCalledWith(
        emptyGameIdDTO,
        mockRequest,
      );
    });

    it('should handle null gameId', async () => {
      // Arrange
      const nullGameIdDTO: CreateLauncherDto = {
        ...mockCreateLauncherDto,
        game: null as any,
        gameMode: GameModeEnum.FREE_PLAY,
      };
      gameLaunchService.getFreeUrl.mockResolvedValue(mockSuccessResponse);

      // Act
      const result = await controller.play(nullGameIdDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(
        nullGameIdDTO,
        mockRequest,
      );
    });

    it('should handle undefined gameId', async () => {
      // Arrange
      const undefinedGameIdDTO: CreateLauncherDto = {
        ...mockCreateLauncherDto,
        game: undefined,
        gameMode: GameModeEnum.REAL_PLAY,
      };
      gameLaunchService.getRealPlayURL.mockResolvedValue(mockSuccessResponse);

      // Act
      const result = await controller.play(undefinedGameIdDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getRealPlayURL).toHaveBeenCalledWith(
        undefinedGameIdDTO,
        mockRequest,
      );
    });

    it('should handle very long gameId', async () => {
      // Arrange
      const longGameId = 'a'.repeat(1000);
      const longGameIdDTO: CreateLauncherDto = {
        ...mockCreateLauncherDto,
        game: longGameId,
        gameMode: GameModeEnum.FREE_PLAY,
      };
      gameLaunchService.getFreeUrl.mockResolvedValue(mockSuccessResponse);

      // Act
      const result = await controller.play(longGameIdDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(
        longGameIdDTO,
        mockRequest,
      );
    });

    it('should handle special characters in gameId', async () => {
      // Arrange
      const specialGameIdDTO: CreateLauncherDto = {
        ...mockCreateLauncherDto,
        game: 'game-123!@#$%^&*()_+{}|:"<>?[]\\;\',./',
        gameMode: GameModeEnum.REAL_PLAY,
      };
      gameLaunchService.getRealPlayURL.mockResolvedValue(mockSuccessResponse);

      // Act
      const result = await controller.play(specialGameIdDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getRealPlayURL).toHaveBeenCalledWith(
        specialGameIdDTO,
        mockRequest,
      );
    });

    it('should handle malformed authorization header', async () => {
      // Arrange
      const malformedAuthRequest = {
        headers: {
          authorization: 'InvalidFormat token',
        },
      } as any;
      const realPlayDTO: CreateLauncherDto = {
        ...mockCreateLauncherDto,
        game: 'test-game-id',
        gameMode: GameModeEnum.REAL_PLAY,
      };
      gameLaunchService.getRealPlayURL.mockResolvedValue(mockSuccessResponse);

      // Act
      const result = await controller.play(realPlayDTO, malformedAuthRequest);

      // Assert
      expect(result).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getRealPlayURL).toHaveBeenCalledWith(
        realPlayDTO,
        malformedAuthRequest,
      );
    });
  });

  describe('play - Integration Tests', () => {
    it('should handle complete real_play workflow', async () => {
      // Arrange
      const realPlayDTO: CreateLauncherDto = {
        ...mockCreateLauncherDto,
        game: 'integration-game-123',
        gameMode: GameModeEnum.REAL_PLAY,
      };

      const integrationResponse = {
        errorCode: 0,
        launchToken: 'integration-token-123',
        launchUrl: 'https://integration-game-url.com/play',
      };

      gameLaunchService.getRealPlayURL.mockResolvedValue(integrationResponse);
      const loggerSpy = jest
        .spyOn(Logger.prototype, 'log')
        .mockImplementation();

      // Act
      const result = await controller.play(realPlayDTO, mockRequest);

      // Assert
      expect(result).toEqual(integrationResponse);
      expect(gameLaunchService.getRealPlayURL).toHaveBeenCalledWith(
        realPlayDTO,
        mockRequest,
      );
      expect(loggerSpy).toHaveBeenCalledWith(
        `Game launch request real_play: ${JSON.stringify(realPlayDTO)}`,
      );

      loggerSpy.mockRestore();
    });

    it('should handle complete free_play workflow', async () => {
      // Arrange
      const freePlayDTO: CreateLauncherDto = {
        ...mockCreateLauncherDto,
        game: 'integration-free-game-456',
        gameMode: GameModeEnum.FREE_PLAY,
      };

      const integrationFreeResponse = {
        errorCode: 0,
        launchToken: 'integration-free-token-456',
        launchUrl: 'https://integration-free-game-url.com/play',
      };

      gameLaunchService.getFreeUrl.mockResolvedValue(integrationFreeResponse);
      const loggerSpy = jest
        .spyOn(Logger.prototype, 'log')
        .mockImplementation();

      // Act
      const result = await controller.play(freePlayDTO, mockRequest);

      // Assert
      expect(result).toEqual(integrationFreeResponse);
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(
        freePlayDTO,
        mockRequest,
      );
      expect(loggerSpy).toHaveBeenCalledWith(
        `Game launch request free_play: ${JSON.stringify(freePlayDTO)}`,
      );

      loggerSpy.mockRestore();
    });

    it('should handle mixed mode requests in sequence', async () => {
      // Arrange
      const realPlayDTO: CreateLauncherDto = {
        ...mockCreateLauncherDto,
        game: 'sequence-real-game',
        gameMode: GameModeEnum.REAL_PLAY,
      };

      const freePlayDTO: CreateLauncherDto = {
        ...mockCreateLauncherDto,
        game: 'sequence-free-game',
        gameMode: GameModeEnum.FREE_PLAY,
      };

      const realPlayResponse = {
        errorCode: 0,
        launchToken: 'real-token',
        launchUrl: 'https://real-game-url.com',
      };

      const freePlayResponse = {
        errorCode: 0,
        launchToken: 'free-token',
        launchUrl: 'https://free-game-url.com',
      };

      gameLaunchService.getRealPlayURL.mockResolvedValue(realPlayResponse);
      gameLaunchService.getFreeUrl.mockResolvedValue(freePlayResponse);

      // Act
      const realResult = await controller.play(realPlayDTO, mockRequest);
      const freeResult = await controller.play(freePlayDTO, mockRequest);

      // Assert
      expect(realResult).toEqual(realPlayResponse);
      expect(freeResult).toEqual(freePlayResponse);
      expect(gameLaunchService.getRealPlayURL).toHaveBeenCalledWith(
        realPlayDTO,
        mockRequest,
      );
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(
        freePlayDTO,
        mockRequest,
      );
    });
  });

  describe('Guards and Security', () => {
    it('should have JwtAuthPlayerGuard configured', () => {
      // This test verifies the JwtAuthPlayerGuard is properly mocked and configured
      expect(mockJwtAuthPlayerGuard.canActivate).toBeDefined();
      expect(typeof mockJwtAuthPlayerGuard.canActivate).toBe('function');
    });

    it('should have CapabilitiesGuard configured', () => {
      // This test verifies the CapabilitiesGuard is properly mocked and configured
      expect(mockCapabilitiesGuard.canActivate).toBeDefined();
      expect(typeof mockCapabilitiesGuard.canActivate).toBe('function');
    });

    it('should have guards returning true by default', () => {
      // Verify that guards are configured to allow access in tests
      expect(mockJwtAuthPlayerGuard.canActivate()).toBe(true);
      expect(mockCapabilitiesGuard.canActivate()).toBe(true);
    });

    it('should be able to simulate guard rejection', () => {
      // Arrange
      mockJwtAuthPlayerGuard.canActivate.mockReturnValue(false);
      mockCapabilitiesGuard.canActivate.mockReturnValue(false);

      // Act & Assert
      expect(mockJwtAuthPlayerGuard.canActivate()).toBe(false);
      expect(mockCapabilitiesGuard.canActivate()).toBe(false);

      // Reset for other tests
      mockJwtAuthPlayerGuard.canActivate.mockReturnValue(true);
      mockCapabilitiesGuard.canActivate.mockReturnValue(true);
    });
  });

  describe('Error Handling and Logging', () => {
    it('should handle service returning undefined', async () => {
      // Arrange
      const realPlayDTO: CreateLauncherDto = {
        ...mockCreateLauncherDto,
        game: 'undefined-response-game',
        gameMode: GameModeEnum.REAL_PLAY,
      };
      gameLaunchService.getRealPlayURL.mockResolvedValue(undefined as any);

      // Act
      const result = await controller.play(realPlayDTO, mockRequest);

      // Assert
      expect(result).toBeUndefined();
      expect(gameLaunchService.getRealPlayURL).toHaveBeenCalledWith(
        realPlayDTO,
        mockRequest,
      );
    });

    it('should handle service returning null', async () => {
      // Arrange
      const freePlayDTO: CreateLauncherDto = {
        ...mockCreateLauncherDto,
        game: 'null-response-game',
        gameMode: GameModeEnum.FREE_PLAY,
      };
      gameLaunchService.getFreeUrl.mockResolvedValue(null as any);

      // Act
      const result = await controller.play(freePlayDTO, mockRequest);

      // Assert
      expect(result).toBeNull();
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(
        freePlayDTO,
        mockRequest,
      );
    });

    it('should handle concurrent requests', async () => {
      // Arrange
      const realPlayDTO: CreateLauncherDto = {
        ...mockCreateLauncherDto,
        game: 'concurrent-real-game',
        gameMode: GameModeEnum.REAL_PLAY,
      };

      const freePlayDTO: CreateLauncherDto = {
        ...mockCreateLauncherDto,
        game: 'concurrent-free-game',
        gameMode: GameModeEnum.FREE_PLAY,
      };

      gameLaunchService.getRealPlayURL.mockResolvedValue(mockSuccessResponse);
      gameLaunchService.getFreeUrl.mockResolvedValue(mockSuccessResponse);

      // Act
      const [realResult, freeResult] = await Promise.all([
        controller.play(realPlayDTO, mockRequest),
        controller.play(freePlayDTO, mockRequest),
      ]);

      // Assert
      expect(realResult).toEqual(mockSuccessResponse);
      expect(freeResult).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getRealPlayURL).toHaveBeenCalledWith(
        realPlayDTO,
        mockRequest,
      );
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(
        freePlayDTO,
        mockRequest,
      );
    });

    it('should handle timeout errors', async () => {
      // Arrange
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'TimeoutError';
      gameLaunchService.getRealPlayURL.mockRejectedValue(timeoutError);

      const realPlayDTO: CreateLauncherDto = {
        ...mockCreateLauncherDto,
        game: 'timeout-game',
        gameMode: GameModeEnum.REAL_PLAY,
      };

      // Act & Assert
      await expect(controller.play(realPlayDTO, mockRequest)).rejects.toThrow(
        'Request timeout',
      );
    });
  });
});
