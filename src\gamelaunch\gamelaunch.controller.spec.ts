import { Test, TestingModule } from '@nestjs/testing';
import { GameLaunchController } from './gamelaunch.controller';
import { GameLaunchService } from './gamelaunch.service';
import { JwtAuthPlayerGuard } from '@/common/guards/player/jwt-auth-player.guard';
import { CapabilitiesGuard } from '@/common/guards/capabilities/capabilities.guard';
import { Logger } from '@nestjs/common';
import { GameLaunchDTO } from './dto/game-launch.dto';
import { GameModeEnum } from './enum/game-mode.enum';

describe('GameLaunchController', () => {
  let controller: GameLaunchController;
  let gameLaunchService: jest.Mocked<GameLaunchService>;

  const mockGameLaunchService = {
    getURL: jest.fn(),
    getFreeUrl: jest.fn(),
  };

  const mockJwtAuthPlayerGuard = { canActivate: jest.fn(() => true) };
  const mockCapabilitiesGuard = { canActivate: jest.fn(() => true) };

  const mockRequest = {
    headers: {
      authorization: 'Bearer test-token',
    },
  } as any;

  const mockSuccessResponse = {
    errorCode: 0,
    launchToken: 'test-launch-token',
    launchUrl: 'https://test-launch-url.com',
  };

  const mockErrorResponse = {
    errorCode: 1001,
    errorMsg: 'Game not found',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GameLaunchController],
      providers: [
        { provide: GameLaunchService, useValue: mockGameLaunchService },
      ],
    })
      .overrideGuard(JwtAuthPlayerGuard)
      .useValue(mockJwtAuthPlayerGuard)
      .overrideGuard(CapabilitiesGuard)
      .useValue(mockCapabilitiesGuard)
      .compile();

    controller = module.get<GameLaunchController>(GameLaunchController);
    gameLaunchService = module.get(GameLaunchService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('play - Real Play Mode', () => {
    const realPlayDTO: GameLaunchDTO = {
      gameId: 'test-game-id',
      gameMode: GameModeEnum.REAL_PLAY,
    };

    beforeEach(() => {
      gameLaunchService.getURL.mockResolvedValue(mockSuccessResponse);
    });

    it('should successfully launch real-play game', async () => {
      // Act
      const result = await controller.play(realPlayDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getURL).toHaveBeenCalledWith(realPlayDTO, 'Bearer test-token');
      expect(gameLaunchService.getFreeUrl).not.toHaveBeenCalled();
    });

    it('should handle real-play game launch with different game ID', async () => {
      // Arrange
      const differentGameDTO: GameLaunchDTO = {
        gameId: 'different-game-id',
        gameMode: GameModeEnum.REAL_PLAY,
      };

      // Act
      const result = await controller.play(differentGameDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getURL).toHaveBeenCalledWith(differentGameDTO, 'Bearer test-token');
    });

    it('should handle service error response for real-play', async () => {
      // Arrange
      gameLaunchService.getURL.mockResolvedValue(mockErrorResponse);

      // Act
      const result = await controller.play(realPlayDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockErrorResponse);
      expect(gameLaunchService.getURL).toHaveBeenCalledWith(realPlayDTO, 'Bearer test-token');
    });

    it('should handle service exception for real-play', async () => {
      // Arrange
      const error = new Error('Service unavailable');
      gameLaunchService.getURL.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.play(realPlayDTO, mockRequest)).rejects.toThrow('Service unavailable');
      expect(gameLaunchService.getURL).toHaveBeenCalledWith(realPlayDTO, 'Bearer test-token');
    });

    it('should log real-play game launch request', async () => {
      // Arrange
      const loggerSpy = jest.spyOn(Logger.prototype, 'log').mockImplementation();

      // Act
      await controller.play(realPlayDTO, mockRequest);

      // Assert
      expect(loggerSpy).toHaveBeenCalledWith(
        `Game launch request real-play: ${JSON.stringify(realPlayDTO)}`
      );

      loggerSpy.mockRestore();
    });

    it('should handle request without authorization header', async () => {
      // Arrange
      const requestWithoutAuth = { headers: {} } as any;

      // Act
      const result = await controller.play(realPlayDTO, requestWithoutAuth);

      // Assert
      expect(result).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getURL).toHaveBeenCalledWith(realPlayDTO, undefined);
    });
  });

  describe('play - Free Play Mode', () => {
    const freePlayDTO: GameLaunchDTO = {
      gameId: 'test-game-id',
      gameMode: GameModeEnum.FREE_PLAY,
    };

    const mockFreePlayResponse = {
      errorCode: 0,
      launchToken: 'free-play-token',
      launchUrl: 'https://free-play-url.com',
    };

    beforeEach(() => {
      gameLaunchService.getFreeUrl.mockResolvedValue(mockFreePlayResponse);
    });

    it('should successfully launch free-play game', async () => {
      // Act
      const result = await controller.play(freePlayDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockFreePlayResponse);
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(freePlayDTO);
      expect(gameLaunchService.getURL).not.toHaveBeenCalled();
    });

    it('should handle free-play game launch with different game ID', async () => {
      // Arrange
      const differentFreePlayDTO: GameLaunchDTO = {
        gameId: 'different-free-game-id',
        gameMode: GameModeEnum.FREE_PLAY,
      };

      // Act
      const result = await controller.play(differentFreePlayDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockFreePlayResponse);
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(differentFreePlayDTO);
    });

    it('should handle service error response for free-play', async () => {
      // Arrange
      const freePlayErrorResponse = {
        errorCode: 2001,
        errorMsg: 'Free-play not available',
      };
      gameLaunchService.getFreeUrl.mockResolvedValue(freePlayErrorResponse);

      // Act
      const result = await controller.play(freePlayDTO, mockRequest);

      // Assert
      expect(result).toEqual(freePlayErrorResponse);
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(freePlayDTO);
    });

    it('should handle service exception for free-play', async () => {
      // Arrange
      const error = new Error('Free-play service unavailable');
      gameLaunchService.getFreeUrl.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.play(freePlayDTO, mockRequest)).rejects.toThrow('Free-play service unavailable');
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(freePlayDTO);
    });

    it('should log free-play game launch request', async () => {
      // Arrange
      const loggerSpy = jest.spyOn(Logger.prototype, 'log').mockImplementation();

      // Act
      await controller.play(freePlayDTO, mockRequest);

      // Assert
      expect(loggerSpy).toHaveBeenCalledWith(
        `Game launch request free-play: ${JSON.stringify(freePlayDTO)}`
      );

      loggerSpy.mockRestore();
    });

    it('should not require authorization header for free-play', async () => {
      // Arrange
      const requestWithoutAuth = { headers: {} } as any;

      // Act
      const result = await controller.play(freePlayDTO, requestWithoutAuth);

      // Assert
      expect(result).toEqual(mockFreePlayResponse);
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(freePlayDTO);
      // Authorization header should not be passed to getFreeUrl
    });
  });

  describe('play - Input Validation and Edge Cases', () => {
    it('should handle empty gameId', async () => {
      // Arrange
      const emptyGameIdDTO: GameLaunchDTO = {
        gameId: '',
        gameMode: GameModeEnum.REAL_PLAY,
      };
      gameLaunchService.getURL.mockResolvedValue(mockSuccessResponse);

      // Act
      const result = await controller.play(emptyGameIdDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getURL).toHaveBeenCalledWith(emptyGameIdDTO, 'Bearer test-token');
    });

    it('should handle null gameId', async () => {
      // Arrange
      const nullGameIdDTO: GameLaunchDTO = {
        gameId: null as any,
        gameMode: GameModeEnum.FREE_PLAY,
      };
      gameLaunchService.getFreeUrl.mockResolvedValue(mockSuccessResponse);

      // Act
      const result = await controller.play(nullGameIdDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(nullGameIdDTO);
    });

    it('should handle undefined gameId', async () => {
      // Arrange
      const undefinedGameIdDTO: GameLaunchDTO = {
        gameId: undefined,
        gameMode: GameModeEnum.REAL_PLAY,
      };
      gameLaunchService.getURL.mockResolvedValue(mockSuccessResponse);

      // Act
      const result = await controller.play(undefinedGameIdDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getURL).toHaveBeenCalledWith(undefinedGameIdDTO, 'Bearer test-token');
    });

    it('should handle very long gameId', async () => {
      // Arrange
      const longGameId = 'a'.repeat(1000);
      const longGameIdDTO: GameLaunchDTO = {
        gameId: longGameId,
        gameMode: GameModeEnum.FREE_PLAY,
      };
      gameLaunchService.getFreeUrl.mockResolvedValue(mockSuccessResponse);

      // Act
      const result = await controller.play(longGameIdDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(longGameIdDTO);
    });

    it('should handle special characters in gameId', async () => {
      // Arrange
      const specialGameIdDTO: GameLaunchDTO = {
        gameId: 'game-123!@#$%^&*()_+{}|:"<>?[]\\;\',./',
        gameMode: GameModeEnum.REAL_PLAY,
      };
      gameLaunchService.getURL.mockResolvedValue(mockSuccessResponse);

      // Act
      const result = await controller.play(specialGameIdDTO, mockRequest);

      // Assert
      expect(result).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getURL).toHaveBeenCalledWith(specialGameIdDTO, 'Bearer test-token');
    });

    it('should handle malformed authorization header', async () => {
      // Arrange
      const malformedAuthRequest = {
        headers: {
          authorization: 'InvalidFormat token',
        },
      } as any;
      const realPlayDTO: GameLaunchDTO = {
        gameId: 'test-game-id',
        gameMode: GameModeEnum.REAL_PLAY,
      };
      gameLaunchService.getURL.mockResolvedValue(mockSuccessResponse);

      // Act
      const result = await controller.play(realPlayDTO, malformedAuthRequest);

      // Assert
      expect(result).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getURL).toHaveBeenCalledWith(realPlayDTO, 'InvalidFormat token');
    });
  });

  describe('play - Integration Tests', () => {
    it('should handle complete real-play workflow', async () => {
      // Arrange
      const realPlayDTO: GameLaunchDTO = {
        gameId: 'integration-game-123',
        gameMode: GameModeEnum.REAL_PLAY,
      };

      const integrationResponse = {
        errorCode: 0,
        launchToken: 'integration-token-123',
        launchUrl: 'https://integration-game-url.com/play',
      };

      gameLaunchService.getURL.mockResolvedValue(integrationResponse);
      const loggerSpy = jest.spyOn(Logger.prototype, 'log').mockImplementation();

      // Act
      const result = await controller.play(realPlayDTO, mockRequest);

      // Assert
      expect(result).toEqual(integrationResponse);
      expect(gameLaunchService.getURL).toHaveBeenCalledWith(realPlayDTO, 'Bearer test-token');
      expect(loggerSpy).toHaveBeenCalledWith(
        `Game launch request real-play: ${JSON.stringify(realPlayDTO)}`
      );

      loggerSpy.mockRestore();
    });

    it('should handle complete free-play workflow', async () => {
      // Arrange
      const freePlayDTO: GameLaunchDTO = {
        gameId: 'integration-free-game-456',
        gameMode: GameModeEnum.FREE_PLAY,
      };

      const integrationFreeResponse = {
        errorCode: 0,
        launchToken: 'integration-free-token-456',
        launchUrl: 'https://integration-free-game-url.com/play',
      };

      gameLaunchService.getFreeUrl.mockResolvedValue(integrationFreeResponse);
      const loggerSpy = jest.spyOn(Logger.prototype, 'log').mockImplementation();

      // Act
      const result = await controller.play(freePlayDTO, mockRequest);

      // Assert
      expect(result).toEqual(integrationFreeResponse);
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(freePlayDTO);
      expect(loggerSpy).toHaveBeenCalledWith(
        `Game launch request free-play: ${JSON.stringify(freePlayDTO)}`
      );

      loggerSpy.mockRestore();
    });

    it('should handle mixed mode requests in sequence', async () => {
      // Arrange
      const realPlayDTO: GameLaunchDTO = {
        gameId: 'sequence-real-game',
        gameMode: GameModeEnum.REAL_PLAY,
      };

      const freePlayDTO: GameLaunchDTO = {
        gameId: 'sequence-free-game',
        gameMode: GameModeEnum.FREE_PLAY,
      };

      const realPlayResponse = {
        errorCode: 0,
        launchToken: 'real-token',
        launchUrl: 'https://real-game-url.com',
      };

      const freePlayResponse = {
        errorCode: 0,
        launchToken: 'free-token',
        launchUrl: 'https://free-game-url.com',
      };

      gameLaunchService.getURL.mockResolvedValue(realPlayResponse);
      gameLaunchService.getFreeUrl.mockResolvedValue(freePlayResponse);

      // Act
      const realResult = await controller.play(realPlayDTO, mockRequest);
      const freeResult = await controller.play(freePlayDTO, mockRequest);

      // Assert
      expect(realResult).toEqual(realPlayResponse);
      expect(freeResult).toEqual(freePlayResponse);
      expect(gameLaunchService.getURL).toHaveBeenCalledWith(realPlayDTO, 'Bearer test-token');
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(freePlayDTO);
    });
  });

  describe('Guards and Security', () => {
    it('should have JwtAuthPlayerGuard configured', () => {
      // This test verifies the JwtAuthPlayerGuard is properly mocked and configured
      expect(mockJwtAuthPlayerGuard.canActivate).toBeDefined();
      expect(typeof mockJwtAuthPlayerGuard.canActivate).toBe('function');
    });

    it('should have CapabilitiesGuard configured', () => {
      // This test verifies the CapabilitiesGuard is properly mocked and configured
      expect(mockCapabilitiesGuard.canActivate).toBeDefined();
      expect(typeof mockCapabilitiesGuard.canActivate).toBe('function');
    });

    it('should have guards returning true by default', () => {
      // Verify that guards are configured to allow access in tests
      expect(mockJwtAuthPlayerGuard.canActivate()).toBe(true);
      expect(mockCapabilitiesGuard.canActivate()).toBe(true);
    });

    it('should be able to simulate guard rejection', () => {
      // Arrange
      mockJwtAuthPlayerGuard.canActivate.mockReturnValue(false);
      mockCapabilitiesGuard.canActivate.mockReturnValue(false);

      // Act & Assert
      expect(mockJwtAuthPlayerGuard.canActivate()).toBe(false);
      expect(mockCapabilitiesGuard.canActivate()).toBe(false);

      // Reset for other tests
      mockJwtAuthPlayerGuard.canActivate.mockReturnValue(true);
      mockCapabilitiesGuard.canActivate.mockReturnValue(true);
    });
  });

  describe('Error Handling and Logging', () => {
    it('should handle service returning undefined', async () => {
      // Arrange
      const realPlayDTO: GameLaunchDTO = {
        gameId: 'undefined-response-game',
        gameMode: GameModeEnum.REAL_PLAY,
      };
      gameLaunchService.getURL.mockResolvedValue(undefined as any);

      // Act
      const result = await controller.play(realPlayDTO, mockRequest);

      // Assert
      expect(result).toBeUndefined();
      expect(gameLaunchService.getURL).toHaveBeenCalledWith(realPlayDTO, 'Bearer test-token');
    });

    it('should handle service returning null', async () => {
      // Arrange
      const freePlayDTO: GameLaunchDTO = {
        gameId: 'null-response-game',
        gameMode: GameModeEnum.FREE_PLAY,
      };
      gameLaunchService.getFreeUrl.mockResolvedValue(null as any);

      // Act
      const result = await controller.play(freePlayDTO, mockRequest);

      // Assert
      expect(result).toBeNull();
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(freePlayDTO);
    });

    it('should handle concurrent requests', async () => {
      // Arrange
      const realPlayDTO: GameLaunchDTO = {
        gameId: 'concurrent-real-game',
        gameMode: GameModeEnum.REAL_PLAY,
      };

      const freePlayDTO: GameLaunchDTO = {
        gameId: 'concurrent-free-game',
        gameMode: GameModeEnum.FREE_PLAY,
      };

      gameLaunchService.getURL.mockResolvedValue(mockSuccessResponse);
      gameLaunchService.getFreeUrl.mockResolvedValue(mockSuccessResponse);

      // Act
      const [realResult, freeResult] = await Promise.all([
        controller.play(realPlayDTO, mockRequest),
        controller.play(freePlayDTO, mockRequest),
      ]);

      // Assert
      expect(realResult).toEqual(mockSuccessResponse);
      expect(freeResult).toEqual(mockSuccessResponse);
      expect(gameLaunchService.getURL).toHaveBeenCalledWith(realPlayDTO, 'Bearer test-token');
      expect(gameLaunchService.getFreeUrl).toHaveBeenCalledWith(freePlayDTO);
    });

    it('should handle timeout errors', async () => {
      // Arrange
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'TimeoutError';
      gameLaunchService.getURL.mockRejectedValue(timeoutError);

      const realPlayDTO: GameLaunchDTO = {
        gameId: 'timeout-game',
        gameMode: GameModeEnum.REAL_PLAY,
      };

      // Act & Assert
      await expect(controller.play(realPlayDTO, mockRequest)).rejects.toThrow('Request timeout');
    });
  });
});