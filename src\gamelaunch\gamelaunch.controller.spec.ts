import { Test, TestingModule } from '@nestjs/testing';
import { GameLaunchController } from './gamelaunch.controller';
import { GameLaunchService } from './gamelaunch.service';
import { JwtAuthPlayerGuard } from '@/common/guards/player/jwt-auth-player.guard';

describe('GameLaunchController', () => {
  let controller: GameLaunchController;

  const mockGameLaunchService = {};
  const mockJwtAuthPlayerGuard = { canActivate: jest.fn(() => true) };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GameLaunchController],
      providers: [
        { provide: GameLaunchService, useValue: mockGameLaunchService },
      ],
    })
      .overrideGuard(JwtAuthPlayerGuard)
      .useValue(mockJwtAuthPlayerGuard)
      .compile();

    controller = module.get<GameLaunchController>(GameLaunchController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
}); 