import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { INestApplication } from '@nestjs/common';
import { addGlobalResponses } from './swagger-response-global';
import { getApiExtraModels } from './swagger-config-extra-model';

export const addSwagger = (app: INestApplication) => {
  const config = new DocumentBuilder()
    .setTitle('PAM Casino API')
    .setDescription(
      `
      API para gerenciamento de operações de casino.
      
      Esta API fornece endpoints para:
      - Autenticação e gerenciamento de sessão
      - Consulta e atualização de saldo
      - Realização e cancelamento de apostas
      - Gerenciamento de jogos de casino
      - Relatórios e transações
      
      A API utiliza dois mecanismos de autenticação:
      1. HMAC (Hash-based Message Authentication Code)
         - Requerido para a maioria dos endpoints
         - O HMAC deve ser enviado no header 'x-checksum'
         - O HMAC é calculado usando SHA-256 sobre o corpo da requisição
      
      2. JWT (JSON Web Token)
         - Utilizado em endpoints específicos
         - Deve ser enviado no header 'Authorization' como Bearer token
    `,
    )
    .setVersion('1.0')
    .addTag('Authentication', 'Endpoints de autenticação e sessão')
    .addTag('Balance', 'Endpoints de consulta e atualização de saldo')
    .addTag('Bet', 'Endpoints de apostas')
    .addTag('Betsettle', 'Endpoints de liquidação de apostas')
    .addTag('CancelBet', 'Endpoints de cancelamento de apostas')
    .addTag('CasinoGames', 'Endpoints de gerenciamento de jogos')
    .addTag('CasinoTransaction', 'Endpoints de transações')
    .addTag('DailyReport', 'Endpoints de relatórios')
    .addTag('GameLaunch', 'Endpoints de inicialização de jogos')
    .addTag('Health', 'Endpoints de monitoramento da API')
    .addTag('HistoryGame', 'Endpoints de histórico de jogos')
    .addTag('NewSession', 'Endpoints de gerenciamento de sessão')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'access-token',
    )
    .addApiKey(
      {
        type: 'apiKey',
        name: 'x-checksum',
        in: 'header',
        description: 'HMAC SHA-256 do corpo da requisição',
      },
      'hmac',
    )
    .build();

  const document = SwaggerModule.createDocument(app, config, {
    extraModels: getApiExtraModels(),
  });

  addGlobalResponses(document);

  SwaggerModule.setup('/v1/pam/casino/doc', app, document);
};
