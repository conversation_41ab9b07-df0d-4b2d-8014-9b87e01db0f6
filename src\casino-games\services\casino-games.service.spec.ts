import { Test, TestingModule } from '@nestjs/testing';
import { CasinoGamesService } from './casino-games.service';
import { EntityManager } from 'typeorm';
import { HttpException } from '@nestjs/common';
import { CasinoGames } from '../entities/casino-game.entity';

describe('CasinoGamesService', () => {
  let service: CasinoGamesService;
  let casinoManager: any;

  beforeEach(async () => {
    casinoManager = {
      count: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
    };
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CasinoGamesService,
        { provide: EntityManager, useValue: casinoManager },
      ],
    }).compile();

    service = module.get<CasinoGamesService>(CasinoGamesService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    const reqMock = { headers: { 'partner-id': 'partner1' } };
    it('deve retornar lista paginada de jogos', async () => {
      casinoManager.count.mockResolvedValue(2);
      casinoManager.find.mockResolvedValue([
        { id: '1', name: 'A' },
        { id: '2', name: 'B' },
      ]);
      const filter = { page: 1, pageSize: 2, sortOrder: 'ASC' };
      const result = await service.findAll(filter, reqMock);
      expect(result.data.length).toBe(2);
      expect(result.totalItems).toBe(2);
      expect(result.totalPages).toBe(1);
      expect(result.currentPage).toBe(1);
      expect(result.pageSize).toBe(2);
    });
    it('deve lançar exceção se não encontrar jogos', async () => {
      casinoManager.count.mockResolvedValue(0);
      casinoManager.find.mockResolvedValue([]);
      const filter = { page: 1, pageSize: 2, sortOrder: 'ASC' };
      await expect(service.findAll(filter, reqMock)).rejects.toThrow(HttpException);
    });
    it('deve lançar exceção em erro inesperado', async () => {
      casinoManager.count.mockRejectedValue(new HttpException('erro', 400));
      const filter = { page: 1, pageSize: 2, sortOrder: 'ASC' };
      await expect(service.findAll(filter, reqMock)).rejects.toThrow(HttpException);
    });
  });

  describe('findOne', () => {
    it('deve retornar um jogo pelo id', async () => {
      casinoManager.findOne.mockResolvedValue({ id: '1', name: 'Jogo' });
      const result = await service.findOne('1');
      expect(result).toEqual({ id: '1', name: 'Jogo' });
      expect(casinoManager.findOne).toHaveBeenCalledWith(CasinoGames, { where: { id: '1' } });
    });
    it('deve lançar exceção se não encontrar', async () => {
      casinoManager.findOne.mockResolvedValue(undefined);
      await expect(service.findOne('2')).rejects.toThrow(HttpException);
    });
    it('deve lançar exceção em erro inesperado', async () => {
      casinoManager.findOne.mockRejectedValue(new HttpException('erro', 400));
      await expect(service.findOne('3')).rejects.toThrow(HttpException);
    });
  });
}); 