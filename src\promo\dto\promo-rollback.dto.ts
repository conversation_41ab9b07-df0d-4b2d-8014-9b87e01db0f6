import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsUUID } from 'class-validator';

export class PromoRollbackDto {
  @ApiProperty({ example: 'EUR' })
  @IsString()
  currency: string;

  @ApiProperty({ example: 'e9888ed7-9e98-4463-be23-35b74295eb79' })
  @IsUUID()
  id: string;

  @ApiProperty({ example: 'c7d445b8-0ba9-487b-a3d7-295e3b23a2ed' })
  @IsUUID()
  original_id: string;

  @ApiProperty({ example: 'd8d4d489-eb54-468c-85b1-df96fbb9b14a' })
  @IsUUID()
  player_id: string;
}
