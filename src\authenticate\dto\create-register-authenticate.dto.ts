import { IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateRegisterAuthenticateDto {
  @ApiProperty({
    description: 'Token de autenticação',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    required: true
  })
  @IsString()
  token: string;

  @ApiProperty({
    description: 'ID do jogador',
    example: 'player123',
    required: true
  })
  @IsString()
  playerId: string;

  @ApiProperty({
    description: 'ID da sessão',
    example: 'session456',
    required: true
  })
  @IsString()
  sessionId: string;
}
