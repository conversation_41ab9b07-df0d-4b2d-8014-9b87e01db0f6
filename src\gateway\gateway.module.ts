import { Modu<PERSON> } from '@nestjs/common';
import { GatewayController } from './controllers/gateway.controller';
import { GatewayService } from './services/gateway.service';
import { AdapterFactoryService } from './services/adapter-factory.service';
import { CasinoIntegrationService } from './services/casino-integration.service';
import { EgtAdapter } from './adapters/egt-adapter';
import { BgamingAdapter } from './adapters/bgaming-adapter';

@Module({
  controllers: [GatewayController],
  providers: [
    GatewayService,
    AdapterFactoryService,
    CasinoIntegrationService,
    EgtAdapter,
    BgamingAdapter,
  ],
  exports: [
    GatewayService,
    AdapterFactoryService,
    CasinoIntegrationService,
  ],
})
export class GatewayModule {}
