import { GenericFilter } from '@/common/filters/generic-filter.dto';
import { DateUtilsService } from '@/common/utils/date';
import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { CasinoTransactionFilterDto } from '../controller/dto/casino-transaction-filter.dto';
import { CasinoTransactionEntity } from '../controller/entities/casino-transactions.entity';
import { SortOrder } from '@/common/filters/sortOrder';
import { CasinoGames } from '@/casino-games/entities/casino-game.entity';

@Injectable()
export class CasinoTransactionService {
  private readonly logger = new Logger(CasinoTransactionService.name);
  constructor(
    @InjectEntityManager() private readonly manager: EntityManager,
    private readonly dateService: DateUtilsService
  ) {}

  async findAll(
    filter: GenericFilter & CasinoTransactionFilterDto,
    req: Request
  ): Promise<{
    data: CasinoTransactionEntity[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    try {
      this.logger.log(
        `[Inicio] Find all casino transactions: ${JSON.stringify(filter)}`
      );
      if (!filter.sortOrder) {
        filter.sortOrder = SortOrder.ASC;
      }
      // console.log(req);
      const partnerId = req.headers['partner-id'] as string;
      const page = !filter.page || filter.page < 1 ? 1 : filter.page;
      const pageSize =
        !filter.pageSize || filter.pageSize < 1 ? 10 : filter.pageSize;
      if (!filter.playerId) filter.playerId = '';

      const fromDate = filter.fromDate ? new Date(filter.fromDate) : new Date();
      const toDate = filter.toDate ? new Date(filter.toDate) : new Date();
      const query = this.manager
        .getRepository(CasinoTransactionEntity)
        .createQueryBuilder('ct')
        .leftJoinAndSelect('ct.casinoGame', 'casinoGame');

      query.andWhere('ct.partnerId = :partnerId', {
        partnerId,
      });

      if (filter.playerId) {
        query.andWhere('ct.playerId = :playerId', {
          playerId: filter.playerId,
        });
      } else {
        query.andWhere('ct.playerId IS NOT NULL');
      }

      if (filter.gameId) {
        query.andWhere('ct.gameId = :gameId', { gameId: filter.gameId });
      } else {
        query.andWhere('ct.gameId IS NOT NULL');
      }

      if (filter.cancelTransaction?.toLowerCase() === 'true') {
        query.andWhere('ct.transactionCancelId IS NOT NULL');
      } else {
        query.andWhere('ct.transactionCancelId IS NULL');
      }

      if (filter.isWin?.toLowerCase() === 'true') {
        query.andWhere('ct.winAmount IS NULL');
      } else {
        query.andWhere('ct.winAmount IS NOT NULL');
      }

      if (filter.fromDate && filter.toDate) {
        query.andWhere('ct.createdAt BETWEEN :fromDate AND :toDate', {
          fromDate,
          toDate,
        });
      } else {
        query.andWhere('ct.createdAt IS NOT NULL');
      }

      if (filter.transactionId) {
        query.andWhere('ct.transactionId = :transactionId', {
          transactionId: filter.transactionId,
        });
      } else {
        query.andWhere('ct.transactionId IS NOT NULL');
      }

      if (filter.betId) {
        query.andWhere(
          '(ct.transactionBetId = :betId OR ct.transactionBetSettleId = :betId)',
          {
            betId: filter.betId,
          }
        );
      }

      query.orderBy(
        'ct.createdAt',
        filter.sortOrder === 'DESC' ? 'DESC' : 'ASC'
      );
      if (pageSize) {
        query.skip((page - 1) * pageSize).take(pageSize);
      }

      query.select(['ct', 'casinoGame.name', 'casinoGame.gameProvider']);

      const [transactionList, totalItems] = await query.getManyAndCount();
      const totalPages = Math.ceil(totalItems / pageSize);

      const newTransactionList = transactionList.map((transaction) => {
        let status: string;

        if (transaction.transactionCancelId) {
          status = 'Canceled';
        } else if (transaction.winAmount && Number(transaction.winAmount) > 0) {
          status = 'Win';
        } else {
          status = 'Lose';
        }

        return {
          ...transaction,
          status,
        };
      });

      this.logger.log(
        `[Fim] Find all casino transactions: ${JSON.stringify(filter)}`
      );
      return {
        data: newTransactionList,
        totalItems: totalItems,
        totalPages: totalPages,
        currentPage: page,
        pageSize: pageSize,
      };
    } catch (error) {
      this.logger.error(`Error finding casino transactions: ${error}`);
      throw new HttpException(
        {
          errorCode: error.response ? error.response.errorCode : 2,
          errorMsg: error.resposnse
            ? error.response.errorMsg
            : 'Error finding casino transactions',
        },
        HttpStatus.SERVICE_UNAVAILABLE
      );
    }
  }

  async findKpisPlayer(
    filter: GenericFilter & CasinoTransactionFilterDto,
    req: Request
  ) {
    try {
      const partnerId = req.headers['partner-id'] as string;

      const transactions = await this.manager
        .createQueryBuilder(CasinoTransactionEntity, 'transaction')
        .select('transaction.amount', 'amount')
        .addSelect('transaction.winAmount', 'winAmount')
        .addSelect('transaction.createdAt', 'createdAt')
        .addSelect('transaction.bet_transaction_id', 'bet_transaction_id')
        .addSelect('transaction.settle_transaction_id', 'settle_transaction_id')
        .addSelect(
          'transaction.betsettle_transaction_id',
          'betsettle_transaction_id'
        )
        .addSelect('transaction.requestType', 'requestType')
        .where('transaction.partnerId = :partnerId', { partnerId })
        .andWhere('transaction.player_id = :playerId', {
          playerId: filter.playerId,
        })
        .orderBy('transaction.createdAt', 'DESC')
        .getRawMany();

      let totalStake = 0;
      let totalWin = 0;
      let totalBonusStake = 0;
      let totalBonusWin = 0;

      if (!transactions.length) {
        return {
          lastCasinoBet: null,
          totalStake: 0,
          totalWin: 0,
          performance: { profitLoss: 0, roi: 0 },
          bonus: { stake: 0, win: 0 },
        };
      }
      for (const bet of transactions) {
        if (bet.requestType === 'RealMoney') {
          totalStake += Number(bet.amount);
          totalWin += Number(bet.winAmount);
        } else {
          totalBonusStake += Number(bet.amount);
          totalBonusWin += Number(bet.winAmount);
        }
      }
      const profitLoss = totalWin - totalStake;
      const roi = totalStake !== 0 ? (profitLoss / totalStake) * 100 : 0;
      return {
        lastCasinoBet: {
          date: transactions[0].createdAt || null,
          amount: Number(transactions[0].amount) || 0,
        },
        totalStake,
        totalWin,
        performance: {
          profitLoss,
          roi: Number(roi.toFixed(2)),
        },
        bonus: {
          stake: totalBonusStake,
          win: totalBonusWin,
        },
      };
    } catch (e) {
      throw new HttpException(
        'erro ao buscar kpis do jogador',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async findAvgBet(
    filter: GenericFilter & { fromDate: string; toDate: string },
    req: Request
  ): Promise<{ averageBet: number }> {
    try {
      this.logger.log(`[Inicio] Find avg bet: ${JSON.stringify(filter)}`);

      const { fromDate, toDate } = this.dateService.getDateRange(
        filter.fromDate,
        filter.toDate
      );
      const partnerId = req.headers['partner-id'] as string;
      const { amount, count } = await this.manager
        .createQueryBuilder(CasinoTransactionEntity, 'transaction')
        .select('SUM(transaction.amount)', 'amount')
        .addSelect('COUNT(*)', 'count')
        .where('transaction.partnerId = :partnerId', { partnerId })
        .andWhere('transaction.createdAt BETWEEN :fromDate AND :toDate', {
          fromDate,
          toDate,
        })
        .getRawOne();
      const averageBet = Number(amount) / Number(count);
      this.logger.log(`[Fim] Find avg bet: ${averageBet}`);
      return { averageBet: averageBet ? averageBet : 0 };
    } catch (e) {
      this.logger.error(`Error in findAvgBet: ${e}`, e);
      throw new HttpException(
        `Error in findAvgBet : ${e}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async findBetCount(
    filter: GenericFilter & { fromDate: string; toDate: string },
    req: Request
  ): Promise<{ totalPlayer: number }> {
    try {
      this.logger.log(`[Inicio] Find bet count: ${JSON.stringify(filter)}`);
      const partnerId = req.headers['partner-id'] as string;
      const { fromDate, toDate } = this.dateService.getDateRange(
        filter.fromDate,
        filter.toDate
      );
      const { totalPlayer } = await this.manager
        .createQueryBuilder(CasinoTransactionEntity, 'transaction')
        .select('COUNT(DISTINCT session.playerId)', 'totalPlayer')
        .leftJoin('transaction.session', 'session')
        .where('session.partnerId = :partnerId', { partnerId })
        .andWhere('transaction.transactionBetId IS NOT NULL')
        .andWhere('transaction.transactionBetSettleId IS NOT NULL')
        .andWhere('b.transactionSettleId IS NULL')
        .andWhere('transaction.createdAt BETWEEN :fromDate AND :toDate', {
          fromDate,
          toDate,
        })
        .getRawOne();
      this.logger.log(`[Fim] Find bet count: ${totalPlayer}`);
      return { totalPlayer: totalPlayer ? Number(totalPlayer) : 0 };
    } catch (e) {
      this.logger.error(`Error in findBetCount: ${e}`, e);
      throw new HttpException(
        `Error in findBetCount : ${e}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async findBetProfitable(
    filter: GenericFilter & { fromDate: string; toDate: string },
    req: Request
  ) {
    try {
      this.logger.log(
        `[Inicio] Find bet profitable: ${JSON.stringify(filter)}`
      );
      const { fromDate, toDate } = this.dateService.getDateRange(
        filter.fromDate,
        filter.toDate
      );

      const partnerId = req.headers['partner-id'] as string;

      const bets = await this.manager
        .createQueryBuilder(CasinoGames, 'cg')
        .select('cg.name', 'gameName')
        .addSelect('cg.id', 'gameId')
        .addSelect('COALESCE(SUM(bets_data.total_bets), 0)', 'totalBets')
        .addSelect(
          'COALESCE(SUM(bets_data.total_bet_amount), 0)',
          'totalBetAmount'
        )
        .addSelect(
          'COALESCE(SUM(settles_data.total_win_amount), 0)',
          'totalWinAmount'
        )
        .addSelect(
          'COALESCE(SUM(bets_data.total_bet_amount), 0) - COALESCE(SUM(settles_data.total_win_amount), 0)',
          'profit'
        )
        .leftJoin('cg.sessions', 'cs')
        .leftJoin(
          (qb) =>
            qb
              .select('b.session', 'casino_session_id')
              .addSelect('COUNT(*)', 'total_bets')
              .addSelect('SUM(b.amount)', 'total_bet_amount')
              .from(CasinoTransactionEntity, 'b')
              .where('b.createdAt BETWEEN :fromDate AND :toDate')
              .andWhere('b.transactionBetId IS NOT NULL')
              .andWhere('b.transactionBetSettleId IS NOT NULL')
              .andWhere('b.transactionSettleId IS NULL')
              .groupBy('b.session'),
          'bets_data',
          'bets_data.casino_session_id = cs.id'
        )
        .leftJoin(
          (qb) =>
            qb
              .select('s.session', 'casino_session_id')
              .addSelect('SUM(s.amount)', 'total_win_amount')
              .from(CasinoTransactionEntity, 's')
              .where('s.roundEnded = true')
              .groupBy('s.session'),
          'settles_data',
          'settles_data.casino_session_id = cs.id'
        )
        .where('cs.partner_id = :partnerId', { partnerId })
        .groupBy('cg.id')
        .addGroupBy('cg.name')
        .setParameters({ fromDate, toDate })
        .getRawMany();

      const sorted = bets.sort((a, b) => Number(b.profit) - Number(a.profit));

      const top3 = sorted.slice(0, 3);
      const bottom3 = sorted.slice(-3);

      const totalProfitTop3 = top3.reduce(
        (acc, game) => acc + Number(game.profit),
        0
      );

      const format = (games: typeof top3) =>
        games.map((game) => ({
          casino: game.gameName,
          totalBets: Number(game.totalBets),
          earnings: Number(game.totalWinAmount),
          profit: Number(game.profit),
          percentageContribution:
            totalProfitTop3 > 0
              ? Number(((game.profit / totalProfitTop3) * 100).toFixed(2))
              : 0,
        }));

      return {
        top3Games: format(top3),
        bottom3Games: format(bottom3),
      };
    } catch (e) {
      this.logger.error(`Error in findBetProfitable: ${e}`, e);
      throw new HttpException(
        `Error in findBetProfitable : ${e}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async findBetTransaction(
    filter: GenericFilter & { fromDate: string; toDate: string },
    req: Request
  ) {
    try {
      this.logger.log(
        `[Inicio] Find bet transaction: ${JSON.stringify(filter)}`
      );
      const partnerId = req.headers['partner-id'] as string;

      const { fromDate, toDate } = this.dateService.getDateRange(
        filter.fromDate,
        filter.toDate
      );
      const totalBet = await this.manager
        .createQueryBuilder(CasinoTransactionEntity, 'transaction')
        .select('transaction.amount', 'totalBet')
        .where('transaction.partnerId = :partnerId', { partnerId })
        .andWhere('transaction.transactionBetId IS NOT NULL')
        .andWhere('transaction.transactionBetSettleId IS NOT NULL')
        .andWhere('transaction.transactionSettleId IS NULL')
        .andWhere('transaction.created_at BETWEEN :fromDate AND :toDate', {
          fromDate,
          toDate,
        })
        .getRawMany();

      const totalSettle = await this.manager
        .createQueryBuilder(CasinoTransactionEntity, 'transaction')
        .select('transaction.winAmount', 'totalSettle')
        .where('transaction.partnerId = :partnerId', { partnerId })
        .andWhere('transaction.transactionSettleId IS NOT NULL')
        .andWhere('transaction.transactionBetId IS NULL')
        .andWhere('transaction.transactionBetSettleId IS NULL')
        .andWhere('transaction.created_at BETWEEN :fromDate AND :toDate', {
          fromDate,
          toDate,
        })
        .getRawMany();
      const totalBetsCount = await this.manager
        .createQueryBuilder(CasinoTransactionEntity, 'transaction')
        .select('COUNT(*)', 'totalBets')
        .where('transaction.partnerId = :partnerId', { partnerId })
        .andWhere('transaction.transactionBetId IS NOT NULL')
        .andWhere('transaction.transactionBetSettleId IS NOT NULL')
        .andWhere('transaction.transactionSettleId IS NULL')
        .andWhere('transaction.created_at BETWEEN :fromDate AND :toDate', {
          fromDate,
          toDate,
        })
        .getRawOne();

      const totalPlayers = await this.manager
        .createQueryBuilder(CasinoTransactionEntity, 'transaction')
        .select('COUNT(DISTINCT transaction.playerId)', 'totalPlayers')
        .where('transaction.partnerId = :partnerId', { partnerId })
        .andWhere('transaction.created_at BETWEEN :fromDate AND :toDate', {
          fromDate,
          toDate,
        })
        .getRawOne();

      const totalBetAmount = totalBet.reduce(
        (acc, bet) => acc + Number(bet.totalBet),
        0
      );
      const totalSettleAmount = totalSettle.reduce(
        (acc, settle) => acc + Number(settle.totalSettle),
        0
      );
      const totalBets = totalBetsCount.totalBets;
      const totalUniquePlayers = Number(totalPlayers.totalPlayers);

      const GGR = totalBetAmount - totalSettleAmount;
      const profitability =
        totalBetAmount > 0 ? (GGR / totalBetAmount) * 100 : 0;
      const averageBet = totalBets > 0 ? totalBetAmount / totalBets : 0;

      return {
        income: totalBetAmount,
        profit: totalSettleAmount,
        GGR,
        profitability: parseFloat(profitability.toFixed(2)),
        averageBet: parseFloat(averageBet.toFixed(2)),
        totalUniquePlayers,
      };
    } catch (e) {
      this.logger.error(`Error in findBetTransaction: ${e}`, e);
      throw new HttpException(
        `Error in findBetTransaction : ${e}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
