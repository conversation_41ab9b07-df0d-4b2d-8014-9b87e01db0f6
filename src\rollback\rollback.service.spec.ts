import { RabbitmqService } from '@h2/rabbitmq';
import { HttpException, HttpStatus } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { EntityManager } from 'typeorm';
import { BalanceService } from '../balance/balance.service';
import { ErrorCode } from '../common/enums/error-codes.enum';
import { statusEnum } from '../common/enums/status.enum';
import { typeBetEnum } from '../common/enums/type-bet.enum';
import { BusinessException } from '../common/exceptions/business.exception';
import { WalletService } from '../wallet/wallet.service';
import { RollbackRequestDto } from './dto/rollback-request.dto';
import { RollbackService } from './rollback.service';

describe('RollbackService', () => {
  let service: RollbackService;

  const mockEntityManager = {
    findOne: jest.fn(),
    insert: jest.fn(),
    transaction: jest.fn(),
  };

  const mockWalletService = {
    batchOperations: jest.fn(),
  };

  const mockRabbitmqService = {
    publishToQueue: jest.fn(),
  };

  const mockBalanceService = {
    getBalanceInWallet: jest.fn(),
  };

  const mockRollbackDto: RollbackRequestDto = {
    playerId: 'player-123',
    sessionId: 'session-123',
    roundId: 'round-123',
    gameId: 'game-123',
    currency: 'BRL',
    aggregatorCode: 'AGG',
    roundEnded: false,
    transactions: [
      {
        id: 'tx-rollback-1',
        originalId: 'tx-original-1',
      },
    ],
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RollbackService,
        { provide: EntityManager, useValue: mockEntityManager },
        { provide: WalletService, useValue: mockWalletService },
        { provide: RabbitmqService, useValue: mockRabbitmqService },
        { provide: BalanceService, useValue: mockBalanceService },
      ],
    }).compile();

    service = module.get<RollbackService>(RollbackService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('processRollback', () => {
    it('deve processar rollback com sucesso para transação existente', async () => {
      const mockSession = {
        partnerId: 'partner-123',
        id: 'session-id-123',
      };

      const mockTransaction = {
        id: 'transaction-123',
        partnerId: 'partner-123',
        status: statusEnum.Success,
        balance: 100,
        details: [],
      };

      const mockGame = {
        id: 'game-123',
        name: 'Test Game',
        gameProvider: 'Provider',
        gameProviderId: 'provider-123',
        category: { name: 'Slots' },
      };

      const mockOriginalDetail = {
        id: 'detail-123',
        type: typeBetEnum.Bet,
        amount: 50,
        transactionId: 'tx-original-1',
      };

      const mockInsertResult = {
        identifiers: [{ id: 'new-detail-id' }],
      };

      mockEntityManager.findOne
        .mockResolvedValueOnce(mockSession) // CasinoSession
        .mockResolvedValueOnce(mockTransaction) // Transaction
        .mockResolvedValueOnce(mockGame) // Game
        .mockResolvedValueOnce(null) // Idempotency check
        .mockResolvedValueOnce(mockOriginalDetail); // Original detail

      mockEntityManager.insert.mockResolvedValue(mockInsertResult);

      mockWalletService.batchOperations.mockResolvedValue({
        subAccount: { balance: 150 },
      });

      mockRabbitmqService.publishToQueue.mockResolvedValue(undefined);

      const result = await service.processRollback(mockRollbackDto);

      expect(result).toBeDefined();
      expect(result.balance).toBe('150');
      expect(result.roundIdCasino).toBe('transaction-123');
      expect(result.transactions).toHaveLength(1);
      expect(result.transactions[0].id).toBe('tx-rollback-1');
    });

    it('deve criar nova transação cancelada quando transação não existe', async () => {
      const mockSession = {
        partnerId: 'partner-123',
        id: 'session-id-123',
      };

      mockEntityManager.findOne
        .mockResolvedValueOnce(mockSession) // CasinoSession
        .mockResolvedValueOnce(null); // Transaction not found

      mockBalanceService.getBalanceInWallet.mockResolvedValue(100);

      mockEntityManager.transaction.mockImplementation(async callback => {
        return await callback(mockEntityManager);
      });

      mockEntityManager.insert.mockResolvedValue({
        identifiers: [{ id: 'new-tx-id' }],
      });

      const result = await service.processRollback(mockRollbackDto);

      expect(result).toBeDefined();
      expect(result.balance).toBe('100');
      expect(mockBalanceService.getBalanceInWallet).toHaveBeenCalledWith(
        'partner-123',
        'player-123',
      );
      expect(mockEntityManager.transaction).toHaveBeenCalled();
    });

    it('deve lançar BusinessException quando sessão não encontrada', async () => {
      mockEntityManager.findOne.mockResolvedValue(null); // Session not found

      await expect(service.processRollback(mockRollbackDto)).rejects.toThrow(
        BusinessException,
      );

      // Verify error code
      try {
        await service.processRollback(mockRollbackDto);
      } catch (error) {
        expect(error).toBeInstanceOf(BusinessException);
        expect((error as BusinessException).errorCode).toBe(
          ErrorCode.NOT_FOUND,
        );
      }
    });

    it('deve retornar balance atual quando transação tem status FailedWallet', async () => {
      const mockSession = {
        partnerId: 'partner-123',
        id: 'session-id-123',
      };

      const mockTransaction = {
        id: 'transaction-123',
        status: statusEnum.FailedWallet,
        balance: 200,
        details: [
          {
            id: 'detail-1',
            transactionId: 'tx-1',
            createdAt: new Date(),
          },
        ],
      };

      mockEntityManager.findOne
        .mockResolvedValueOnce(mockSession)
        .mockResolvedValueOnce(mockTransaction);

      const result = await service.processRollback(mockRollbackDto);

      expect(result.balance).toBe('200');
      expect(result.roundIdCasino).toBe('transaction-123');
      expect(mockWalletService.batchOperations).not.toHaveBeenCalled();
    });

    it('deve lançar BusinessException quando jogo não encontrado', async () => {
      const mockSession = {
        partnerId: 'partner-123',
        id: 'session-id-123',
      };

      const mockTransaction = {
        id: 'transaction-123',
        status: statusEnum.Success,
        details: [],
      };

      mockEntityManager.findOne
        .mockResolvedValueOnce(mockSession)
        .mockResolvedValueOnce(mockTransaction)
        .mockResolvedValueOnce(null); // Game not found

      await expect(service.processRollback(mockRollbackDto)).rejects.toThrow(
        BusinessException,
      );
    });

    it('deve retornar transações existentes quando todas são idempotentes', async () => {
      const mockSession = {
        partnerId: 'partner-123',
        id: 'session-id-123',
      };

      const mockTransaction = {
        id: 'transaction-123',
        partnerId: 'partner-123',
        status: statusEnum.Success,
        balance: 100,
      };

      const mockGame = {
        id: 'game-123',
        name: 'Test Game',
        gameProvider: 'Provider',
        category: { name: 'Slots' },
      };

      const mockExistingDetail = {
        id: 'existing-detail-id',
        type: typeBetEnum.Bet,
        amount: 50,
        createdAt: new Date(),
      };

      mockEntityManager.findOne
        .mockResolvedValueOnce(mockSession)
        .mockResolvedValueOnce(mockTransaction)
        .mockResolvedValueOnce(mockGame)
        .mockResolvedValueOnce(mockExistingDetail); // Idempotency check returns existing

      const result = await service.processRollback(mockRollbackDto);

      expect(result.balance).toBe('100');
      expect(mockWalletService.batchOperations).not.toHaveBeenCalled();
      expect(mockRabbitmqService.publishToQueue).not.toHaveBeenCalled();
    });

    it('deve lançar BusinessException quando detail original não encontrado', async () => {
      const mockSession = {
        partnerId: 'partner-123',
        id: 'session-id-123',
      };

      const mockTransaction = {
        id: 'transaction-123',
        status: statusEnum.Success,
      };

      const mockGame = {
        id: 'game-123',
        name: 'Test Game',
        gameProvider: 'Provider',
        category: { name: 'Slots' },
      };

      mockEntityManager.findOne
        .mockResolvedValueOnce(mockSession)
        .mockResolvedValueOnce(mockTransaction)
        .mockResolvedValueOnce(mockGame)
        .mockResolvedValueOnce(null) // Idempotency check - not found
        .mockResolvedValueOnce(null); // Original detail not found

      await expect(service.processRollback(mockRollbackDto)).rejects.toThrow(
        BusinessException,
      );
    });

    it('deve processar múltiplas transações de rollback', async () => {
      const multipleTransactionsDto = {
        ...mockRollbackDto,
        transactions: [
          { id: 'tx-rollback-1', originalId: 'tx-original-1' },
          { id: 'tx-rollback-2', originalId: 'tx-original-2' },
        ],
      };

      const mockSession = {
        partnerId: 'partner-123',
        id: 'session-id-123',
      };

      const mockTransaction = {
        id: 'transaction-123',
        partnerId: 'partner-123',
        status: statusEnum.Success,
        balance: 100,
      };

      const mockGame = {
        id: 'game-123',
        name: 'Test Game',
        gameProvider: 'Provider',
        gameProviderId: 'provider-123',
        category: { name: 'Slots' },
      };

      const mockOriginalDetail1 = {
        id: 'detail-1',
        type: typeBetEnum.Bet,
        amount: 50,
      };

      const mockOriginalDetail2 = {
        id: 'detail-2',
        type: typeBetEnum.Win,
        amount: 30,
      };

      mockEntityManager.findOne
        .mockResolvedValueOnce(mockSession)
        .mockResolvedValueOnce(mockTransaction)
        .mockResolvedValueOnce(mockGame)
        // First transaction
        .mockResolvedValueOnce(null) // Idempotency check
        .mockResolvedValueOnce(mockOriginalDetail1) // Original detail
        // Second transaction
        .mockResolvedValueOnce(null) // Idempotency check
        .mockResolvedValueOnce(mockOriginalDetail2); // Original detail

      mockEntityManager.insert.mockResolvedValue({
        identifiers: [{ id: 'new-detail-id' }],
      });

      mockWalletService.batchOperations.mockResolvedValue({
        subAccount: { balance: 180 },
      });

      mockRabbitmqService.publishToQueue.mockResolvedValue(undefined);

      const result = await service.processRollback(multipleTransactionsDto);

      expect(result.transactions).toHaveLength(2);
      expect(mockWalletService.batchOperations).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            adjustmentType: typeBetEnum.RefundBet,
          }),
          expect.objectContaining({
            adjustmentType: typeBetEnum.RefundWin,
          }),
        ]),
      );
    });

    it('deve converter HttpException genérica em caso de erro inesperado', async () => {
      mockEntityManager.findOne.mockRejectedValue(new Error('Database error'));

      await expect(service.processRollback(mockRollbackDto)).rejects.toThrow(
        HttpException,
      );
    });

    it('deve propagar BusinessException sem conversão', async () => {
      const businessError = new BusinessException(
        ErrorCode.NOT_FOUND,
        'Custom error',
        {},
        HttpStatus.NOT_FOUND,
      );

      mockEntityManager.findOne.mockRejectedValue(businessError);

      await expect(service.processRollback(mockRollbackDto)).rejects.toThrow(
        BusinessException,
      );
    });
  });
});
