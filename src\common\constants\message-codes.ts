// EGT Digital v1.5

export const MessageCodes = {
  SUCCESS: {
    code: 0,
    Message:
      'Success – message has been processed successfully by the Platform.',
  },

  ALREADY_PROCESSED: {
    code: 1,
    message:
      'Already processed – message has been already processed by the Platform (duplicate request).',
  },

  GENERIC_ERROR: {
    code: 2,
    message:
      'Generic error – any other error not enlisted in this table which should not cause request to be retried.',
  },

  EXPIRED_TOKEN: {
    code: 1101,
    message: 'Expired token – token has expired.',
  },

  INVALID_TOKEN: {
    code: 1102,
    message: 'Invalid token – no such token can be found.',
  },

  PLAYER_NOT_FOUND: {
    code: 1111,
    message:
      'Player not found – player with such ID cannot be found in the Platform.',
  },

  SESSION_NOT_FOUND: {
    code: 1112,
    message:
      'Session not found – session with such ID cannot be found in the Platform.',
  },

  GAME_NOT_FOUND: {
    code: 1113,
    message:
      'Game not found – game with such ID cannot be found in the Platform.',
  },

  ROUND_NOT_FOUND: {
    code: 1114,
    message:
      'Round not found – round with such ID cannot be found in the Platform.',
  },

  BET_NOT_FOUND: {
    code: 1115,
    message: 'Bet not found – no bet found when settling or canceling.',
  },

  INSUFFICIENT_FUNDS: {
    code: 1201,
    message:
      'Insufficient funds – player doesn’t have enough funds in his wallet to perform the operation.',
  },

  PLAYER_BLOCKED: {
    code: 1202,
    message: 'Player is blocked in the Platform.',
  },

  TOTAL_LOSS_LIMIT: {
    code: 1211,
    message: 'Total loss limit exceeded.',
  },

  TOTAL_STAKE_LIMIT: {
    code: 1212,
    message: 'Total stake limit exceeded.',
  },

  MAX_STAKE_LIMIT: {
    code: 1213,
    message: 'Max stake limit exceeded.',
  },

  DAILY_TIME_LIMIT: {
    code: 1301,
    message: 'Daily time limit reached.',
  },

  WEEKLY_TIME_LIMIT: {
    code: 1302,
    message: 'Weekly time limit reached.',
  },

  MONTHLY_TIME_LIMIT: {
    code: 1303,
    message: 'Monthly time limit reached.',
  },

  SELF_EXCLUDED: {
    code: 1401,
    message: 'Player has self-excluded himself.',
  },
};

// Para compatibilidade com código existente
export const SUCCESS = MessageCodes.SUCCESS;
export const ALREADY_PROCESSED = MessageCodes.ALREADY_PROCESSED;
export const GENERIC_ERROR = MessageCodes.GENERIC_ERROR;
export const EXPIRED_TOKEN = MessageCodes.EXPIRED_TOKEN;
export const INVALID_TOKEN = MessageCodes.INVALID_TOKEN;
export const PLAYER_NOT_FOUND = MessageCodes.PLAYER_NOT_FOUND;
export const SESSION_NOT_FOUND = MessageCodes.SESSION_NOT_FOUND;
export const GAME_NOT_FOUND = MessageCodes.GAME_NOT_FOUND;
export const ROUND_NOT_FOUND = MessageCodes.ROUND_NOT_FOUND;
export const BET_NOT_FOUND = MessageCodes.BET_NOT_FOUND;
export const INSUFFICIENT_FUNDS = MessageCodes.INSUFFICIENT_FUNDS;
export const PLAYER_BLOCKED = MessageCodes.PLAYER_BLOCKED;
export const TOTAL_LOSS_LIMIT = MessageCodes.TOTAL_LOSS_LIMIT;
export const TOTAL_STAKE_LIMIT = MessageCodes.TOTAL_STAKE_LIMIT;
export const MAX_STAKE_LIMIT = MessageCodes.MAX_STAKE_LIMIT;
export const DAILY_TIME_LIMIT = MessageCodes.DAILY_TIME_LIMIT;
export const WEEKLY_TIME_LIMIT = MessageCodes.WEEKLY_TIME_LIMIT;
export const MONTHLY_TIME_LIMIT = MessageCodes.MONTHLY_TIME_LIMIT;
export const SELF_EXCLUDED = MessageCodes.SELF_EXCLUDED;
