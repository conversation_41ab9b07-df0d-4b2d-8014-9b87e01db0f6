import { Injectable, Logger } from '@nestjs/common';
import * as crypto from 'crypto';

@Injectable()
export class HMACService {
  private readonly hmacSecret = process.env.SECRET_KEY;
  private readonly logger = new Logger(HMACService.name);

  /**
    * Função para gerar o HMAC
    * @param {string} data - O corpo da mensagem JSON (em formato string) que foi enviado na solicitação.
    * @returns {string} - O HMAC gerado em formato hexadecimal.
  */
  generateHMAC(data: string): string {
    this.logger.log(`[Inicio] Generating HMAC`);
    const hmac = crypto
      .createHmac('sha256', this.hmacSecret)
      .update(data)
      .digest('hex');
      this.logger.log(`[Fim] Generating HMAC`);
      return  hmac
  }

  /**
    * Função para validar o HMAC recebido
    * @param {string} hmac - O HMAC recebido no cabeçalho `X-Checksum`.
    * @param {string} data - O corpo da mensagem JSON recebido (em formato string).
    * @returns {boolean} - Retorna true se o HMAC for válido, false caso contrário.
  */
  validateHMAC(data: string, hmac: string): boolean {
    this.logger.log(`[Inicio] validateHMAC: ${JSON.stringify(data)}`);
    const calculatedHmac = this.generateHMAC(data);
    this.logger.log(`[Sucesso] validateHMAC: ${JSON.stringify(calculatedHmac)}`);
    const buf1 = Buffer.from(hmac);
    const buf2 = Buffer.from(calculatedHmac);
    if (buf1.length !== buf2.length) {
      return false;
    }
    return crypto.timingSafeEqual(buf1, buf2);
  }
  
}
