import { Body, Controller, HttpCode, Post, UseGuards } from '@nestjs/common';
import { AuthenticateService } from './authenticate.service';
import { CreateRegisterAuthenticateDto } from './dto/create-register-authenticate.dto';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { HMACGuard } from '@/common/guards/hmac.guard';
import {
  ApiHmacHeader,
  ApiHmacUnauthorizedResponse,
} from '@/common/decorators/swagger.decorator';
import { SwaggerApiCodeResponses } from '@/common/decorators/swagger-api-code-responses';
import {
  SUCCESS,
  EXPIRED_TOKEN,
  INVALID_TOKEN,
  GENERIC_ERROR,
} from '@/common/constants/message-codes';

import {
  CreateRegisterAuthenticateResponseDto,
  CreateRegisterAuthenticateResponseDtoExample,
} from './dto/create-register-authenticate-response.dto';

@ApiTags('Authentication')
@Controller('authenticate')
export class AuthenticateController {
  constructor(private readonly authenticateService: AuthenticateService) {}
  @UseGuards(HMACGuard)
  @Post()
  @HttpCode(200)
  @ApiOperation({
    summary: 'Autenticar jogador',
    description:
      'Endpoint para autenticar um jogador e iniciar uma sessão de jogo',
  })
  @ApiHmacHeader()
  @ApiHmacUnauthorizedResponse()
  @SwaggerApiCodeResponses(
    200,
    [SUCCESS.code, GENERIC_ERROR.code, EXPIRED_TOKEN.code, INVALID_TOKEN.code],
    CreateRegisterAuthenticateResponseDto,
    CreateRegisterAuthenticateResponseDtoExample
  )
  async authenticate(
    @Body() createRegisterAuthenticate: CreateRegisterAuthenticateDto
  ) {
    return this.authenticateService.authenticate(createRegisterAuthenticate);
  }
}
