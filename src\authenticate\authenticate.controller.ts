import {
  EXPIRED_TOKEN,
  GENERIC_ERROR,
  INVALID_TOKEN,
  SUCCESS,
} from '@/common/constants/message-codes';
import { SwaggerApiCodeResponses } from '@/common/decorators/swagger-api-code-responses';
import {
  ApiHmacHeader,
  ApiHmacUnauthorizedResponse,
} from '@/common/decorators/swagger.decorator';
import { Body, Controller, HttpCode, Post } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthenticateService } from './authenticate.service';
import { CreateRegisterAuthenticateDto } from './dto/create-register-authenticate.dto';

import {
  CreateRegisterAuthenticateResponseDto,
  CreateRegisterAuthenticateResponseDtoExample,
} from './dto/create-register-authenticate-response.dto';

@ApiTags('Authentication')
@Controller('authenticate')
export class AuthenticateController {
  constructor(private readonly authenticateService: AuthenticateService) {}
  //@UseGuards(HMACGuard)
  @Post()
  @HttpCode(200)
  @ApiOperation({
    summary: 'Autenticar jogador',
    description:
      'Endpoint para autenticar um jogador e iniciar uma sessão de jogo',
  })
  @ApiHmacHeader()
  @ApiHmacUnauthorizedResponse()
  @SwaggerApiCodeResponses(
    200,
    [SUCCESS.code, GENERIC_ERROR.code, EXPIRED_TOKEN.code, INVALID_TOKEN.code],
    CreateRegisterAuthenticateResponseDto,
    CreateRegisterAuthenticateResponseDtoExample,
  )
  async authenticate(
    @Body() createRegisterAuthenticate: CreateRegisterAuthenticateDto,
  ) {
    return this.authenticateService.authenticate(createRegisterAuthenticate);
  }
}
