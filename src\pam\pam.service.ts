import { Injectable } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class PamService {
  private pamUrl = process.env.API_PAM;

  async getUser(email: string, req: any) {
    try {
      const { authorization } = req.headers;
      const user = await axios.get(`${this.pamUrl}/users/email/${email}`, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${authorization.split(' ')[1]}`,
          'partner-id': req.headers['partner-id'],
          'client-ip': req.headers['client-ip'],
        },
      });
      return user.data;
    } catch (err) {
      console.log(err);
    }
  }
}
