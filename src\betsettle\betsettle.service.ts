import { Injectable, Logger } from "@nestjs/common";
import { EntityManager } from "typeorm";
import { CreateBetSettleDto } from "./dto/create-betsettle.dto";
import { CasinoTransactionEntity } from "@/casino-transaction/controller/entities/casino-transactions.entity";
import { WalletService } from "@/wallet/wallet.service";
import { InjectEntityManager } from "@nestjs/typeorm";
import { CasinoSessionEntity } from "@/common/entities/casino-session.entity";
import { CasinoGames } from "@/casino-games/entities/casino-game.entity";

@Injectable()
export class BetSettleService {
  private readonly logger = new Logger(BetSettleService.name);
  constructor(
    @InjectEntityManager() private readonly manager: EntityManager,
    private readonly walletService: WalletService
  ) {}

  async create(createBetSettleDto: CreateBetSettleDto): Promise<{
    balance?: number;
    errorCode: number;
    errorMsg?: string;
    casinoTransactionId?: string;
    reconcileAmount?: number;
    reconcileWinAmount?: number;
  }> {
    try {
      this.logger.log(
        `[Inicio] Create bet settle: ${JSON.stringify(createBetSettleDto)}`
      );
      const searchPartner = await this.manager.findOne(CasinoSessionEntity, {
        select: ["partnerId"],
        where: {
          playerId: createBetSettleDto.playerId,
        },
      });
      const partnerId = searchPartner.partnerId;

      const result = await this.manager.insert(CasinoTransactionEntity, {
        transactionBetSettleId: createBetSettleDto.transactionId,
        partnerId: partnerId,
        playerId: createBetSettleDto.playerId,
        gameId: createBetSettleDto.gameId.toString(),
        requestType: createBetSettleDto.requestType,
        roundId: createBetSettleDto.roundId,
        transactionId: createBetSettleDto.transactionId,
        amount: Number(createBetSettleDto.amount.toFixed(2)),
        winAmount: Number(createBetSettleDto.winAmount.toFixed(2)),
        roundEnded: createBetSettleDto.roundEnded,
        sessionCasinoId: createBetSettleDto.sessionId,
      });

      const { gameProvider } = await this.manager.findOne(CasinoGames, {
        where: {
          gameId: createBetSettleDto.gameId.toString(),
        },
      });

      const walletResult = await this.walletService.debit(
        {
          amount: createBetSettleDto.amount,
          reason: "BetSettle",
          isBonusOperation: false,
          gameId: createBetSettleDto.gameId.toString(),
          gameProvider,
          adjustmentType: "bet",
        },
        partnerId,
        createBetSettleDto.playerId
      );

      if (createBetSettleDto.winAmount) {
        const winWalletResult = await this.walletService.credit(
          {
            amount: createBetSettleDto.winAmount,
            reason: "BetSettle",
            isBonusOperation: false,
            gameId: createBetSettleDto.gameId.toString(),
            adjustmentType: "bet",
          },
          partnerId,
          createBetSettleDto.playerId
        );
        this.logger.log(
          `[Fim] Create bet settle winAmount: ${JSON.stringify(createBetSettleDto)}`
        );
        return {
          balance: winWalletResult.subAccount.balance,
          errorCode: 0,
          casinoTransactionId: result.generatedMaps[0].id_transaction,
          reconcileAmount: createBetSettleDto.amount,
          reconcileWinAmount: createBetSettleDto.winAmount,
        };
      }
      this.logger.log(
        `[Fim] Create bet settle: ${JSON.stringify(createBetSettleDto)}`
      );
      return {
        balance: walletResult.subAccount.balance,
        errorCode: 0,
        casinoTransactionId: result.generatedMaps[0].id_transaction,
        reconcileAmount: createBetSettleDto.amount,
        reconcileWinAmount: createBetSettleDto.winAmount,
      };
    } catch (error) {
      this.logger.error(`Error creating bet settle: ${error}`);
      
      if (error.response?.data) {
        this.logger.error(error.response.data);
      } else {
        this.logger.error("bet settle não tem response.data");
      }

      return {
        errorCode: error.response ? error.response.errorCode : 2,
        errorMsg: error.response ? error.response.errorMsg : error.message,
      };
    }
  }
}
