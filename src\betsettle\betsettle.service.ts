import { BalanceService } from '@/balance/balance.service';
import { CasinoGames } from '@/casino-games/entities/casino-game.entity';
import { CasinoTransactionDetailsEntity } from '@/casino-transaction/controller/entities/casino-transaction-details.entity';
import { CasinoTransactionEntity } from '@/casino-transaction/controller/entities/casino-transactions.entity';
import { CasinoSessionEntity } from '@/common/entities/casino-session.entity';
import { ErrorCode } from '@/common/enums/error-codes.enum';
import { statusEnum } from '@/common/enums/status.enum';
import { BusinessException } from '@/common/exceptions/business.exception';
import { WalletService } from '@/wallet/wallet.service';
import { RabbitmqService } from '@h2/rabbitmq';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { BetsettleFinishResponseDto } from './dto/create-betsettle-finish-response.dto';
import { CreateBetsettleFinishDto } from './dto/create-betsettle-finish.dto';
import {
  CreateBetSettleResponseDto,
  CreateBetSettleTransactionResponseDto,
} from './dto/create-betsettle-response.dto';
import {
  CreateBetSettleDto,
  TransactionType,
} from './dto/create-betsettle.dto';

@Injectable()
export class BetSettleService {
  private readonly logger = new Logger(BetSettleService.name);
  constructor(
    @InjectEntityManager() private readonly manager: EntityManager,
    private readonly walletService: WalletService,
    private readonly rabbitmqService: RabbitmqService,
    private readonly balanceService: BalanceService,
  ) {}

  private async publishCasinoMessage(params: {
    activityId: string;
    amount: number;
    balanceAfter: number;
    balanceBefore: number;
    currency: string;
    gameId: string;
    gameName: string;
    gameType: string;
    isRoundEnd: boolean;
    roundId: string;
    status: string;
    type: string;
    userId: string;
    vendorId: string;
    vendorName: string;
    origin: string;
  }): Promise<void> {
    const message = {
      activity_id: params.activityId,
      amount: params.amount,
      balance_after: params.balanceAfter,
      balance_before: params.balanceBefore,
      currency: params.currency,
      exchange_rate: 1,
      game_id: params.gameId,
      game_name: params.gameName,
      game_type: params.gameType,
      is_round_end: params.isRoundEnd,
      origin: params.origin,
      round_id: params.roundId,
      status: params.status,
      timestamp: new Date().toISOString(),
      type: params.type,
      user_id: params.userId,
      vendor_id: params.vendorId,
      vendor_name: params.vendorName,
      wager_amount: params.amount,
    };

    try {
      await this.rabbitmqService.publishToQueue(
        message,
        'crm.fasttrack.integration.queue',
        'CASINO',
      );
    } catch (error) {
      this.logger.error(
        `[Error] Error publishing casino message: ${JSON.stringify(error)}`,
      );
    }
  }

  async create(
    createBetSettleDto: CreateBetSettleDto,
    origin: string,
  ): Promise<CreateBetSettleResponseDto> {
    let casinoTransactionId: string = null;
    let transactionsDetails: CreateBetSettleTransactionResponseDto[] = [];
    let betAmount = 0;
    let winAmount = 0;
    let batchResult = null;

    try {
      this.logger.log(
        `[Inicio] Create bet settle: ${JSON.stringify(createBetSettleDto)}`,
      );
      const casinoSession = await this.manager.findOne(CasinoSessionEntity, {
        select: ['partnerId', 'id'],
        where: {
          playerId: createBetSettleDto.playerId,
          aggregatorId: createBetSettleDto.sessionId,
        },
      });

      if (!casinoSession) {
        throw new BusinessException(
          ErrorCode.NOT_FOUND,
          'Sessão não encontrada',
          {},
          HttpStatus.NOT_FOUND,
        );
      }
      const partnerId = casinoSession.partnerId;

      const existingTransaction = await this.manager.findOne(
        CasinoTransactionEntity,
        {
          where: {
            playerId: createBetSettleDto.playerId,
            roundId: createBetSettleDto.roundId,
          },
        },
      );

      if (
        existingTransaction &&
        existingTransaction.status == statusEnum.Canceled
      ) {
        const response = await this.createTransactionCanceled(
          existingTransaction,
          createBetSettleDto,
        );

        return response;
      }

      casinoTransactionId = existingTransaction?.id;

      let pendingTransactions = createBetSettleDto.transactions;

      const finalListDetails = createBetSettleDto.transactions.map(
        transaction => ({
          id: null,
          externalId: transaction.externalId,
          amount: parseFloat(transaction.amount),
          type: transaction.type,
        }),
      ) as CreateBetSettleTransactionResponseDto[];

      if (!pendingTransactions || pendingTransactions.length === 0) {
        throw new BusinessException(
          ErrorCode.NOT_FOUND,
          'Nenhuma transação fornecida para liquidação de aposta.',
          {},
          HttpStatus.NOT_FOUND,
        );
      }

      if (casinoTransactionId) {
        const existingTransactionDetails = await this.manager.find(
          CasinoTransactionDetailsEntity,
          {
            select: ['id', 'transactionId'],
            where: {
              roundId: createBetSettleDto.roundId,
            },
          },
        );

        if (existingTransactionDetails.length > 0) {
          pendingTransactions = pendingTransactions.filter(
            t =>
              !existingTransactionDetails.some(
                d => d.transactionId === t.externalId,
              ),
          );

          finalListDetails.forEach((transaction, _index) => {
            transaction.id = existingTransactionDetails.find(
              detail => detail.transactionId === transaction.externalId,
            )?.id;
          });
        }

        if (pendingTransactions.length === 0) {
          const balance = await this.balanceService.getBalanceInWallet(
            partnerId,
            createBetSettleDto.playerId,
          );

          const transactionsDetails = createBetSettleDto.transactions.map(
            transaction => ({
              id: existingTransactionDetails.find(
                detail => detail.transactionId === transaction.externalId,
              )?.id,
              externalId: transaction.externalId,
              amount: parseFloat(transaction.amount),
              type: transaction.type,
            }),
          ) as CreateBetSettleTransactionResponseDto[];

          return {
            id: casinoTransactionId,
            balance: balance,
            externalId: createBetSettleDto.roundId,
            transactions: transactionsDetails,
          } as CreateBetSettleResponseDto;
        }
      }

      betAmount = pendingTransactions
        .filter(t => t.type === TransactionType.BET)
        .reduce((sum, t) => sum + parseFloat(t.amount), 0);

      winAmount = pendingTransactions
        .filter(t => t.type === TransactionType.WIN)
        .reduce((sum, t) => sum + parseFloat(t.amount), 0);

      const txResult = await this.manager.transaction(
        async transactionalEntityManager => {
          if (casinoTransactionId == null) {
            const result = await transactionalEntityManager.insert(
              CasinoTransactionEntity,
              {
                sessionCasinoId: casinoSession.id,
                partnerId: partnerId,
                playerId: createBetSettleDto.playerId,
                gameId: createBetSettleDto.gameId,
                roundId: createBetSettleDto.roundId,
                amount: betAmount,
                winAmount: winAmount,
                roundEnded: createBetSettleDto.roundEnded,
                status: statusEnum.Pending,
                currency: createBetSettleDto.currency,
                aggregatorCode: createBetSettleDto.aggregatorCode,
              },
            );

            casinoTransactionId = result.generatedMaps[0].id;
          }

          const transactionDetails = pendingTransactions.map(transaction => ({
            casinoTransactionsId: casinoTransactionId,
            transactionId: transaction.externalId,
            roundId: createBetSettleDto.roundId,
            type: transaction.type,
            amount: parseFloat(transaction.amount),
            currency: createBetSettleDto.currency,
          }));

          const detailsResult = await transactionalEntityManager.insert(
            CasinoTransactionDetailsEntity,
            transactionDetails,
          );

          return { casinoTransactionId, detailsResult };
        },
      );

      if (
        txResult.detailsResult &&
        pendingTransactions &&
        pendingTransactions.length > 0
      ) {
        transactionsDetails = pendingTransactions.map((transaction, index) => ({
          id: txResult.detailsResult.identifiers[index].id,
          externalId: transaction.externalId,
          amount: parseFloat(transaction.amount),
          type: transaction.type,
        })) as CreateBetSettleTransactionResponseDto[];

        finalListDetails
          .filter(c => !c.id)
          .forEach((transaction, _index) => {
            transaction.id = transactionsDetails.find(
              detail => detail.externalId === transaction.externalId,
            )?.id;
          });

        this.logger.log(
          `[Info] Created ${transactionsDetails.length} transaction details for transaction ${casinoTransactionId}`,
        );
      }

      this.logger.log(
        `[DB] Transaction ${casinoTransactionId} criada com status Pending`,
      );

      const game = await this.manager.findOne(CasinoGames, {
        where: { id: createBetSettleDto.gameId },
        relations: ['category'],
      });

      if (!game) {
        this.logger.warn(`Game ${createBetSettleDto.gameId} not found`);
        throw new BusinessException(
          ErrorCode.NOT_FOUND,
          'Jogo não encontrado para o ID informado.',
          {},
          HttpStatus.NOT_FOUND,
        );
      }

      const walletOperations = transactionsDetails.map(transaction => ({
        playerId: createBetSettleDto.playerId,
        partnerId: partnerId,
        adjustmentType: transaction.type,
        amount: parseFloat(transaction.amount.toString()),
        reason: 'BetSettle',
        origin: 'casino',
        accountType: 'regular',
        isBonusOperation: false,
        gameId: createBetSettleDto.gameId,
        gameProvider: game.gameProvider,
        transactionId: transaction.id,
      }));

      this.logger.log(
        `[Wallet] Executando ${walletOperations.length} operações em lote`,
      );
      try {
        batchResult =
          await this.walletService.batchOperations(walletOperations);
      } catch (walletError) {
        this.logger.error(
          `[Wallet] Error ao executar servico wallet: ${walletError.message}`,
        );

        const finalBalance = await this.walletService.getBalanceByPlayer(
          partnerId,
          createBetSettleDto.playerId,
        );
        await this.manager.update(
          CasinoTransactionEntity,
          casinoTransactionId,
          {
            balance: finalBalance.balance,
            status: statusEnum.FailedWallet,
          },
        );

        throw walletError;
      }

      this.logger.log(
        `[Wallet] Operações concluídas. Balance: ${batchResult.initialBalance} → ${batchResult.endBalance}`,
      );

      // Publica mensagens RabbitMQ em paralelo usando Promise.all
      await Promise.all(
        pendingTransactions.map(opResult =>
          this.publishCasinoMessage({
            activityId: opResult.externalId,
            amount: Number(opResult.amount),
            type: opResult.type === 'bet' ? 'Bet' : 'Win',
            balanceBefore: batchResult.initialBalance,
            balanceAfter: batchResult.endBalance,
            currency: batchResult.subAccount.wallet.currency,
            gameId: createBetSettleDto.gameId,
            gameName: game.name,
            gameType: game.category?.name,
            isRoundEnd: false,
            roundId: createBetSettleDto.roundId,
            status: 'Approved',
            userId: createBetSettleDto.playerId,
            vendorId: game.gameProviderId,
            vendorName: game.gameProvider,
            origin: origin,
          }),
        ),
      );

      if (existingTransaction) {
        betAmount += Number(existingTransaction.amount);
        winAmount += Number(existingTransaction.winAmount);
      }

      await this.manager.update(CasinoTransactionEntity, casinoTransactionId, {
        balance: batchResult.subAccount.balance,
        status: statusEnum.Success,
        roundEnded: createBetSettleDto.roundEnded,
        winAmount: winAmount,
        amount: betAmount,
      });

      this.logger.log(
        `[Success] BetSettle ${casinoTransactionId} concluído com sucesso`,
      );

      const response: CreateBetSettleResponseDto = {
        id: casinoTransactionId,
        balance: batchResult.subAccount.balance,
        externalId: createBetSettleDto.roundId,
        transactions: finalListDetails,
      };

      return response;
    } catch (error) {
      this.logger.error(`[Error] Error no metodo betsettle: ${error.message}`);

      // Compensação: Marcar transação como Failed se foi criada
      // Não deletar para manter auditoria
      if (casinoTransactionId) {
        try {
          await this.manager.update(
            CasinoTransactionEntity,
            casinoTransactionId,
            {
              status: statusEnum.Failed,
            },
          );
          this.logger.warn(
            `[Compensação] Transaction ${casinoTransactionId} marcada como Failed`,
          );
        } catch (updateError) {
          this.logger.error(
            `[Error] Falha ao atualizar status para Failed: ${updateError.message}`,
          );
        }
      }

      // Se já é uma BusinessException, re-lança diretamente
      if (error instanceof BusinessException) {
        throw error;
      }

      // Caso contrário, lança como HttpException genérica
      throw new HttpException(
        error.message || 'Erro ao liquidar aposta.',
        error.status || HttpStatus.BAD_REQUEST,
      );
    }
  }

  async createTransactionCanceled(
    existingTransaction: CasinoTransactionEntity,
    dto: CreateBetSettleDto,
  ) {
    const betAmount = dto.transactions
      .filter(t => t.type === TransactionType.BET)
      .reduce((sum, t) => sum + parseFloat(t.amount), 0);

    const winAmount = dto.transactions
      .filter(t => t.type === TransactionType.WIN)
      .reduce((sum, t) => sum + parseFloat(t.amount), 0);

    const transactionDetails = dto.transactions.map(
      t =>
        ({
          id: uuidv4(),
          casinoTransactionsId: existingTransaction.id,
          transactionId: t.externalId,
          roundId: dto.roundId,
          type: t.type,
          amount: parseFloat(t.amount),
          currency: dto.currency,
          createdAt: new Date(),
        }) as CasinoTransactionDetailsEntity,
    ) as CasinoTransactionDetailsEntity[];

    const existingTransactionDetails = await this.manager.find(
      CasinoTransactionDetailsEntity,
      {
        where: {
          roundId: existingTransaction.roundId,
        },
      },
    );

    const updatedDetails = existingTransactionDetails.map(item => {
      const detail = transactionDetails.find(
        d => d.transactionId === item.transactionRefundId,
      );

      if (detail) {
        return {
          ...item,
          amount: detail.amount,
          type: `refund_${detail.type}`,
        };
      }
      return item;
    });

    // Se precisar juntar com transactionDetails
    const combinedDetails = [...transactionDetails, ...updatedDetails];

    // Criar nova transação com status cancelada
    await this.manager.transaction(async transactionalEntityManager => {
      await transactionalEntityManager.update(
        CasinoTransactionEntity,
        { id: existingTransaction.id },
        {
          amount: betAmount,
          winAmount: winAmount,
          updatedAt: new Date(),
        },
      );

      await transactionalEntityManager.save(
        CasinoTransactionDetailsEntity,
        combinedDetails,
      );
    });

    const response: CreateBetSettleResponseDto = {
      id: existingTransaction.id,
      balance: existingTransaction.balance,
      externalId: dto.roundId,
      transactions: transactionDetails.map(detail => ({
        id: detail.transactionId,
        idCasino: detail.id,
        processedAt: new Date(detail.createdAt).toISOString(),
        externalId: detail.transactionId,
        amount: detail.amount,
      })) as CreateBetSettleTransactionResponseDto[],
    };

    return response;
  }

  async finish(
    dto: CreateBetsettleFinishDto,
  ): Promise<BetsettleFinishResponseDto> {
    try {
      this.logger.log(
        `[Inicio] Create bet settle finish: ${JSON.stringify(dto)}`,
      );
      const casinoSession = await this.manager.findOne(CasinoSessionEntity, {
        select: ['partnerId', 'id'],
        where: {
          playerId: dto.playerId,
          aggregatorId: dto.sessionId,
        },
      });

      if (!casinoSession) {
        throw new BusinessException(
          ErrorCode.NOT_FOUND,
          'Sessão não encontrada',
          {},
          HttpStatus.NOT_FOUND,
        );
      }

      const existingTransaction = await this.manager.findOne(
        CasinoTransactionEntity,
        {
          select: ['id'],
          where: {
            playerId: dto.playerId,
            roundId: dto.roundId,
          },
        },
      );

      if (!existingTransaction) {
        throw new BusinessException(
          ErrorCode.NOT_FOUND,
          'Transação não encontrada',
          {},
          HttpStatus.NOT_FOUND,
        );
      }

      await this.manager.update(
        CasinoTransactionEntity,
        existingTransaction.id,
        {
          roundEnded: true,
          updatedAt: new Date(),
        },
      );

      this.logger.log(
        `[Success] BetSettle finish ${existingTransaction.id} concluído com sucesso`,
      );

      const balance = await this.balanceService.getBalanceInWallet(
        casinoSession.partnerId,
        dto.playerId,
      );

      const response: BetsettleFinishResponseDto = {
        balance: balance?.toString(),
      };

      return response;
    } catch (error) {
      this.logger.error(
        `[Error] Error no metodo betsettle finish: ${error.message}`,
      );

      // Se já é uma BusinessException, re-lança diretamente
      if (error instanceof BusinessException) {
        throw error;
      }

      // Caso contrário, lança como HttpException genérica
      throw new HttpException(
        error.message || 'Erro ao finalizar aposta.',
        error.status || HttpStatus.BAD_REQUEST,
      );
    }
  }
}
