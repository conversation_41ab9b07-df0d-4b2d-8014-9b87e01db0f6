import { ExecutionContext, HttpException, HttpStatus } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Test, TestingModule } from '@nestjs/testing';
import { KeycloakBackofficeGuard } from './keycloak-backoffice.guard';

describe('KeycloakBackofficeGuard', () => {
  let guard: KeycloakBackofficeGuard;

  const mockJwtService = {
    verify: jest.fn(),
  };

  const mockExecutionContext = (headers: any) =>
    ({
      switchToHttp: () => ({
        getRequest: () => ({
          headers,
          user: null,
        }),
      }),
    }) as ExecutionContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KeycloakBackofficeGuard,
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
      ],
    }).compile();

    guard = module.get<KeycloakBackofficeGuard>(KeycloakBackofficeGuard);
    jwtService = module.get<JwtService>(JwtService);

    // Reset mocks
    jest.clearAllMocks();

    // Mock environment variables
    process.env.KEYCLOAK_AUTH_SERVER_URL = 'https://keycloak.test.com';
    process.env.KEYCLOAK_REALM = 'test-realm';
    process.env.KEYCLOAK_CLIENT_ID = 'test-client';
    process.env.KEYCLOAK_CLIENT_SECRET = 'test-secret';
    process.env.JWT_SECRET_BACK_OFFICE = 'test-secret';
  });

  describe('canActivate', () => {
    it('deve rejeitar quando Authorization header não está presente', async () => {
      const context = mockExecutionContext({});

      await expect(guard.canActivate(context)).rejects.toThrow(
        new HttpException(
          'Token de autenticação não fornecido.',
          HttpStatus.UNAUTHORIZED,
        ),
      );
    });

    it('deve rejeitar quando X-User-Context header não está presente', async () => {
      const context = mockExecutionContext({
        authorization: 'Bearer keycloak-token',
      });

      await expect(guard.canActivate(context)).rejects.toThrow(
        new HttpException(
          'Contexto de usuário não fornecido.',
          HttpStatus.UNAUTHORIZED,
        ),
      );
    });

    it('deve rejeitar quando token Keycloak é inválido', async () => {
      // Mock fetch global para introspection retornando erro HTTP
      global.fetch = jest.fn().mockResolvedValue({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        text: async () => 'Unauthorized',
      });

      const context = mockExecutionContext({
        authorization: 'Bearer invalid-keycloak-token',
        'x-user-context': 'user-context-jwt',
      });

      await expect(guard.canActivate(context)).rejects.toThrow(
        new HttpException(
          'Token de autenticação inválido.',
          HttpStatus.UNAUTHORIZED,
        ),
      );

      expect(global.fetch).toHaveBeenCalledWith(
        'https://keycloak.test.com/realms/test-realm/protocol/openid-connect/token/introspect',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }),
      );
    });

    it('deve rejeitar quando X-User-Context JWT é inválido', async () => {
      // Mock fetch para introspection retornando token ativo
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: async () => ({ active: true, username: '<EMAIL>' }),
      });

      // Mock JWT verify para falhar
      mockJwtService.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      const context = mockExecutionContext({
        authorization: 'Bearer valid-keycloak-token',
        'x-user-context': 'invalid-user-context-jwt',
      });

      await expect(guard.canActivate(context)).rejects.toThrow(HttpException);
    });

    it('deve aceitar quando ambos tokens são válidos e popular req.user', async () => {
      const mockUserPayload = {
        userId: 'user-123',
        email: '<EMAIL>',
        customers: [],
        iat: 1234567890,
        exp: 1234567890,
      };

      // Mock fetch para introspection retornando token ativo
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: async () => ({
          active: true,
          sub: 'user-123',
          username: 'testuser',
          preferred_username: 'testuser',
          email: '<EMAIL>',
          client_id: 'test-client',
        }),
      });

      // Mock JWT verify para retornar payload válido
      mockJwtService.verify.mockReturnValue(mockUserPayload);

      const mockRequest = {
        headers: {
          authorization: 'Bearer valid-keycloak-token',
          'x-user-context': 'valid-user-context-jwt',
        },
        user: null,
      };

      const context = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
        }),
      } as ExecutionContext;

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
      expect(mockRequest.user).toEqual(mockUserPayload);
      expect(mockJwtService.verify).toHaveBeenCalledWith(
        'valid-user-context-jwt',
        {
          secret: 'test-secret',
        },
      );
    });

    it('deve lidar com X-User-Context como array', async () => {
      const mockUserPayload = {
        userId: 'user-123',
        email: '<EMAIL>',
      };

      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: async () => ({ active: true, email: '<EMAIL>' }),
      });

      mockJwtService.verify.mockReturnValue(mockUserPayload);

      const mockRequest = {
        headers: {
          authorization: 'Bearer valid-keycloak-token',
          'x-user-context': ['valid-user-context-jwt'],
        },
        user: null,
      };

      const context = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
        }),
      } as ExecutionContext;

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
      expect(mockRequest.user).toEqual(mockUserPayload);
    });

    it('deve rejeitar quando token Keycloak não está ativo (revogado)', async () => {
      // Mock introspection retornando active: false
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: async () => ({ active: false }),
      });

      const context = mockExecutionContext({
        authorization: 'Bearer revoked-keycloak-token',
        'x-user-context': 'user-context-jwt',
      });

      await expect(guard.canActivate(context)).rejects.toThrow(
        new HttpException(
          'Token de autenticação inválido.',
          HttpStatus.UNAUTHORIZED,
        ),
      );
    });

    it('deve rejeitar com erro apropriado quando token expirou', async () => {
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: async () => ({ active: true, email: '<EMAIL>' }),
      });

      const expiredError = new Error('Token expired');
      expiredError.name = 'TokenExpiredError';
      mockJwtService.verify.mockImplementation(() => {
        throw expiredError;
      });

      const context = mockExecutionContext({
        authorization: 'Bearer valid-keycloak-token',
        'x-user-context': 'expired-jwt',
      });

      await expect(guard.canActivate(context)).rejects.toThrow(
        new HttpException('Token expired', HttpStatus.UNAUTHORIZED),
      );
    });

    it('deve rejeitar quando variáveis de ambiente Keycloak não estão configuradas', async () => {
      const originalUrl = process.env.KEYCLOAK_AUTH_SERVER_URL;
      delete process.env.KEYCLOAK_AUTH_SERVER_URL;

      const context = mockExecutionContext({
        authorization: 'Bearer valid-token',
        'x-user-context': 'valid-jwt',
      });

      await expect(guard.canActivate(context)).rejects.toThrow(
        new HttpException(
          'Token de autenticação inválido.',
          HttpStatus.UNAUTHORIZED,
        ),
      );

      // Restore environment variable
      if (originalUrl) {
        process.env.KEYCLOAK_AUTH_SERVER_URL = originalUrl;
      }
    });

    it('deve lidar com erro de rede ao validar Keycloak', async () => {
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

      const context = mockExecutionContext({
        authorization: 'Bearer valid-token',
        'x-user-context': 'valid-jwt',
      });

      await expect(guard.canActivate(context)).rejects.toThrow(
        new HttpException(
          'Token de autenticação inválido.',
          HttpStatus.UNAUTHORIZED,
        ),
      );
    });
  });

  describe('extractBearerToken', () => {
    it('deve extrair token corretamente do header Authorization', async () => {
      const context = mockExecutionContext({
        authorization: 'Bearer test-token',
        'x-user-context': 'jwt',
      });

      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: async () => ({ active: true, email: '<EMAIL>' }),
      });

      mockJwtService.verify.mockReturnValue({ userId: '123' });

      await guard.canActivate(context);

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/token/introspect'),
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }),
      );
    });

    it('deve retornar null quando não é Bearer token', async () => {
      const context = mockExecutionContext({
        authorization: 'Basic test-token',
      });

      await expect(guard.canActivate(context)).rejects.toThrow(
        new HttpException(
          'Token de autenticação não fornecido.',
          HttpStatus.UNAUTHORIZED,
        ),
      );
    });
  });
});
