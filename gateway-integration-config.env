# Gateway Configuration (projeto separado)
PORT=4020
NODE_ENV=staging

# Gateway Info
GATEWAY_NAME=PAM Casino API Gateway
GATEWAY_VERSION=1.0.0
GATEWAY_LOG_LEVEL=info

# PAM Casino API Configuration (sua API atual)
CASINO_API_URL=http://api-pam-casino:4002
CASINO_API_TIMEOUT=10000
CASINO_API_RETRIES=3
CASINO_API_KEY=ed2ddb7524e5b0ec4b263f11a99369b7b437a63137eea9bcee24effc405dbd23

# EGT Provider (usando suas configs existentes)
EGT_ENABLED=true
EGT_API_TOKEN=your_egt_token_from_gamelaunch
EGT_API_URL=https://cf8npe94euwae7hdringzdbjaxtr91p4quznbtw6bt74pv3j-h2br-api-uat.egt-digital.com/api
EGT_TIMEOUT=5000
EGT_RETRIES=3
EGT_RATE_LIMIT=100

# BGaming Provider
BGAMING_ENABLED=true
BGAMING_API_KEY=your_bgaming_api_key
BGAMING_SECRET_KEY=your_bgaming_secret_key
BGAMING_API_URL=https://api.bgaming.com
BGAMING_TIMEOUT=5000
BGAMING_RETRIES=3
BGAMING_RATE_LIMIT=200

# Security
JWT_SECRET=c6a5afddd29e348e4573ba9b6f2099bc09ca7ea706bf2071b556313b6d0e591a
API_KEY=ed2ddb7524e5b0ec4b263f11a99369b7b437a63137eea9bcee24effc405dbd23

# Database (opcional para logs do gateway)
REDIS_URL=redis://redis:6379

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
