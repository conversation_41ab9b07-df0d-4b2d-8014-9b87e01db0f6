import { Test, TestingModule } from '@nestjs/testing';
import { HistoryGameService } from './history-game.service';
import { EntityManager } from 'typeorm';
import { DateUtilsService } from '@/common/utils/date';

describe('HistoryGameService', () => {
  let service: HistoryGameService;

  const mockEntityManager = {};
  const mockDateUtilsService = {};

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HistoryGameService,
        { provide: EntityManager, useValue: mockEntityManager },
        { provide: DateUtilsService, useValue: mockDateUtilsService },
      ],
    }).compile();

    service = module.get<HistoryGameService>(HistoryGameService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
}); 