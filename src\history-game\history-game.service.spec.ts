import { Test, TestingModule } from '@nestjs/testing';
import { HistoryGameService } from './history-game.service';
import { EntityManager } from 'typeorm';
import { DateUtilsService } from '@/common/utils/date';
import { HttpException, Logger } from '@nestjs/common';
import { GenericFilter } from '@/common/filters/generic-filter.dto';
import { SortOrder } from '@/common/filters/sortOrder';
import { HistoryGameEntity } from './entities/history-game.entity';

describe('HistoryGameService', () => {
  let service: HistoryGameService;
  let entityManager: jest.Mocked<EntityManager>;
  let dateService: jest.Mocked<DateUtilsService>;

  const mockQueryBuilder = {
    select: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    groupBy: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    getRawMany: jest.fn(),
    getRawOne: jest.fn(),
  };

  const mockEntityManager = {
    createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
  };

  const mockDateUtilsService = {
    getDateRange: jest.fn(),
  };

  const mockRequest = {
    headers: {
      'partner-id': 'test-partner-id',
    },
  } as any;

  const mockFilter: GenericFilter = {
    page: 1,
    pageSize: 10,
    orderBy: 'game_name',
    sortOrder: SortOrder.ASC,
    partners: 'test-partner',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HistoryGameService,
        { provide: EntityManager, useValue: mockEntityManager },
        { provide: DateUtilsService, useValue: mockDateUtilsService },
      ],
    }).compile();

    service = module.get<HistoryGameService>(HistoryGameService);
    entityManager = module.get(EntityManager);
    dateService = module.get(DateUtilsService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getHistoryGame', () => {
    const mockHistoryData = [
      {
        game_id: 'game-1',
        game_name: 'Test Game 1',
        game_provider: 'Provider A',
        currency: 'USD',
        btag: 'test-btag',
        region: 'US',
        partner_id: 'partner-1',
        total_bet: '1000.00',
        total_settle: '950.00',
        total_round: '10',
        ggr: '50.00',
        profitability: '-5.00',
      },
      {
        game_id: 'game-2',
        game_name: 'Test Game 2',
        game_provider: 'Provider B',
        currency: 'EUR',
        btag: 'test-btag-2',
        region: 'EU',
        partner_id: 'partner-1',
        total_bet: '2000.00',
        total_settle: '1800.00',
        total_round: '20',
        ggr: '200.00',
        profitability: '-10.00',
      },
    ];

    const mockSummaryData = {
      total_bet: '3000.00',
      total_settle: '2750.00',
      total_round: '30',
      ggr: '250.00',
      profitability: '-8.33',
    };

    beforeEach(() => {
      dateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-31'),
      });
    });

    it('should return history game data with summary successfully', async () => {
      // Arrange
      mockQueryBuilder.getRawMany.mockResolvedValue(mockHistoryData);
      mockQueryBuilder.getRawOne.mockResolvedValue(mockSummaryData);

      // Act
      const result = await service.getHistoryGame(
        'test-player-id',
        mockFilter,
        '2024-01-01',
        '2024-01-31',
        mockRequest,
      );

      // Assert
      expect(result).toEqual({
        data: mockHistoryData,
        summary: {
          totalBet: 3000,
          totalSettle: 2750,
          totalRound: 30,
          ggr: 250,
          profitability: -8.33,
        },
      });

      expect(dateService.getDateRange).toHaveBeenCalledWith(
        '2024-01-01',
        '2024-01-31',
      );
      expect(entityManager.createQueryBuilder).toHaveBeenCalledWith(
        HistoryGameEntity,
        'h',
      );
      expect(mockQueryBuilder.select).toHaveBeenCalledTimes(2);
      expect(mockQueryBuilder.where).toHaveBeenCalledTimes(2);
      expect(mockQueryBuilder.groupBy).toHaveBeenCalledWith('h.game_id');
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        'game_name',
        SortOrder.ASC,
      );
    });

    it('should handle empty player ID', async () => {
      // Arrange
      mockQueryBuilder.getRawMany.mockResolvedValue([]);
      mockQueryBuilder.getRawOne.mockResolvedValue({
        total_bet: '0.00',
        total_settle: '0.00',
        total_round: '0',
        ggr: '0.00',
        profitability: '0.00',
      });

      // Act
      const result = await service.getHistoryGame(
        '',
        mockFilter,
        '2024-01-01',
        '2024-01-31',
        mockRequest,
      );

      // Assert
      expect(result.data).toEqual([]);
      expect(result.summary.totalBet).toBe(0);
    });

    it('should handle missing partner ID in request headers', async () => {
      // Arrange
      const requestWithoutPartnerId = { headers: {} } as any;
      mockQueryBuilder.getRawMany.mockResolvedValue(mockHistoryData);
      mockQueryBuilder.getRawOne.mockResolvedValue(mockSummaryData);

      // Act
      const result = await service.getHistoryGame(
        'test-player-id',
        mockFilter,
        '2024-01-01',
        '2024-01-31',
        requestWithoutPartnerId,
      );

      // Assert
      expect(result).toBeDefined();
      expect(result.data).toEqual(mockHistoryData);
    });

    it('should use default orderBy and sortOrder when not provided in filter', async () => {
      // Arrange
      const filterWithoutOrder = {
        ...mockFilter,
        orderBy: undefined,
        sortOrder: undefined,
      };
      mockQueryBuilder.getRawMany.mockResolvedValue(mockHistoryData);
      mockQueryBuilder.getRawOne.mockResolvedValue(mockSummaryData);

      // Act
      await service.getHistoryGame(
        'test-player-id',
        filterWithoutOrder,
        '2024-01-01',
        '2024-01-31',
        mockRequest,
      );

      // Assert
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        'game_name',
        SortOrder.ASC,
      );
    });

    it('should handle custom orderBy and sortOrder from filter', async () => {
      // Arrange
      const customFilter = {
        ...mockFilter,
        orderBy: 'total_bet',
        sortOrder: SortOrder.DESC,
      };
      mockQueryBuilder.getRawMany.mockResolvedValue(mockHistoryData);
      mockQueryBuilder.getRawOne.mockResolvedValue(mockSummaryData);

      // Act
      await service.getHistoryGame(
        'test-player-id',
        customFilter,
        '2024-01-01',
        '2024-01-31',
        mockRequest,
      );

      // Assert
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        'total_bet',
        SortOrder.DESC,
      );
    });

    it('should handle null date range from dateService', async () => {
      // Arrange
      dateService.getDateRange.mockReturnValue({
        fromDate: null,
        toDate: null,
      });
      mockQueryBuilder.getRawMany.mockResolvedValue(mockHistoryData);
      mockQueryBuilder.getRawOne.mockResolvedValue(mockSummaryData);

      // Act
      const result = await service.getHistoryGame(
        'test-player-id',
        mockFilter,
        '',
        '',
        mockRequest,
      );

      // Assert
      expect(result).toBeDefined();
      expect(dateService.getDateRange).toHaveBeenCalledWith('', '');
    });

    it('should handle database query error and throw HttpException', async () => {
      // Arrange
      const errorMessage = 'Database connection failed';
      mockQueryBuilder.getRawMany.mockRejectedValue(new Error(errorMessage));

      // Act & Assert
      await expect(
        service.getHistoryGame(
          'test-player-id',
          mockFilter,
          '2024-01-01',
          '2024-01-31',
          mockRequest,
        ),
      ).rejects.toThrow(HttpException);

      const thrownError = await service
        .getHistoryGame(
          'test-player-id',
          mockFilter,
          '2024-01-01',
          '2024-01-31',
          mockRequest,
        )
        .catch(err => err);

      expect(thrownError).toBeInstanceOf(HttpException);
      expect(thrownError.getResponse()).toEqual({
        errorCode: 2,
        errorMsg: 'Erro ao buscar histórico de jogos',
      });
    });

    it('should handle summary query error and throw HttpException', async () => {
      // Arrange
      mockQueryBuilder.getRawMany.mockResolvedValue(mockHistoryData);
      mockQueryBuilder.getRawOne.mockRejectedValue(
        new Error('Summary query failed'),
      );

      // Act & Assert
      await expect(
        service.getHistoryGame(
          'test-player-id',
          mockFilter,
          '2024-01-01',
          '2024-01-31',
          mockRequest,
        ),
      ).rejects.toThrow(HttpException);
    });

    it('should parse numeric values correctly in summary', async () => {
      // Arrange
      const summaryWithStrings = {
        total_bet: '1500.50',
        total_settle: '1400.25',
        total_round: '15',
        ggr: '100.25',
        profitability: '-6.68',
      };

      mockQueryBuilder.getRawMany.mockResolvedValue(mockHistoryData);
      mockQueryBuilder.getRawOne.mockResolvedValue(summaryWithStrings);

      // Act
      const result = await service.getHistoryGame(
        'test-player-id',
        mockFilter,
        '2024-01-01',
        '2024-01-31',
        mockRequest,
      );

      // Assert
      expect(result.summary).toEqual({
        totalBet: 1500.5,
        totalSettle: 1400.25,
        totalRound: 15,
        ggr: 100.25,
        profitability: -6.68,
      });
    });

    it('should handle NaN values in summary parsing', async () => {
      // Arrange
      const summaryWithInvalidValues = {
        total_bet: 'invalid',
        total_settle: null,
        total_round: undefined,
        ggr: '',
        profitability: 'NaN',
      };

      mockQueryBuilder.getRawMany.mockResolvedValue([]);
      mockQueryBuilder.getRawOne.mockResolvedValue(summaryWithInvalidValues);

      // Act
      const result = await service.getHistoryGame(
        'test-player-id',
        mockFilter,
        '2024-01-01',
        '2024-01-31',
        mockRequest,
      );

      // Assert
      expect(result.summary.totalBet).toBeNaN();
      expect(result.summary.totalSettle).toBeNaN();
      expect(result.summary.totalRound).toBeNaN();
      expect(result.summary.ggr).toBeNaN();
      expect(result.summary.profitability).toBeNaN();
    });

    it('should build correct where conditions with all parameters', async () => {
      // Arrange
      mockQueryBuilder.getRawMany.mockResolvedValue(mockHistoryData);
      mockQueryBuilder.getRawOne.mockResolvedValue(mockSummaryData);

      // Act
      await service.getHistoryGame(
        'test-player-id',
        mockFilter,
        '2024-01-01',
        '2024-01-31',
        mockRequest,
      );

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        expect.objectContaining({
          partner_id: 'test-partner-id',
          player_id: 'test-player-id',
        }),
      );
    });

    it('should build where conditions without player ID when empty', async () => {
      // Arrange
      mockQueryBuilder.getRawMany.mockResolvedValue([]);
      mockQueryBuilder.getRawOne.mockResolvedValue(mockSummaryData);

      // Act
      await service.getHistoryGame(
        '',
        mockFilter,
        '2024-01-01',
        '2024-01-31',
        mockRequest,
      );

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        expect.objectContaining({
          partner_id: 'test-partner-id',
        }),
      );
      expect(mockQueryBuilder.where).not.toHaveBeenCalledWith(
        expect.objectContaining({
          player_id: expect.anything(),
        }),
      );
    });

    it('should log the start of the operation', async () => {
      // Arrange
      const loggerSpy = jest
        .spyOn(Logger.prototype, 'log')
        .mockImplementation();
      mockQueryBuilder.getRawMany.mockResolvedValue(mockHistoryData);
      mockQueryBuilder.getRawOne.mockResolvedValue(mockSummaryData);

      // Act
      await service.getHistoryGame(
        'test-player-id',
        mockFilter,
        '2024-01-01',
        '2024-01-31',
        mockRequest,
      );

      // Assert
      expect(loggerSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          '[Inicio] Find history game agrupado por game_id:',
        ),
      );

      loggerSpy.mockRestore();
    });

    it('should log error when exception occurs', async () => {
      // Arrange
      const loggerSpy = jest
        .spyOn(Logger.prototype, 'error')
        .mockImplementation();
      const errorMessage = 'Test error';
      mockQueryBuilder.getRawMany.mockRejectedValue(new Error(errorMessage));

      // Act & Assert
      await expect(
        service.getHistoryGame(
          'test-player-id',
          mockFilter,
          '2024-01-01',
          '2024-01-31',
          mockRequest,
        ),
      ).rejects.toThrow();

      expect(loggerSpy).toHaveBeenCalledWith(
        expect.stringContaining('[Error] Find history game:'),
      );

      loggerSpy.mockRestore();
    });
  });
});
