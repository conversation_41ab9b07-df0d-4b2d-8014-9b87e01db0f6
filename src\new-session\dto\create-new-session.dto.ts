import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class CreateNewSessionDto {
  @ApiProperty({
    description: 'ID da sessão atual',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: true,
  })
  @IsString()
  sessionId: string;

  @ApiProperty({
    description: 'ID da nova sessão',
    example: '123e4567-e89b-12d3-a456-426614174001',
    required: true,
  })
  @IsString()
  newSessionId: string;

  @ApiProperty({
    description: 'ID do jogador',
    example: '123e4567-e89b-12d3-a456-426614174002',
    required: true,
  })
  @IsString()
  playerId: string;

  @ApiProperty({
    description: 'ID do jogo',
    example: '123e4567-e89b-12d3-a456-426614174003',
    required: true,
  })
  @IsString()
  gameId: string;
}
