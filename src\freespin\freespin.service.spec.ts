import { Test, TestingModule } from '@nestjs/testing';
import { FreespinService } from './freespin.service';
import { HttpService } from '@nestjs/axios';
import { of, throwError } from 'rxjs';
import { HttpException } from '@nestjs/common';

describe('FreespinService', () => {
  let service: FreespinService;
  let httpService: { post: jest.Mock };

  const GATEWAY_URL = 'http://gateway.test';

  beforeAll(() => {
    process.env.GATEWAY_URL = GATEWAY_URL;
  });

  beforeEach(async () => {
    httpService = { post: jest.fn() };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FreespinService,
        { provide: HttpService, useValue: httpService },
      ],
    }).compile();

    service = module.get<FreespinService>(FreespinService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('finish should return static balance', async () => {
    const dto: any = { campaignId: 'c1' };
    await expect(service.finish(dto)).resolves.toEqual({ balance: '0.01' });
  });

  it('issue should POST to gateway and return data', async () => {
    const dto: any = { userId: 'u1', spins: 10 };
    const data = { issued: true };
    httpService.post.mockReturnValue(of({ data }));

    await expect(service.issue(dto)).resolves.toEqual(data);
    expect(httpService.post).toHaveBeenCalledWith(
      `${GATEWAY_URL}/casino/freespin/issue`,
      dto,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-aggregator': 'softswiss',
        },
      },
    );
  });

  it('cancel should POST to gateway and return data', async () => {
    const dto: any = { campaignId: 'c1' };
    const data = { cancelled: true };
    httpService.post.mockReturnValue(of({ data }));

    await expect(service.cancel(dto)).resolves.toEqual(data);
    expect(httpService.post).toHaveBeenCalledWith(
      `${GATEWAY_URL}/casino/freespin/cancel`,
      dto,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-aggregator': 'softswiss',
        },
      },
    );
  });

  it('cancel should wrap http errors in HttpException', async () => {
    const dto: any = { campaignId: 'c1' };
    const error: any = { message: 'upstream error', status: 500 };
    httpService.post.mockReturnValue(throwError(() => error));

    await expect(service.cancel(dto)).rejects.toBeInstanceOf(HttpException);
    await expect(service.cancel(dto)).rejects.toMatchObject({
      message: error.message,
    });
  });
});
