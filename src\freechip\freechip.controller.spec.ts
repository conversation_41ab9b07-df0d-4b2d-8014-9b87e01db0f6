import { Test, TestingModule } from '@nestjs/testing';
import { FreechipController } from './freechip.controller';
import { FreechipService } from './freechip.service';

describe('FreechipController', () => {
  let controller: FreechipController;
  let service: { finish: jest.Mock; cancel: jest.Mock; issue: jest.Mock };

  beforeEach(async () => {
    service = {
      finish: jest.fn(),
      cancel: jest.fn(),
      issue: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [FreechipController],
      providers: [{ provide: FreechipService, useValue: service }],
    }).compile();

    controller = module.get<FreechipController>(FreechipController);
  });

  it('finish should call service.finish and return result', async () => {
    const dto: any = { userId: 'u1', amount: '5' };
    const result = { balance: '0.01' };
    service.finish.mockResolvedValue(result);

    await expect(controller.finish(dto)).resolves.toEqual(result);
    expect(service.finish).toHaveBeenCalledWith(dto);
  });

  it('cancel should call service.cancel and return result', async () => {
    const dto: any = { promotionId: 'p1' };
    const result = { cancelled: true };
    service.cancel.mockResolvedValue(result);

    await expect(controller.cancel(dto)).resolves.toEqual(result);
    expect(service.cancel).toHaveBeenCalledWith(dto);
  });

  it('issue should call service.issue and return result', async () => {
    const dto: any = { userId: 'u1', amount: '5' };
    const result = { issued: true };
    service.issue.mockResolvedValue(result);

    await expect(controller.issue(dto)).resolves.toEqual(result);
    expect(service.issue).toHaveBeenCalledWith(dto);
  });
});
