import { ApiProperty } from '@nestjs/swagger';

export class BackofficeCasinoGamesResponseDto {
  @ApiProperty({ description: 'ID do jogo' })
  id: string;

  @ApiProperty({ description: 'ID do jogo no provedor' })
  gameId: string;

  @ApiProperty({ description: 'Nome do jogo' })
  name: string;

  @ApiProperty({ description: 'Provedor do jogo' })
  gameProvider: string;

  @ApiProperty({ description: 'descrição do jogo' })
  description: string;
}

export const CasinoGamesResponseDtoExample = {
  id: 'game-123',
  gameId: '12345',
  name: 'Fortune Tiger',
  gameProvider: 'Pragmatic Play',
  description: 'burning_fort',
};
