import { ApiProperty } from '@nestjs/swagger';

export class DailyReportProvidersResponseDto {
  @ApiProperty({ description: 'Provedor do jogo' })
  gameProvider: string;

  @ApiProperty({ description: 'ID do parceiro' })
  partnerId: string;

  @ApiProperty({ description: 'Total de apostas' })
  totalBet: number;

  @ApiProperty({ description: 'Total de ganhos' })
  totalSettle: number;

  @ApiProperty({ description: 'Total de rodadas' })
  totalRound: number;

  @ApiProperty({ description: 'Moeda' })
  currency: string;

  @ApiProperty({ description: 'GGR (Gross Gaming Revenue)' })
  ggr: number;

  @ApiProperty({ description: 'Data do relatório' })
  date: Date;
}
