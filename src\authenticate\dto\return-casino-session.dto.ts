import { CasinoSessionEntity } from '@/common/entities/casino-session.entity';
export class ReturnCasinoSession {
  id: string;
  sessionAuthId: string;
  statusId: number;
  gameId: string;
  playerId: string;
  requestId: string;
  launchToken: string;
  launchUrl: string;
  gameMode: string;
  token: string;
  createdAt: Date;
  updatedAt: Date;
  lastUserUpdated: Date;
  errorCode: string;
  errorMsg: string;

  constructor(casinoSessionEntity: CasinoSessionEntity) {
    this.id = casinoSessionEntity.id;
    this.sessionAuthId = casinoSessionEntity.sessionAuthId;
    this.statusId = casinoSessionEntity.statusId;
    this.gameId = casinoSessionEntity.gameId;
    this.playerId = casinoSessionEntity.playerId;
    this.requestId = casinoSessionEntity.requestId;
    this.launchToken = casinoSessionEntity.launchToken;
    this.launchUrl = casinoSessionEntity.launchUrl;
    this.gameMode = casinoSessionEntity.gameMode;
    this.token = casinoSessionEntity.token;
    this.createdAt = casinoSessionEntity.createdAt;
    this.updatedAt = casinoSessionEntity.updatedAt;
    this.lastUserUpdated = casinoSessionEntity.lastUserUpdated;
    this.errorCode = casinoSessionEntity.errorCode;
    this.errorMsg = casinoSessionEntity.errorMsg;
  }
}
