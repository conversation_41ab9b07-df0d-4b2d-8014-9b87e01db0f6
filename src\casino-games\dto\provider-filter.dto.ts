import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { GenericFilter } from '@/common/filters/generic-filter.dto';

export class ProviderFilterDto extends GenericFilter {
  @ApiPropertyOptional({
    description: 'Filtro por ID do provedor',
    example: 'a1b2c3d4-e5f6-4a5b-8c9d-0e1f2a3b4c5d',
  })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiPropertyOptional({
    description: 'Filtro por nome do provedor (busca parcial)',
    example: 'PRAGMATIC',
  })
  @IsOptional()
  @IsString()
  name?: string;
}

