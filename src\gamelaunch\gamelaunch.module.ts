import { <PERSON>du<PERSON> } from '@nestjs/common';
import { GameLaunchController } from './gamelaunch.controller';
import { HMACModule } from '@/hmac/hmac.module';
import { JwtModule } from '@nestjs/jwt';
import { DatabaseModule } from '@/common/database/database.module';
import { GameLaunchService } from './gamelaunch.service';
import { WalletService } from '@/wallet/wallet.service';
import { HttpModule } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { PlayerService } from '@/player/player.service';
import { PartnerService } from '@/partner/partner.service';

const modules = [HMACModule, JwtModule, DatabaseModule, HttpModule];

@Module({
  imports: [...modules],
  providers: [
    GameLaunchService,
    WalletService,
    ConfigService,
    PlayerService,
    PartnerService,
  ],
  controllers: [GameLaunchController],
})
export class GameLaunchModule {}
