import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsUUID } from 'class-validator';

export class PromoBetDto {
  @ApiProperty({ example: '0.01' })
  @IsString()
  amount: string;

  @ApiProperty({ example: 'EUR' })
  @IsString()
  currency: string;

  @ApiProperty({ example: '7b0ad440-5433-441a-a3c2-855b7fd2dddd' })
  @IsUUID()
  event_id: string;

  @ApiProperty({ example: 'Tip' })
  @IsString()
  event_type: string;

  @ApiProperty({ example: '1fb7a3e6-6d9a-4e0a-aff9-9b94e9382795' })
  @IsUUID()
  id: string;

  @ApiProperty({ example: '0b7ad2b3-75c4-48d4-8c29-f1766d0fc574' })
  @IsUUID()
  player_id: string;

  @ApiProperty({ example: 'sample_provider' })
  @IsString()
  provider: string;
}
