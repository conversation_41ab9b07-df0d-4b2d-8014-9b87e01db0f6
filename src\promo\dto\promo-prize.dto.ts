import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsUUID } from 'class-validator';

export class PromoPrizeDto {
  @ApiProperty({ example: '16ba00f3-e942-4f6b-acf4-32eb62cc7b4b' })
  @IsUUID()
  event_id: string;

  @ApiProperty({ example: 'Tournament' })
  @IsString()
  event_type: string;

  @ApiProperty({ example: '9b41d5fc-f4f7-4588-bdd7-8b8f3b220946' })
  @IsUUID()
  id: string;

  @ApiProperty({ example: 'be7db17e-9e8a-4cfa-b124-ee81ae9315e6' })
  @IsUUID()
  player_id: string;

  @ApiProperty({ example: '10 Gems' })
  @IsString()
  prize: string;

  @ApiProperty({ example: 'sample_provider' })
  @IsString()
  provider: string;
}
