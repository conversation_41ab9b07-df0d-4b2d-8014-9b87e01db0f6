import { Test, TestingModule } from '@nestjs/testing';
import { PlayerService } from './player.service';
import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { PlayerResponseDto } from './dto/player-response.dto';

jest.mock('rxjs', () => ({
  ...jest.requireActual('rxjs'),
  firstValueFrom: jest.fn(),
}));

describe('PlayerService', () => {
  let service: PlayerService;
  let httpService: HttpService;

  const mockHttpService = {
    get: jest.fn(),
  };

  const mockRequest = {
    headers: {
      authorization: 'Bearer test-token',
      'x-user-context': 'user-context-token',
    },
  };

  beforeEach(async () => {
    // Mock environment variable BEFORE creating the module
    process.env.API_PLAYER = 'http://api-player.test';

    (firstValueFrom as jest.Mock).mockReset();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PlayerService,
        { provide: HttpService, useValue: mockHttpService },
      ],
    }).compile();

    service = module.get<PlayerService>(PlayerService);
    httpService = module.get<HttpService>(HttpService);
    jest.clearAllMocks();
  });

  afterEach(() => {
    delete process.env.API_PLAYER;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getPlayerById', () => {
    it('deve retornar informações do player com sucesso', async () => {
      const playerId = 'player-123';
      const mockPlayerData = {
        id: playerId,
        name: 'Test Player',
        email: '<EMAIL>',
      } as any as PlayerResponseDto;

      const mockResponse = {
        data: mockPlayerData,
      };

      (firstValueFrom as jest.Mock).mockResolvedValue(mockResponse);

      const result = await service.getPlayerById(playerId, mockRequest as any);

      expect(result).toEqual(mockPlayerData);
      expect(httpService.get).toHaveBeenCalledWith(
        `http://api-player.test/v1/pam/player/${playerId}/id`,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer test-token',
            'x-user-context': 'user-context-token',
          },
        },
      );
      expect(firstValueFrom).toHaveBeenCalled();
    });

    it('deve lançar HttpException quando player não encontrado', async () => {
      const playerId = 'invalid-player';
      const errorResponse = {
        response: {
          data: { message: 'Player not found' },
          status: 404,
        },
      };

      (firstValueFrom as jest.Mock).mockRejectedValue(errorResponse);

      await expect(
        service.getPlayerById(playerId, mockRequest as any),
      ).rejects.toThrow(
        new HttpException('Player not found', HttpStatus.NOT_FOUND),
      );
    });

    it('deve lançar HttpException com mensagem padrão quando erro não tem response.data', async () => {
      const playerId = 'player-456';
      const errorResponse = {
        response: {
          status: 500,
        },
      };

      (firstValueFrom as jest.Mock).mockRejectedValue(errorResponse);

      await expect(
        service.getPlayerById(playerId, mockRequest as any),
      ).rejects.toThrow(
        new HttpException(
          'Erro ao buscar informações do jogador',
          HttpStatus.INTERNAL_SERVER_ERROR,
        ),
      );
    });

    it('deve lançar HttpException com status INTERNAL_SERVER_ERROR quando erro não tem status', async () => {
      const playerId = 'player-789';
      const errorResponse = {
        response: {
          data: { message: 'Unknown error' },
        },
      };

      (firstValueFrom as jest.Mock).mockRejectedValue(errorResponse);

      await expect(
        service.getPlayerById(playerId, mockRequest as any),
      ).rejects.toThrow(
        new HttpException('Unknown error', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });

    it('deve lançar HttpException quando ocorrer erro de rede', async () => {
      const playerId = 'player-999';
      const networkError = new Error('Network error');

      (firstValueFrom as jest.Mock).mockRejectedValue(networkError);

      await expect(
        service.getPlayerById(playerId, mockRequest as any),
      ).rejects.toThrow(HttpException);
    });

    it('deve incluir headers corretos na requisição', async () => {
      const playerId = 'player-abc';
      const customRequest = {
        headers: {
          authorization: 'Bearer custom-token',
          'x-user-context': 'custom-context',
        },
      };

      const mockResponse = {
        data: { id: playerId, name: 'Test' } as any as PlayerResponseDto,
      };

      (firstValueFrom as jest.Mock).mockResolvedValue(mockResponse);

      await service.getPlayerById(playerId, customRequest as any);

      expect(httpService.get).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer custom-token',
            'x-user-context': 'custom-context',
          },
        }),
      );
    });
  });
});
