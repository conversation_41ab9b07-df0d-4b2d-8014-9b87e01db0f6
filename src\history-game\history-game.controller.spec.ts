import { Test, TestingModule } from '@nestjs/testing';
import { HistoryGameController } from './history-game.controller';
import { HistoryGameService } from './history-game.service';
import { BackofficeGuard } from '@/common/guards/backoffice/backoffice.guard';
import { JwtService } from '@nestjs/jwt';

describe('HistoryGameController', () => {
  let controller: HistoryGameController;

  const mockHistoryGameService = {};
  const mockBackofficeGuard = { canActivate: jest.fn(() => true) };
  const mockJwtService = {};

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [HistoryGameController],
      providers: [
        { provide: HistoryGameService, useValue: mockHistoryGameService },
        { provide: JwtService, useValue: mockJwtService },
      ],
    })
      .overrideGuard(BackofficeGuard)
      .useValue(mockBackofficeGuard)
      .compile();

    controller = module.get<HistoryGameController>(HistoryGameController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
