import { SUCCESS, ALREADY_PROCESSED, GENERIC_ERROR, PLAYER_NOT_FOUND, SESSION_NOT_FOUND, GAME_NOT_FOUND, ROUND_NOT_FOUND } from "@/common/constants/message-codes";
import { SwaggerApiCodeResponses } from "@/common/decorators/swagger-api-code-responses";
import { ApiPartnerIdHeader, ApiClientIpHeader } from "@/common/decorators/swagger.decorator";
import { BackofficeGuard } from "@/common/guards/backoffice/backoffice.guard";
import { UseGuards, Controller, Post, HttpCode, Body, Get, Req, Param, Patch, Delete } from "@nestjs/common";
import { ApiTags, ApiForbiddenResponse, ApiInternalServerErrorResponse, ApiBearerAuth, ApiOperation, ApiResponse } from "@nestjs/swagger";
import { BackofficeCasinoGamesResponseDto } from "../dto/backoffice-games-response.dto";
import { CreateCasinoGameDto, CreateCasinoGameDtoExample } from "../dto/create-casino-game.dto";
import { UpdateCasinoGameDto } from "../dto/update-casino-game.dto";
import { CasinoGames } from "../entities/casino-game.entity";
import { BackofficeCasinoGamesService } from "../services/backoffice-casino-games.service";

@ApiTags('Backoffice Casino Games')
@ApiPartnerIdHeader()
@ApiClientIpHeader()
@ApiForbiddenResponse()
@ApiInternalServerErrorResponse()
@UseGuards(BackofficeGuard)
@ApiBearerAuth('access-token')
@Controller('backoffice/casino-games')
export class BackofficeCasinoGamesController {
  constructor(
    private readonly casinoGamesService: BackofficeCasinoGamesService
  ) {}

  @Post()
  @ApiOperation({
    summary: 'Criar jogo',
    description: 'Endpoint para criar um novo jogo de casino',
  })
  @SwaggerApiCodeResponses(
    201,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    CreateCasinoGameDto,
    CreateCasinoGameDtoExample
  )
  @HttpCode(201)
  create(@Body() createCasinoGameDto: CreateCasinoGameDto): Promise<string> {
    return this.casinoGamesService.create(createCasinoGameDto);
  }

  @ApiOperation({
    summary: 'Obter todos os provedores de jogos',
    description:
      'Retorna uma lista com todos os provedores de jogos disponíveis na plataforma. Requer autenticação de backoffice.',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de provedores recuperada com sucesso',
    content: {
      'application/json': {
        example: ['PRAGMATIC', 'BETSOFT', 'AGS', 'EVO', 'BETRIVER', 'BETKING', 'BETWIN', 'BETRIVER', 'BETKING', 'BETWIN'], // TODO: Criar um enum com os provedores disponíveis
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Requisição inválida',
    content: {
      'application/json': {
        example: {
          statusCode: 400,
          message: 'Parâmetros inválidos',
          error: 'Bad Request',
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Não autorizado - Credenciais de backoffice inválidas',
    content: {
      'application/json': {
        example: {
          statusCode: 401,
          message: 'Não autorizado',
          error: 'Unauthorized',
        },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Erro interno do servidor',
    content: {
      'application/json': {
        example: {
          statusCode: 500,
          message: 'Erro interno do servidor',
          error: 'Internal Server Error',
        },
      },
    },
  })
  @Get('providers')
  async getAllProvides() {
    return await this.casinoGamesService.getAllProvides();
  }

  @Get()
  @ApiOperation({
    summary: 'Listar jogos',
    description: 'Endpoint para listar todos os jogos de casino com paginação',
  })
  @ApiResponse({
    status: 200,
    description: 'Jogos encontrados com sucesso',
    type: BackofficeCasinoGamesResponseDto,
  })
  @ApiResponse({
    status: 204,
    description: 'Nenhum jogo encontrado',
  })
  @HttpCode(200)
  findAll(@Req() req): Promise<CasinoGames[]> {
    return this.casinoGamesService.findAll(req);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Buscar jogo',
    description: 'Endpoint para buscar um jogo específico por ID',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    CreateCasinoGameDto,
    CreateCasinoGameDtoExample
  )
  @ApiResponse({
    status: 200,
    description: 'Jogo encontrado com sucesso',
    type: CreateCasinoGameDto,
  })
  @ApiResponse({
    status: 204,
    description: 'Jogo não encontrado',
  })
  @HttpCode(200)
  findOne(@Param('id') id: string): Promise<CasinoGames> {
    return this.casinoGamesService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Atualizar jogo',
    description: 'Endpoint para atualizar um jogo existente',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    CreateCasinoGameDto,
    CreateCasinoGameDtoExample
  )
  @HttpCode(200)
  update(
    @Param('id') id: string,
    @Body() updateCasinoGameDto: UpdateCasinoGameDto
  ) {
    return this.casinoGamesService.update(id, updateCasinoGameDto);
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Remover jogo',
    description: 'Endpoint para remover um jogo',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    null,
    null
  )
  @HttpCode(200)
  remove(@Param('id') id: string) {
    return this.casinoGamesService.remove(id);
  }
}