import {
  ALREADY_PROCESSED,
  GAME_NOT_FOUND,
  GENERIC_ERROR,
  PLAYER_NOT_FOUND,
  ROUND_NOT_FOUND,
  SESSION_NOT_FOUND,
  SUCCESS,
} from '@/common/constants/message-codes';
import { SwaggerApiCodeResponses } from '@/common/decorators/swagger-api-code-responses';
import {
  ApiClientIpHeader,
  ApiPartnerIdHeader,
  ApiPaginationQuery,
} from '@/common/decorators/swagger.decorator';
import { PaginationResponseDto, PaginatedResponseDtoExample } from '@/common/dto/paginated-response.dto';
import { KeycloakBackofficeGuard } from '@/common/guards/keycloak/keycloak-backoffice.guard';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiConsumes,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { BackofficeCasinoGamesResponseDto } from '../dto/backoffice-games-response.dto';
import {
  CreateCasinoGameDto,
  CreateCasinoGameDtoExample,
} from '../dto/create-casino-game.dto';
import { ProviderFilterDto } from '../dto/provider-filter.dto';
import { UpdateCasinoGameDto } from '../dto/update-casino-game.dto';
import { CasinoGames } from '../entities/casino-game.entity';
import { BackofficeCasinoGamesService } from '../services/backoffice-casino-games.service';

@ApiTags('Backoffice Casino Games')
@ApiPartnerIdHeader()
@ApiClientIpHeader()
@ApiForbiddenResponse()
@ApiInternalServerErrorResponse()
@UseGuards(KeycloakBackofficeGuard)
// @ApiBearerAuth('access-token')
@Controller('backoffice/casino-games')
export class BackofficeCasinoGamesController {
  constructor(
    private readonly casinoGamesService: BackofficeCasinoGamesService,
  ) {}

  @Post()
  @UseInterceptors(FileInterceptor('image'))
  @ApiConsumes('multipart/form-data', 'application/json')
  @ApiOperation({
    summary: 'Criar jogo',
    description:
      'Endpoint para criar um novo jogo de casino com upload de imagem. Aceita dois formatos: 1) multipart/form-data com arquivo de imagem no campo "image", ou 2) JSON com imagem em base64 no campo "imageBase64"',
  })
  @SwaggerApiCodeResponses(
    201,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    CreateCasinoGameDto,
    CreateCasinoGameDtoExample,
  )
  @HttpCode(201)
  create(
    @Body() createCasinoGameDto: CreateCasinoGameDto,
    @UploadedFile() image?: Express.Multer.File,
  ): Promise<{ message: string }> {
    return this.casinoGamesService.create(createCasinoGameDto, image);
  }

  @ApiOperation({
    summary: 'Obter todos os provedores de jogos',
    description:
      'Retorna uma lista paginada com todos os provedores de jogos disponíveis na plataforma. Suporta filtros opcionais por ID e nome. Requer autenticação de backoffice.',
  })
  @ApiPaginationQuery()
  @ApiQuery({
    name: 'id',
    required: false,
    type: String,
    description: 'Filtro por ID do provedor',
    example: 'a1b2c3d4-e5f6-4a5b-8c9d-0e1f2a3b4c5d',
  })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: 'Filtro por nome do provedor (busca parcial)',
    example: 'PRAGMATIC',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de provedores recuperada com sucesso',
    type: PaginationResponseDto,
    content: {
      'application/json': {
        example: PaginatedResponseDtoExample([
          { id: 'a1b2c3d4-e5f6-4a5b-8c9d-0e1f2a3b4c5d', name: 'PRAGMATIC' },
          { id: 'b2c3d4e5-f6a7-5b6c-9d0e-1f2a3b4c5d6e', name: 'BETSOFT' },
          { id: 'c3d4e5f6-a7b8-6c7d-0e1f-2a3b4c5d6e7f', name: 'AGS' },
          { id: 'd4e5f6a7-b8c9-7d8e-1f2a-3b4c5d6e7f8a', name: 'EVO' },
        ]),
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Requisição inválida',
    content: {
      'application/json': {
        example: {
          statusCode: 400,
          message: 'Parâmetros inválidos',
          error: 'Bad Request',
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Não autorizado - Credenciais de backoffice inválidas',
    content: {
      'application/json': {
        example: {
          statusCode: 401,
          message: 'Não autorizado',
          error: 'Unauthorized',
        },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Erro interno do servidor',
    content: {
      'application/json': {
        example: {
          statusCode: 500,
          message: 'Erro interno do servidor',
          error: 'Internal Server Error',
        },
      },
    },
  })
  @Get('providers')
  @HttpCode(200)
  async getAllProvides(
    @Query() filter: ProviderFilterDto,
  ): Promise<{
    data: Array<{ id: string; name: string }>;
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    return await this.casinoGamesService.getAllProvides(filter);
  }

  @Get()
  @ApiOperation({
    summary: 'Listar jogos',
    description: 'Endpoint para listar todos os jogos de casino com paginação',
  })
  @ApiResponse({
    status: 200,
    description: 'Jogos encontrados com sucesso',
    type: BackofficeCasinoGamesResponseDto,
  })
  @ApiResponse({
    status: 204,
    description: 'Nenhum jogo encontrado',
  })
  @HttpCode(200)
  findAll(@Req() req): Promise<CasinoGames[]> {
    return this.casinoGamesService.findAll(req);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Buscar jogo',
    description: 'Endpoint para buscar um jogo específico por ID',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    CreateCasinoGameDto,
    CreateCasinoGameDtoExample,
  )
  @ApiResponse({
    status: 200,
    description: 'Jogo encontrado com sucesso',
    type: CreateCasinoGameDto,
  })
  @ApiResponse({
    status: 204,
    description: 'Jogo não encontrado',
  })
  @HttpCode(200)
  findOne(@Param('id') id: string): Promise<CasinoGames> {
    return this.casinoGamesService.findOne(id);
  }

  @Patch(':id')
  @UseInterceptors(FileInterceptor('image'))
  @ApiConsumes('multipart/form-data', 'application/json')
  @ApiOperation({
    summary: 'Atualizar jogo',
    description:
      'Endpoint para atualizar um jogo existente com upload de imagem. Aceita dois formatos: 1) multipart/form-data com arquivo de imagem no campo "image", ou 2) JSON com imagem em base64 no campo "imageBase64"',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    CreateCasinoGameDto,
    CreateCasinoGameDtoExample,
  )
  @HttpCode(200)
  update(
    @Param('id') id: string,
    @Body() updateCasinoGameDto: UpdateCasinoGameDto,
    @UploadedFile() image?: Express.Multer.File,
  ) {
    return this.casinoGamesService.update(id, updateCasinoGameDto, image);
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Remover jogo',
    description: 'Endpoint para remover um jogo',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    null,
    null,
  )
  @HttpCode(200)
  remove(@Param('id') id: string) {
    return this.casinoGamesService.remove(id);
  }
}
