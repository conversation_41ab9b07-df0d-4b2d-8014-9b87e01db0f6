import { ApiProperty } from '@nestjs/swagger';

export class CreateSettleResponseDto {
  @ApiProperty({
    description: 'ID da transação',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: true,
  })
  casinoTransactionId: string;

  @ApiProperty({
    description: 'Valor da reconciliação',
    example: 100.5,
    required: true,
  })
  reconcileAmount: number;

  @ApiProperty({
    description: 'Saldo do jogador',
    example: 100.5,
    required: true,
  })
  balance: number;
}

export const CreateSettleResponseDtoExample = {
  casinoTransactionId: '123e4567-e89b-12d3-a456-426614174000',
  reconcileAmount: 100.5,
  balance: 100.5,
};
