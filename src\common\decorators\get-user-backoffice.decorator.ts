import {
  createParamDecorator,
  ExecutionContext,
  HttpException,
  HttpStatus,
} from '@nestjs/common';

export const UserBackOfficeFromToken = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();

    if (!request.user) {
      throw new HttpException(
        'Usuário não autenticado. Verifique se o KeycloakBackofficeGuard está ativo.',
        HttpStatus.UNAUTHORIZED,
      );
    }

    return request.user;
  },
);
