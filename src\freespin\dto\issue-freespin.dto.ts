// issue-freespin.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsDateString,
  IsEmail,
  IsInt,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';

class PlayerDto {
  @ApiProperty({ example: '458f80a6-c9e8-46b6-802f-83d284b23051' })
  @IsUUID()
  id: string;

  @ApiProperty({ example: 'string' })
  @IsString()
  walletId: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiProperty({ example: 'John' })
  @IsString()
  firstname: string;

  @ApiProperty({ example: 'Doe' })
  @IsString()
  lastname: string;

  @ApiProperty({ example: 'John.Doe' })
  @IsString()
  nickname: string;

  @ApiProperty({ example: '1999-12-31T15:30:00Z' })
  @IsDateString()
  dateOfBirth: string;

  @ApiProperty({ example: '2020-12-31T15:30:00Z' })
  @IsDateString()
  registeredAt: string;

  @ApiProperty({ example: 'DE' })
  @IsString()
  country: string;

  @ApiProperty({ example: ['vip'] })
  @IsArray()
  @IsString({ each: true })
  tags: string[];

  @ApiProperty({ example: 'EUR' })
  @IsString()
  currency: string;
}

export class IssueFreeSpinDto {
  @ApiProperty({ example: 'sample_casino' })
  @IsString()
  casinoId: string;

  @ApiProperty({ example: 'f500b45b-75e5-416d-94f9-10c2a2e804f1' })
  @IsUUID()
  issueId: string;

  @ApiProperty({ example: ['sample_provider:sample_game'] })
  @IsArray()
  @IsString({ each: true })
  games: string[];

  @ApiProperty({ example: 5 })
  @IsInt()
  freespinsQuantity: number;

  @ApiProperty({ example: 3 })
  @IsInt()
  betLevel: number;

  @ApiProperty({ example: '2025-12-31T15:30:00Z' })
  @IsDateString()
  validUntil: string;

  @ApiProperty({ type: PlayerDto })
  @ValidateNested()
  @Type(() => PlayerDto)
  player: PlayerDto;

  @ApiProperty({ example: 'string' })
  @IsOptional()
  @IsString()
  payload?: string;
}
