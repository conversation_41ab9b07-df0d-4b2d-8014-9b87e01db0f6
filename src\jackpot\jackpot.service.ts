import { HttpService } from '@nestjs/axios';
import { HttpException, Injectable, Logger } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { FeedJackpotDto } from './dto/feed-jackpot.dto';
import { ResponseJackpotFeedDto } from './dto/response-feed-jackpot.dto';

@Injectable()
export class JackpotService {
  private readonly logger = new Logger(JackpotService.name);
  constructor(private readonly httpService: HttpService) {}
  private urlGateway = process.env.GATEWAY_URL;

  async feed(feedJackpotDto: FeedJackpotDto): Promise<ResponseJackpotFeedDto> {
    try {
      this.logger.log('JackpotService', JSON.stringify(feedJackpotDto));
      const response = await firstValueFrom(
        this.httpService.post<ResponseJackpotFeedDto>(
          `${this.urlGateway}/casino/jackpot/feed`,
          feedJackpotDto,
          {
            headers: {
              'Content-Type': 'application/json',
              'x-aggregator': 'softswiss',
            },
          },
        ),
      );
      return response.data;
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(error.message, error.status);
    }
  }
}
