import { Test, TestingModule } from '@nestjs/testing';
import { FreespinController } from './freespin.controller';
import { FreespinService } from './freespin.service';

describe('FreespinController', () => {
  let controller: FreespinController;
  let service: { finish: jest.Mock; issue: jest.Mock; cancel: jest.Mock };

  beforeEach(async () => {
    service = {
      finish: jest.fn(),
      issue: jest.fn(),
      cancel: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [FreespinController],
      providers: [{ provide: FreespinService, useValue: service }],
    }).compile();

    controller = module.get<FreespinController>(FreespinController);
  });

  it('finish should call service.finish and return result', async () => {
    const dto: any = { campaignId: 'c1' };
    const result = { balance: '0.01' };
    service.finish.mockResolvedValue(result);

    await expect(controller.finish(dto)).resolves.toEqual(result);
    expect(service.finish).toHaveBeenCalledWith(dto);
  });

  it('issue should call service.issue and return result', async () => {
    const dto: any = { userId: 'u1', spins: 10 };
    const result = { issued: true };
    service.issue.mockResolvedValue(result);

    await expect(controller.issue(dto)).resolves.toEqual(result);
    expect(service.issue).toHaveBeenCalledWith(dto);
  });

  it('cancel should call service.cancel and return result', async () => {
    const dto: any = { campaignId: 'c1' };
    const result = { cancelled: true };
    service.cancel.mockResolvedValue(result);

    await expect(controller.cancel(dto)).resolves.toEqual(result);
    expect(service.cancel).toHaveBeenCalledWith(dto);
  });
});
