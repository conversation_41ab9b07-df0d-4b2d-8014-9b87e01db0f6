import { GameModeEnum } from '../enum/game-mode.enum';
import { GenderEnum } from '../enum/gender.enum';

export interface IAggregator {
  requestId?: string;
  partnerCloseUrl?: string;
  partnerCashierUrl?: string;
  gameId?: string;
  gameMode: GameModeEnum;
  playerId?: string;
  playerCurrency?: string;
  playerLanguage?: string;
  playerCountry?: string;
  playerFirstName?: string;
  playerLastName?: string;
  playerAlias?: string;
  playerGender?: GenderEnum;
  playerBalance?: string;
  playerEmail?: string;
}

export class Aggregator implements IAggregator {
  requestId: string;
  partnerCloseUrl: string;
  partnerCashierUrl?: string;
  gameId?: string;
  gameMode: GameModeEnum;
  playerId: string;
  playerCurrency: string;
  playerLanguage: string;
  playerCountry: string;
  playerFirstName?: string;
  playerLastName?: string;
  playerAlias?: string;
  playerGender?: GenderEnum;
  playerBalance: string;
  playerEmail?: string;

  constructor();
  constructor(aggregator: IAggregator);
  constructor(aggregator?: IAggregator) {
    this.requestId = aggregator?.requestId || '';
    this.partnerCloseUrl = aggregator?.partnerCloseUrl || '';
    this.gameId = aggregator?.gameId || '';
    this.gameMode = aggregator?.gameMode || GameModeEnum.REAL_PLAY;
    this.playerLanguage = aggregator?.playerLanguage || 'pt';
    this.playerCurrency = aggregator?.playerCurrency || 'BRL';
    this.playerCountry = aggregator?.playerCountry || 'BR';
    this.playerBalance = aggregator?.playerBalance || '0';
    this.playerId = aggregator?.playerId || '';
  }
}
