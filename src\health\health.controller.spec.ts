import { Test, TestingModule } from '@nestjs/testing';
import { HealthController } from './health.controller';
import { HealthCheckService, MemoryHealthIndicator, TypeOrmHealthIndicator } from '@nestjs/terminus';

describe('HealthController', () => {
  let controller: HealthController;
  let healthCheckService: HealthCheckService;
  let typeOrmHealthIndicator: TypeOrmHealthIndicator;
  let memoryHealthIndicator: MemoryHealthIndicator;

  const mockHealthCheckService = {
    check: jest.fn(),
  };
  const mockTypeOrmHealthIndicator = {
    pingCheck: jest.fn(),
  };
  const mockMemoryHealthIndicator = {
    checkHeap: jest.fn(),
    checkRSS: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [HealthController],
      providers: [
        { provide: HealthCheckService, useValue: mockHealthCheckService },
        { provide: TypeOrmHealthIndicator, useValue: mockTypeOrmHealthIndicator },
        { provide: MemoryHealthIndicator, useValue: mockMemoryHealthIndicator },
      ],
    }).compile();

    controller = module.get<HealthController>(HealthController);
    healthCheckService = module.get<HealthCheckService>(HealthCheckService);
    typeOrmHealthIndicator = module.get<TypeOrmHealthIndicator>(TypeOrmHealthIndicator);
    memoryHealthIndicator = module.get<MemoryHealthIndicator>(MemoryHealthIndicator);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('check', () => {
    it('deve retornar sucesso quando todos os health checks passam', async () => {
      const healthResult = {
        status: 'ok',
        info: {
          database: { status: 'up' },
          memory_heap: { status: 'up' },
          memory_rss: { status: 'up' },
        },
        error: {},
        details: {
          database: { status: 'up' },
          memory_heap: { status: 'up' },
          memory_rss: { status: 'up' },
        },
      };
      mockHealthCheckService.check.mockResolvedValue(healthResult);
      const result = await controller.check();
      expect(result).toEqual(healthResult);
      expect(healthCheckService.check).toHaveBeenCalledWith([
        expect.any(Function),
        expect.any(Function),
        expect.any(Function),
      ]);
    });

    it('deve retornar erro quando database health check falha', async () => {
      const healthResult = {
        status: 'error',
        info: {},
        error: {
          database: { status: 'down', message: 'Database connection failed' },
        },
        details: {
          database: { status: 'down', message: 'Database connection failed' },
          memory_heap: { status: 'up' },
          memory_rss: { status: 'up' },
        },
      };
      mockHealthCheckService.check.mockResolvedValue(healthResult);
      const result = await controller.check();
      expect(result).toEqual(healthResult);
      expect(result.status).toBe('error');
      expect(result.error.database).toBeDefined();
    });

    it('deve retornar erro quando memory heap health check falha', async () => {
      const healthResult = {
        status: 'error',
        info: {
          database: { status: 'up' },
        },
        error: {
          memory_heap: { status: 'down', message: 'Memory heap exceeded limit' },
        },
        details: {
          database: { status: 'up' },
          memory_heap: { status: 'down', message: 'Memory heap exceeded limit' },
          memory_rss: { status: 'up' },
        },
      };
      mockHealthCheckService.check.mockResolvedValue(healthResult);
      const result = await controller.check();
      expect(result).toEqual(healthResult);
      expect(result.status).toBe('error');
      expect(result.error.memory_heap).toBeDefined();
    });

    it('deve retornar erro quando memory RSS health check falha', async () => {
      const healthResult = {
        status: 'error',
        info: {
          database: { status: 'up' },
          memory_heap: { status: 'up' },
        },
        error: {
          memory_rss: { status: 'down', message: 'Memory RSS exceeded limit' },
        },
        details: {
          database: { status: 'up' },
          memory_heap: { status: 'up' },
          memory_rss: { status: 'down', message: 'Memory RSS exceeded limit' },
        },
      };
      mockHealthCheckService.check.mockResolvedValue(healthResult);
      const result = await controller.check();
      expect(result).toEqual(healthResult);
      expect(result.status).toBe('error');
      expect(result.error.memory_rss).toBeDefined();
    });

    it('deve retornar erro quando múltiplos health checks falham', async () => {
      const healthResult = {
        status: 'error',
        info: {},
        error: {
          database: { status: 'down', message: 'Database connection failed' },
          memory_heap: { status: 'down', message: 'Memory heap exceeded limit' },
          memory_rss: { status: 'down', message: 'Memory RSS exceeded limit' },
        },
        details: {
          database: { status: 'down', message: 'Database connection failed' },
          memory_heap: { status: 'down', message: 'Memory heap exceeded limit' },
          memory_rss: { status: 'down', message: 'Memory RSS exceeded limit' },
        },
      };
      mockHealthCheckService.check.mockResolvedValue(healthResult);
      const result = await controller.check();
      expect(result).toEqual(healthResult);
      expect(result.status).toBe('error');
      expect(Object.keys(result.error)).toHaveLength(3);
    });
  });
}); 