import { Test, TestingModule } from '@nestjs/testing';
import { HealthController } from './health.controller';
import { HealthCheckService, MemoryHealthIndicator, TypeOrmHealthIndicator } from '@nestjs/terminus';

describe('HealthController', () => {
  let controller: HealthController;
  let healthCheckService: jest.Mocked<HealthCheckService>;

  const mockHealthCheckService = {
    check: jest.fn(),
  };
  const mockTypeOrmHealthIndicator = {
    pingCheck: jest.fn(),
  };
  const mockMemoryHealthIndicator = {
    checkHeap: jest.fn(),
    checkRSS: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [HealthController],
      providers: [
        { provide: HealthCheckService, useValue: mockHealthCheckService },
        { provide: TypeOrmHealthIndicator, useValue: mockTypeOrmHealthIndicator },
        { provide: MemoryHealthIndicator, useValue: mockMemoryHealthIndicator },
      ],
    }).compile();

    controller = module.get<HealthController>(HealthController);
    healthCheckService = module.get(HealthCheckService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should have all required dependencies injected', () => {
    expect(controller['health']).toBeDefined();
    expect(controller['db']).toBeDefined();
    expect(controller['memory']).toBeDefined();
  });

  describe('check', () => {
    it('should return success when all health checks pass', async () => {
      // Arrange
      const healthResult = {
        status: 'ok',
        info: {
          database: { status: 'up' },
          memory_heap: { status: 'up' },
          memory_rss: { status: 'up' },
        },
        error: {},
        details: {
          database: { status: 'up' },
          memory_heap: { status: 'up' },
          memory_rss: { status: 'up' },
        },
      };
      (healthCheckService.check as jest.Mock).mockResolvedValue(healthResult);

      // Act
      const result = await controller.check();

      // Assert
      expect(result).toEqual(healthResult);
      expect(result.status).toBe('ok');
      expect(Object.keys(result.info)).toHaveLength(3);
      expect(Object.keys(result.error)).toHaveLength(0);
      expect(healthCheckService.check).toHaveBeenCalledWith([
        expect.any(Function),
        expect.any(Function),
        expect.any(Function),
      ]);
    });

    it('should call health check with correct parameters', async () => {
      // Arrange
      const healthResult = {
        status: 'ok',
        info: { database: { status: 'up' }, memory_heap: { status: 'up' }, memory_rss: { status: 'up' } },
        error: {},
        details: { database: { status: 'up' }, memory_heap: { status: 'up' }, memory_rss: { status: 'up' } },
      };
      (healthCheckService.check as jest.Mock).mockResolvedValue(healthResult);

      // Act
      await controller.check();

      // Assert
      expect(healthCheckService.check).toHaveBeenCalledTimes(1);
      const checkFunctions = healthCheckService.check.mock.calls[0][0];
      expect(checkFunctions).toHaveLength(3);
      expect(typeof checkFunctions[0]).toBe('function');
      expect(typeof checkFunctions[1]).toBe('function');
      expect(typeof checkFunctions[2]).toBe('function');
    });

    it('should return error when database health check fails', async () => {
      // Arrange
      const healthResult = {
        status: 'error',
        info: {
          memory_heap: { status: 'up' },
          memory_rss: { status: 'up' },
        },
        error: {
          database: { status: 'down', message: 'Database connection failed' },
        },
        details: {
          database: { status: 'down', message: 'Database connection failed' },
          memory_heap: { status: 'up' },
          memory_rss: { status: 'up' },
        },
      };
      (healthCheckService.check as jest.Mock).mockResolvedValue(healthResult);

      // Act
      const result = await controller.check();

      // Assert
      expect(result).toEqual(healthResult);
      expect(result.status).toBe('error');
      expect(result.error.database).toBeDefined();
      expect(result.error.database.status).toBe('down');
      expect(result.error.database.message).toBe('Database connection failed');
      expect(result.info.memory_heap.status).toBe('up');
      expect(result.info.memory_rss.status).toBe('up');
    });

    it('should return error when memory heap health check fails', async () => {
      // Arrange
      const healthResult = {
        status: 'error',
        info: {
          database: { status: 'up' },
          memory_rss: { status: 'up' },
        },
        error: {
          memory_heap: { status: 'down', message: 'Memory heap exceeded limit' },
        },
        details: {
          database: { status: 'up' },
          memory_heap: { status: 'down', message: 'Memory heap exceeded limit' },
          memory_rss: { status: 'up' },
        },
      };
      (healthCheckService.check as jest.Mock).mockResolvedValue(healthResult);

      // Act
      const result = await controller.check();

      // Assert
      expect(result).toEqual(healthResult);
      expect(result.status).toBe('error');
      expect(result.error.memory_heap).toBeDefined();
      expect(result.error.memory_heap.status).toBe('down');
      expect(result.error.memory_heap.message).toBe('Memory heap exceeded limit');
    });

    it('should return error when memory RSS health check fails', async () => {
      // Arrange
      const healthResult = {
        status: 'error',
        info: {
          database: { status: 'up' },
          memory_heap: { status: 'up' },
        },
        error: {
          memory_rss: { status: 'down', message: 'Memory RSS exceeded limit' },
        },
        details: {
          database: { status: 'up' },
          memory_heap: { status: 'up' },
          memory_rss: { status: 'down', message: 'Memory RSS exceeded limit' },
        },
      };
      (healthCheckService.check as jest.Mock).mockResolvedValue(healthResult);

      // Act
      const result = await controller.check();

      // Assert
      expect(result).toEqual(healthResult);
      expect(result.status).toBe('error');
      expect(result.error.memory_rss).toBeDefined();
      expect(result.error.memory_rss.status).toBe('down');
      expect(result.error.memory_rss.message).toBe('Memory RSS exceeded limit');
    });

    it('should return error when multiple health checks fail', async () => {
      // Arrange
      const healthResult = {
        status: 'error',
        info: {},
        error: {
          database: { status: 'down', message: 'Database connection failed' },
          memory_heap: { status: 'down', message: 'Memory heap exceeded limit' },
          memory_rss: { status: 'down', message: 'Memory RSS exceeded limit' },
        },
        details: {
          database: { status: 'down', message: 'Database connection failed' },
          memory_heap: { status: 'down', message: 'Memory heap exceeded limit' },
          memory_rss: { status: 'down', message: 'Memory RSS exceeded limit' },
        },
      };
      (healthCheckService.check as jest.Mock).mockResolvedValue(healthResult);

      // Act
      const result = await controller.check();

      // Assert
      expect(result).toEqual(healthResult);
      expect(result.status).toBe('error');
      expect(Object.keys(result.error)).toHaveLength(3);
      expect(Object.keys(result.info)).toHaveLength(0);
      expect(result.error.database.status).toBe('down');
      expect(result.error.memory_heap.status).toBe('down');
      expect(result.error.memory_rss.status).toBe('down');
    });

    it('should handle health check service throwing an error', async () => {
      // Arrange
      const error = new Error('Health check service failed');
      (healthCheckService.check as jest.Mock).mockRejectedValue(error);

      // Act & Assert
      await expect(controller.check()).rejects.toThrow('Health check service failed');
      expect(healthCheckService.check).toHaveBeenCalledTimes(1);
    });

    it('should verify health check configuration', async () => {
      // Arrange
      const healthResult = {
        status: 'ok',
        info: { database: { status: 'up' }, memory_heap: { status: 'up' }, memory_rss: { status: 'up' } },
        error: {},
        details: { database: { status: 'up' }, memory_heap: { status: 'up' }, memory_rss: { status: 'up' } },
      };
      (healthCheckService.check as jest.Mock).mockResolvedValue(healthResult);

      // Act
      await controller.check();

      // Assert
      expect(healthCheckService.check).toHaveBeenCalledWith([
        expect.any(Function),
        expect.any(Function),
        expect.any(Function),
      ]);

      // Verify the functions are properly configured
      const checkFunctions = healthCheckService.check.mock.calls[0][0];
      expect(checkFunctions).toHaveLength(3);
    });

    it('deve lidar com retorno parcial dos checks (ex: apenas database e heap)', async () => {
      const healthResult = {
        status: 'ok',
        info: { database: { status: 'up' }, memory_heap: { status: 'up' } },
        error: {},
        details: { database: { status: 'up' }, memory_heap: { status: 'up' } },
      };
      (healthCheckService.check as jest.Mock).mockResolvedValue(healthResult);
      const result = await controller.check();
      expect(result).toEqual(healthResult);
      expect(result.info.memory_heap).toBeDefined();
      expect(result.info.memory_rss).toBeUndefined();
    });

    it('deve lidar com valores inesperados no resultado do health check', async () => {
      const healthResult = {
        status: 'weird',
        info: { database: { status: 'maybe' } },
        error: { memory_heap: { status: 'strange', message: '???' } },
        details: { database: { status: 'maybe' }, memory_heap: { status: 'strange', message: '???' } },
        extraField: 'surpresa',
      };
      (healthCheckService.check as jest.Mock).mockResolvedValue(healthResult);
      const result = await controller.check();
      expect(result.status).toBe('weird');
      expect(result.info.database.status).toBe('maybe');
      expect(result.error.memory_heap.status).toBe('strange');
      expect((result as any).extraField).toBe('surpresa');
    });

    it('deve lidar com campos nulos ou undefined no health check', async () => {
      const healthResult = {
        status: 'ok',
        info: { database: null, memory_heap: undefined },
        error: {},
        details: { database: null, memory_heap: undefined },
      };
      (healthCheckService.check as jest.Mock).mockResolvedValue(healthResult);
      const result = await controller.check();
      expect(result.info.database).toBeNull();
      expect(result.info.memory_heap).toBeUndefined();
    });
  });
});
