import { <PERSON>du<PERSON> } from '@nestjs/common';
import { CancelBetService } from './cancel-bet.service';
import { CancelBetController } from './cancel-bet.controller';
import { HMACModule } from 'src/hmac/hmac.module';
import { DatabaseModule } from '../common/database/database.module';
import { WalletModule } from '@/wallet/wallet.module';

@Module({
  imports: [DatabaseModule, HMACModule, WalletModule],
  controllers: [CancelBetController],
  providers: [CancelBetService],
  exports: [CancelBetService],
})
export class CancelBetModule {}
