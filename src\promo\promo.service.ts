import { HttpException, Injectable, Logger } from '@nestjs/common';
import { PromoBetDto } from './dto/promo-bet.dto';
import { ResponsePromoBetDto } from './dto/response-promo-bet.dto';
import { PromoPrizeDto } from './dto/promo-prize.dto';
import { ResponsePromoPrizeDto } from './dto/response-promo-prize.dto';
import { PromoRollbackDto } from './dto/promo-rollback.dto';
import { ResponsePromoRollbackDto } from './dto/response-promo-rollback.dto';
import { PromoWinDto } from './dto/promo-win.dto';
import { ResponsePromoWinDto } from './dto/response-promo-win.dto';

@Injectable()
export class PromoService {
  private readonly logger = new Logger(PromoService.name);

  async bet(createPromoDto: PromoBetDto): Promise<ResponsePromoBetDto> {
    try {
      this.logger.log('PromoService', JSON.stringify(createPromoDto));
      return {
        balance: '0.01',
        bonus_amount: '1.00',
        id: '1fb7a3e6-6d9a-4e0a-aff9-9b94e9382795',
        id_casino: 'fd9b4706-5599-47cb-9832-9474c69de4a5',
        processed_at: '2022-06-15T15:30:00.000054Z',
      };
    } catch (e) {
      this.logger.error('Error to bet promo', e);
      throw new HttpException(e.message, e.status);
    }
  }

  async prize(createPromoDto: PromoPrizeDto): Promise<ResponsePromoPrizeDto> {
    try {
      this.logger.log('PromoService', JSON.stringify(createPromoDto));
      return {
        id: '9b41d5fc-f4f7-4588-bdd7-8b8f3b220946',
        id_casino: '7a822265-6073-4274-a19e-a03314ba282e',
        processed_at: '2022-06-15T15:30:00.000054Z',
      };
    } catch (e) {
      this.logger.error('Error to bet promo', e);
      throw new HttpException(e.message, e.status);
    }
  }

  async rollback(
    createPromoDto: PromoRollbackDto,
  ): Promise<ResponsePromoRollbackDto> {
    try {
      this.logger.log('PromoService', JSON.stringify(createPromoDto));
      return {
        balance: '0.01',
        id: '1fb7a3e6-6d9a-4e0a-aff9-9b94e9382795',
        id_casino: 'fd9b4706-5599-47cb-9832-9474c69de4a5',
        processed_at: '2022-06-15T15:30:00.000054Z',
      };
    } catch (e) {
      this.logger.error('Error to bet promo', e);
      throw new HttpException(e.message, e.status);
    }
  }

  async win(createPromoDto: PromoWinDto): Promise<ResponsePromoWinDto> {
    try {
      this.logger.log('PromoService', JSON.stringify(createPromoDto));
      return {
        balance: '0.01',
        bonus_amount: '1.00',
        id: '1fb7a3e6-6d9a-4e0a-aff9-9b94e9382795',
        id_casino: 'fd9b4706-5599-47cb-9832-9474c69de4a5',
        processed_at: '2022-06-15T15:30:00.000054Z',
      };
    } catch (e) {
      this.logger.error('Error to bet promo', e);
      throw new HttpException(e.message, e.status);
    }
  }
}
