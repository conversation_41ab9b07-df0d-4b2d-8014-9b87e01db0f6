import { BalanceService } from '@/balance/balance.service';
import { CasinoGames } from '@/casino-games/entities/casino-game.entity';
import { CasinoTransactionDetailsEntity } from '@/casino-transaction/controller/entities/casino-transaction-details.entity';
import { CasinoTransactionEntity } from '@/casino-transaction/controller/entities/casino-transactions.entity';
import { ErrorCode } from '@/common/enums/error-codes.enum';
import { statusEnum } from '@/common/enums/status.enum';
import { typeBetEnum } from '@/common/enums/type-bet.enum';
import { BusinessException } from '@/common/exceptions/business.exception';
import { WalletService } from '@/wallet/wallet.service';
import { RabbitmqService } from '@h2/rabbitmq';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager, InsertResult } from 'typeorm';
import {
  RollbackRequestDto,
  RollbackTransactionDto,
} from './dto/rollback-request.dto';
import {
  RollbackResponseDto,
  RollbackTransactionResponseDto,
} from './dto/rollback-response.dto';
import { CasinoSessionEntity } from '@/common/entities/casino-session.entity';
import { v4 as uuidv4 } from 'uuid';

interface ProcessedRollbackData {
  response: RollbackTransactionResponseDto;
  originalType: string;
  amount: number;
  transactionId: string;
  isIdempotent: boolean;
}

@Injectable()
export class RollbackService {
  private readonly logger = new Logger(RollbackService.name);

  constructor(
    @InjectEntityManager() private readonly manager: EntityManager,
    private readonly walletService: WalletService,
    private readonly rabbitmqService: RabbitmqService,
    private readonly balanceService: BalanceService,
  ) {}

  async processRollback(dto: RollbackRequestDto): Promise<RollbackResponseDto> {
    this.logger.log(`[Inicio] Process rollback: ${JSON.stringify(dto)}`);

    try {
      const casinoSession = await this.manager.findOne(CasinoSessionEntity, {
        select: ['partnerId', 'id'],
        where: {
          playerId: dto.playerId,
          aggregatorId: dto.sessionId,
        },
      });

      if (!casinoSession) {
        throw new BusinessException(
          ErrorCode.NOT_FOUND,
          'Sessão não encontrada',
          {},
          HttpStatus.NOT_FOUND,
        );
      }
      // 1. Buscar transaction principal pela roundId
      let transaction = await this.findTransaction(dto.roundId);

      if (!transaction) {
        this.logger.log(
          `[Info] Nenhuma transação encontrada para o round ${dto.roundId}. Criando nova transação cancelada.`,
        );

        const balance = await this.balanceService.getBalanceInWallet(
          casinoSession.partnerId,
          dto.playerId,
        );

        const transactionId = uuidv4();
        transaction = {
          id: transactionId,
          sessionCasinoId: casinoSession.id,
          partnerId: casinoSession.partnerId,
          playerId: dto.playerId,
          gameId: dto.gameId,
          roundId: dto.roundId,
          roundEnded: dto.roundEnded,
          status: statusEnum.Canceled,
          currency: dto.currency,
          aggregatorCode: dto.aggregatorCode,
          balance: balance,
          amount: 0,
          winAmount: 0,
        } as CasinoTransactionEntity;

        const transactionDetails = dto.transactions.map(
          transaction =>
            ({
              id: uuidv4(),
              casinoTransactionsId: transactionId,
              transactionId: transaction.id,
              transactionRefundId: transaction.originalId,
              roundId: dto.roundId,
              currency: dto.currency,
              createdAt: new Date(),
              type: '',
              amount: 0,
            }) as CasinoTransactionDetailsEntity,
        ) as CasinoTransactionDetailsEntity[];

        // Criar nova transação com status cancelada
        await this.manager.transaction(async transactionalEntityManager => {
          await transactionalEntityManager.insert(
            CasinoTransactionEntity,
            transaction,
          );

          await transactionalEntityManager.insert(
            CasinoTransactionDetailsEntity,
            transactionDetails,
          );
        });

        return {
          balance: transaction.balance.toString(),
          roundIdCasino: transaction.id,
          transactions: transactionDetails.map(detail => ({
            id: detail.transactionId,
            idCasino: detail.id,
            processedAt: new Date(detail.createdAt).toISOString(),
          })),
        } as RollbackResponseDto;
      }

      this.logger.log(
        `[Found] Transaction ${transaction.id} for round ${dto.roundId}`,
      );

      if (transaction.status === statusEnum.FailedWallet) {
        this.logger.warn(
          `Transacao ${transaction.id} está com status FailedWallet, rollback não pode ser processado.`,
        );
        return {
          balance: transaction.balance.toString(),
          roundIdCasino: transaction.id,
          transactions: transaction.details.map(detail => ({
            id: detail.transactionId,
            idCasino: detail.id,
            processedAt: new Date(detail.createdAt).toISOString(),
          })),
        };
      }

      // 2. Buscar informações do jogo
      const game = await this.manager.findOne(CasinoGames, {
        where: { id: dto.gameId },
        relations: ['category'],
      });

      if (!game) {
        this.logger.warn(`Game ${dto.gameId} not found`);
        throw new BusinessException(
          ErrorCode.NOT_FOUND,
          'Jogo não encontrado para o ID informado.',
          {},
          HttpStatus.NOT_FOUND,
        );
      }

      // 3. Processar cada rollback transaction (validações e criação de details)
      const processedData: ProcessedRollbackData[] = [];

      for (const rollbackTx of dto.transactions) {
        const result = await this.processRollbackTransaction(
          rollbackTx,
          dto,
          transaction,
        );

        if (result) {
          processedData.push(result);
        }
      }

      // 4. Executar operações na wallet em lote (apenas novas, não idempotentes)
      const newOperations = processedData.filter(data => !data.isIdempotent);

      if (!newOperations || newOperations.length === 0) {
        this.logger.log(`[Wallet] No new operations to process`);

        const balanceResult = await this.walletService.getBalanceByPlayer(
          transaction.partnerId,
          dto.playerId,
        );
        return {
          balance: balanceResult.balance.toString(),
          roundIdCasino: transaction.id,
          transactions: processedData.map(data => data.response),
        };
      }

      const walletOperations = newOperations.map(data => ({
        playerId: dto.playerId,
        partnerId: transaction.partnerId,
        adjustmentType:
          data.originalType === typeBetEnum.Bet
            ? typeBetEnum.RefundBet
            : typeBetEnum.RefundWin,
        amount: data.amount,
        reason:
          data.originalType === typeBetEnum.Bet ? 'Refund Bet' : 'Refund Win',
        origin: 'casino',
        accountType: 'regular',
        isBonusOperation: false,
        gameId: dto.gameId,
        gameProvider: game?.gameProvider,
        transactionId: data.response.idCasino,
      }));

      this.logger.log(
        `[Wallet] Executando ${walletOperations.length} operações de rollback em lote`,
      );

      const walletResult =
        await this.walletService.batchOperations(walletOperations);

      this.logger.log(`[Wallet] Todas operações de rollback concluídas`);

      // 5. Publicar mensagens de auditoria em paralelo (apenas para novas operações)
      await Promise.all(
        newOperations.map(data =>
          this.publishCasinoMessage({
            activityId: data.transactionId,
            amount: data.amount,
            type: data.originalType === typeBetEnum.Bet ? 'Win' : 'Bet', //inverte a logica pois só podemos enviar win e bet
            roundId: dto.roundId,
            playerId: dto.playerId,
            gameId: dto.gameId,
            gameName: game.name,
            gameType: game.category?.name,
            vendorId: game.gameProviderId,
            vendorName: game.gameProvider,
            currency: dto.currency,
            origin: 'casino',
            exchangeRate: 1,
            roundEnd: dto.roundEnded,
            status: 'Approved',
          }),
        ),
      );

      this.logger.log(
        `[RabbitMQ] ${newOperations.length} mensagens de auditoria publicadas`,
      );

      this.logger.log(
        `[Fim] Rollback processado. Balance final: ${walletResult.subAccount.balance}`,
      );

      await this.manager.update(CasinoTransactionEntity, transaction.id, {
        status: statusEnum.Canceled,
      });

      return {
        balance: walletResult.subAccount.balance.toString(),
        roundIdCasino: transaction.id,
        transactions: processedData.map(data => data.response),
      };
    } catch (error) {
      this.logger.error(`[Error] Rollback failed: ${error.message}`);

      if (error instanceof BusinessException) {
        throw error;
      }

      throw new HttpException(
        error.message || 'Erro ao processar rollback.',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private async processRollbackTransaction(
    rollbackTx: RollbackTransactionDto,
    dto: RollbackRequestDto,
    transaction: CasinoTransactionEntity,
  ): Promise<ProcessedRollbackData | null> {
    // 1. Verificar idempotência
    const existing = await this.checkIdempotency(rollbackTx.id);
    if (existing) {
      this.logger.log(`[Idempotency] Rollback ${rollbackTx.id} já processado`);
      // Retorna com flag de idempotência para não processar wallet/auditoria novamente
      return {
        response: {
          id: rollbackTx.id,
          idCasino: existing.id,
          processedAt: new Date(existing.createdAt).toISOString(),
        },
        originalType: existing.type,
        amount: existing.amount,
        transactionId: rollbackTx.id,
        isIdempotent: true,
      };
    }

    // 2. Buscar detail original
    const originalDetail = await this.findOriginalDetail(
      transaction.id,
      rollbackTx.originalId,
    );

    if (!originalDetail) {
      this.logger.warn(
        `[Warning] Original detail ${rollbackTx.originalId} not found - processing anyway`,
      );
      // Requisito: processar mesmo sem original
      // Pode-se escolher skip ou criar "órfão"
      // throw new HttpException(
      //   `Original detail not found for id ${rollbackTx.originalId}`,
      //   HttpStatus.NOT_FOUND,
      // );
      throw new BusinessException(
        ErrorCode.NOT_FOUND,
        'Detalhe original não encontrado para o ID informado.',
        {},
        HttpStatus.NOT_FOUND,
      );
    }

    this.logger.log(
      `[Found] Original detail: type=${originalDetail.type}, amount=${originalDetail.amount}`,
    );

    // 3. Criar rollback detail
    const rollbackDetail = await this.createRollbackDetail({
      casinoTransactionsId: transaction.id,
      transactionId: rollbackTx.id,
      roundId: dto.roundId,
      type: `refund_${originalDetail.type}`,
      amount: originalDetail.amount,
      currency: dto.currency,
      transactionRefundId: rollbackTx.originalId,
    });

    this.logger.log(
      `[Created] Rollback detail ${rollbackDetail.identifiers[0].id}`,
    );

    // Retornar dados para processar wallet/auditoria em lote
    return {
      response: {
        id: rollbackTx.id,
        idCasino: rollbackDetail.identifiers[0].id,
        processedAt: new Date().toISOString(),
      },
      originalType: originalDetail.type,
      amount: parseFloat(originalDetail.amount.toString()),
      transactionId: rollbackTx.id,
      isIdempotent: false,
    };
  }

  private async checkIdempotency(
    transactionId: string,
  ): Promise<CasinoTransactionDetailsEntity | null> {
    return this.manager.findOne(CasinoTransactionDetailsEntity, {
      where: { transactionId },
    });
  }

  private async findTransaction(
    roundId: string,
  ): Promise<CasinoTransactionEntity | null> {
    return this.manager.findOne(CasinoTransactionEntity, {
      where: { roundId },
    });
  }

  private async findOriginalDetail(
    casinoTransactionsId: string,
    transactionId: string,
  ): Promise<CasinoTransactionDetailsEntity | null> {
    return this.manager.findOne(CasinoTransactionDetailsEntity, {
      where: {
        casinoTransactionsId,
        transactionId,
      },
    });
  }

  private async createRollbackDetail(
    data: Partial<CasinoTransactionDetailsEntity>,
  ): Promise<InsertResult> {
    return await this.manager.insert(CasinoTransactionDetailsEntity, data);
  }

  private async publishCasinoMessage(params: {
    activityId: string;
    amount: number;
    type: string;
    roundId: string;
    playerId: string;
    gameId: string;
    gameName: string;
    gameType: string;
    vendorId: string;
    vendorName: string;
    currency: string;
    origin: string;
    exchangeRate: number;
    roundEnd: boolean;
    status: string;
  }): Promise<void> {
    const message = {
      activity_id: params.activityId,
      amount: params.amount,
      type: params.type,
      round_id: params.roundId,
      user_id: params.playerId,
      game_id: params.gameId,
      game_name: params.gameName,
      game_type: params.gameType,
      vendor_id: params.vendorId,
      vendor_name: params.vendorName,
      currency: params.currency,
      origin: params.origin,
      exchange_rate: params.exchangeRate,
      is_round_end: params.roundEnd,
      status: params.status,
      timestamp: new Date().toISOString(),
      wager_amount: params.amount,
    };

    try {
      await this.rabbitmqService.publishToQueue(
        message,
        'crm.fasttrack.integration.queue',
        'CASINO',
      );
    } catch (error) {
      this.logger.error(
        `[Error] Error publishing casino message: ${JSON.stringify(error)}`,
      );
    }
  }
}
