import { ApiProperty } from '@nestjs/swagger';

export class CasinoGamesResponseDto {
  @ApiProperty({ description: 'ID do jogo' })
  id: string;

  @ApiProperty({ description: 'ID do jogo no provedor' })
  gameId: string;

  @ApiProperty({ description: 'Nome do jogo' })
  name: string;

  @ApiProperty({ description: 'Provedor do jogo' })
  gameProvider: string;

  @ApiProperty({ description: 'ID do parceiro' })
  partnerId: string;

  @ApiProperty({ description: 'Status do jogo' })
  status: boolean;

  @ApiProperty({ description: 'Data de criação' })
  createdAt: Date;

  @ApiProperty({ description: 'Data de atualização' })
  updatedAt: Date;
}

export const CasinoGamesResponseDtoExample = {
  id: 'game-123',
  gameId: '12345',
  name: 'Fortune Tiger',
  gameProvider: 'Pragmatic Play',
  partnerId: 'partner-123',
};
