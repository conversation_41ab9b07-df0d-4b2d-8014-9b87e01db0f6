import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { CasinoTransactionService } from "./services/casino-transaction.service";
import { CasinoTransactionController } from "./controller/casino-transaction.controller";
import { DatabaseModule } from "@/common/database/database.module";
import { DateUtilsService } from "@/common/utils/date";
import { DailyReportController } from "./controller/daily-report.controller";
import { DailyReportService } from "./services/daily-report.service";

@Module({
  imports: [DatabaseModule],
  controllers: [CasinoTransactionController, DailyReportController],
  providers: [CasinoTransactionService, DateUtilsService, DailyReportService],
})
export class CasinoTransactionModule {}
