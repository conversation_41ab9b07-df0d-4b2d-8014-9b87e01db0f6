import { DatabaseModule } from '@/common/database/database.module';
import { DateUtilsService } from '@/common/utils/date';
import { Module } from '@nestjs/common';
import { BetCasinoTransactionController } from './controller/bet-casino-transaction.controller';
import { CasinoTransactionController } from './controller/casino-transaction.controller';
import { DailyReportController } from './controller/daily-report.controller';
import { CasinoTransactionService } from './services/casino-transaction.service';
import { DailyReportService } from './services/daily-report.service';

@Module({
  imports: [DatabaseModule],
  controllers: [
    CasinoTransactionController,
    DailyReportController,
    BetCasinoTransactionController,
  ],
  providers: [CasinoTransactionService, DateUtilsService, DailyReportService],
})
export class CasinoTransactionModule {}
