import { ApiProperty } from '@nestjs/swagger';

export class RollbackTransactionResponseDto {
  @ApiProperty({
    description: 'Rollback transaction ID from request',
    example: 'a4_r_30104280_int',
  })
  id: string;

  @ApiProperty({
    description: 'Casino internal transaction ID',
    example: 'eece8ba4-934a-48e1-bfd7-c111918b543e',
  })
  idCasino: string;

  @ApiProperty({
    description: 'Timestamp when processed (ISO 8601)',
    example: '2025-01-14T10:30:45.123Z',
    required: false,
  })
  processedAt?: string;
}

export class RollbackResponseDto {
  @ApiProperty({
    description: 'Player wallet balance after rollback',
    example: '1250.50',
  })
  balance: string;

  @ApiProperty({
    description: 'Casino round identifier',
    example: 'eece8ba4-934a-48e1-bfd7-c111918b543e',
  })
  roundIdCasino: string;

  @ApiProperty({
    description: 'List of processed rollback transactions',
    type: [RollbackTransactionResponseDto],
  })
  transactions: RollbackTransactionResponseDto[];
}
