import {
  Column,
  CreateDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { CasinoGames } from './casino-game.entity';

@Entity('casino_game_providers')
export class CasinoGameProvider {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  @Column({ name: 'name' })
  name: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', nullable: true })
  updatedAt: Date;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'last_user_modify', nullable: true })
  lastUserModify: string;

  @OneToMany(() => CasinoGames, game => game.provider)
  games: CasinoGames[];
}
