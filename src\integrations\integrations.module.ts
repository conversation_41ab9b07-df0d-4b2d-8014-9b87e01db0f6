import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { UtilsIntegrationService } from './utils-integration.service';
import { PlayerIntegrationService } from './player-integration.service';

@Module({
  imports: [HttpModule],
  providers: [UtilsIntegrationService, PlayerIntegrationService],
  exports: [HttpModule, UtilsIntegrationService, PlayerIntegrationService],
})
export class IntegrationsModule {}
