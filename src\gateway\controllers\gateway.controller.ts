import {
  Controller,
  Post,
  Body,
  Headers,
  Param,
  Get,
  HttpException,
  HttpStatus,
  Logger,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiHeader } from '@nestjs/swagger';
import { GatewayService } from '../services/gateway.service';
import { AdapterFactoryService } from '../services/adapter-factory.service';

/**
 * Controller principal do Gateway que recebe requisições dos provedores
 */
@ApiTags('Gateway')
@Controller('gateway')
export class GatewayController {
  private readonly logger = new Logger(GatewayController.name);

  constructor(
    private readonly gatewayService: GatewayService,
    private readonly adapterFactory: AdapterFactoryService,
  ) {}

  /**
   * Endpoint genérico para saldo - detecta automaticamente o provedor
   */
  @Post('balance')
  @ApiOperation({ summary: 'Consulta saldo do jogador (auto-detect provider)' })
  @ApiHeader({ name: 'x-provider', required: false, description: 'Nome do provedor (opcional)' })
  @ApiResponse({ status: 200, description: 'Saldo consultado com sucesso' })
  @ApiResponse({ status: 400, description: 'Requisição inválida' })
  @ApiResponse({ status: 500, description: 'Erro interno do servidor' })
  async getBalance(
    @Body() body: any,
    @Headers() headers: Record<string, string>,
  ): Promise<any> {
    try {
      this.logger.log('Received balance request', { body, headers: this.sanitizeHeaders(headers) });
      
      const result = await this.gatewayService.processBalance(body, headers);
      
      this.logger.log('Balance request processed successfully');
      return result;
    } catch (error) {
      this.logger.error('Error processing balance request:', error);
      throw new HttpException(
        error.message || 'Internal server error',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Endpoint específico para saldo por provedor
   */
  @Post(':provider/balance')
  @ApiOperation({ summary: 'Consulta saldo do jogador por provedor específico' })
  @ApiParam({ name: 'provider', description: 'Nome do provedor (EGT, BGAMING, etc.)' })
  @ApiResponse({ status: 200, description: 'Saldo consultado com sucesso' })
  @ApiResponse({ status: 400, description: 'Requisição inválida' })
  @ApiResponse({ status: 404, description: 'Provedor não encontrado' })
  async getBalanceByProvider(
    @Param('provider') provider: string,
    @Body() body: any,
    @Headers() headers: Record<string, string>,
  ): Promise<any> {
    try {
      this.logger.log(`Received balance request for provider: ${provider}`, { 
        body, 
        headers: this.sanitizeHeaders(headers) 
      });

      if (!this.adapterFactory.hasAdapter(provider)) {
        throw new HttpException(
          `Provider ${provider} not supported`,
          HttpStatus.NOT_FOUND,
        );
      }

      const result = await this.gatewayService.processBalance(body, headers, provider);
      
      this.logger.log(`Balance request processed successfully for provider: ${provider}`);
      return result;
    } catch (error) {
      this.logger.error(`Error processing balance request for provider ${provider}:`, error);
      throw new HttpException(
        error.message || 'Internal server error',
        error.status || HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Endpoint genérico para aposta
   */
  @Post('bet')
  @ApiOperation({ summary: 'Processa aposta do jogador (auto-detect provider)' })
  @ApiHeader({ name: 'x-provider', required: false, description: 'Nome do provedor (opcional)' })
  @ApiResponse({ status: 200, description: 'Aposta processada com sucesso' })
  @ApiResponse({ status: 400, description: 'Requisição inválida' })
  async placeBet(
    @Body() body: any,
    @Headers() headers: Record<string, string>,
  ): Promise<any> {
    try {
      this.logger.log('Received bet request', { body, headers: this.sanitizeHeaders(headers) });
      
      const result = await this.gatewayService.processBet(body, headers);
      
      this.logger.log('Bet request processed successfully');
      return result;
    } catch (error) {
      this.logger.error('Error processing bet request:', error);
      throw new HttpException(
        error.message || 'Internal server error',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Endpoint específico para aposta por provedor
   */
  @Post(':provider/bet')
  @ApiOperation({ summary: 'Processa aposta do jogador por provedor específico' })
  @ApiParam({ name: 'provider', description: 'Nome do provedor (EGT, BGAMING, etc.)' })
  @ApiResponse({ status: 200, description: 'Aposta processada com sucesso' })
  @ApiResponse({ status: 400, description: 'Requisição inválida' })
  async placeBetByProvider(
    @Param('provider') provider: string,
    @Body() body: any,
    @Headers() headers: Record<string, string>,
  ): Promise<any> {
    try {
      this.logger.log(`Received bet request for provider: ${provider}`, { 
        body, 
        headers: this.sanitizeHeaders(headers) 
      });

      if (!this.adapterFactory.hasAdapter(provider)) {
        throw new HttpException(
          `Provider ${provider} not supported`,
          HttpStatus.NOT_FOUND,
        );
      }

      const result = await this.gatewayService.processBet(body, headers, provider);
      
      this.logger.log(`Bet request processed successfully for provider: ${provider}`);
      return result;
    } catch (error) {
      this.logger.error(`Error processing bet request for provider ${provider}:`, error);
      throw new HttpException(
        error.message || 'Internal server error',
        error.status || HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Endpoint genérico para ganho
   */
  @Post('win')
  @ApiOperation({ summary: 'Processa ganho do jogador (auto-detect provider)' })
  @ApiHeader({ name: 'x-provider', required: false, description: 'Nome do provedor (opcional)' })
  @ApiResponse({ status: 200, description: 'Ganho processado com sucesso' })
  @ApiResponse({ status: 400, description: 'Requisição inválida' })
  async processWin(
    @Body() body: any,
    @Headers() headers: Record<string, string>,
  ): Promise<any> {
    try {
      this.logger.log('Received win request', { body, headers: this.sanitizeHeaders(headers) });
      
      const result = await this.gatewayService.processWin(body, headers);
      
      this.logger.log('Win request processed successfully');
      return result;
    } catch (error) {
      this.logger.error('Error processing win request:', error);
      throw new HttpException(
        error.message || 'Internal server error',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Endpoint específico para ganho por provedor
   */
  @Post(':provider/win')
  @ApiOperation({ summary: 'Processa ganho do jogador por provedor específico' })
  @ApiParam({ name: 'provider', description: 'Nome do provedor (EGT, BGAMING, etc.)' })
  @ApiResponse({ status: 200, description: 'Ganho processado com sucesso' })
  @ApiResponse({ status: 400, description: 'Requisição inválida' })
  async processWinByProvider(
    @Param('provider') provider: string,
    @Body() body: any,
    @Headers() headers: Record<string, string>,
  ): Promise<any> {
    try {
      this.logger.log(`Received win request for provider: ${provider}`, { 
        body, 
        headers: this.sanitizeHeaders(headers) 
      });

      if (!this.adapterFactory.hasAdapter(provider)) {
        throw new HttpException(
          `Provider ${provider} not supported`,
          HttpStatus.NOT_FOUND,
        );
      }

      const result = await this.gatewayService.processWin(body, headers, provider);
      
      this.logger.log(`Win request processed successfully for provider: ${provider}`);
      return result;
    } catch (error) {
      this.logger.error(`Error processing win request for provider ${provider}:`, error);
      throw new HttpException(
        error.message || 'Internal server error',
        error.status || HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Endpoint para listar provedores disponíveis
   */
  @Get('providers')
  @ApiOperation({ summary: 'Lista provedores disponíveis' })
  @ApiResponse({ status: 200, description: 'Lista de provedores' })
  getProviders(): Array<{ name: string; version: string; enabled: boolean }> {
    return this.gatewayService.getProvidersInfo();
  }

  /**
   * Endpoint para verificar status de um provedor
   */
  @Get('providers/:provider/status')
  @ApiOperation({ summary: 'Verifica status de um provedor específico' })
  @ApiParam({ name: 'provider', description: 'Nome do provedor' })
  @ApiResponse({ status: 200, description: 'Status do provedor' })
  @ApiResponse({ status: 404, description: 'Provedor não encontrado' })
  getProviderStatus(@Param('provider') provider: string): { name: string; available: boolean } {
    const available = this.gatewayService.isProviderAvailable(provider);
    
    if (!available) {
      throw new HttpException(
        `Provider ${provider} not found`,
        HttpStatus.NOT_FOUND,
      );
    }

    return {
      name: provider,
      available: true,
    };
  }

  /**
   * Remove dados sensíveis dos headers para log
   */
  private sanitizeHeaders(headers: Record<string, string>): Record<string, string> {
    const sanitized = { ...headers };
    const sensitiveHeaders = ['authorization', 'x-api-key', 'x-secret'];
    
    for (const header of sensitiveHeaders) {
      if (sanitized[header]) {
        sanitized[header] = '***';
      }
    }
    
    return sanitized;
  }
}
