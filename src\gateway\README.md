# API Gateway para Provedores de Cassino

Este módulo implementa um API Gateway que atua como uma camada intermediária entre diferentes provedores de jogos de cassino (EGT, BGaming, etc.) e o sistema de cassino existente.

## 🏗️ Arquitetura

```
Provedores (EGT, BGaming, etc.)
        ↓
    API Gateway
        ↓
   Sistema Cassino
```

## 📁 Estrutura do Projeto

```
src/gateway/
├── adapters/           # Adapters específicos para cada provedor
│   ├── base-adapter.ts
│   ├── egt-adapter.ts
│   ├── bgaming-adapter.ts
│   └── *.spec.ts
├── controllers/        # Controllers do gateway
│   └── gateway.controller.ts
├── interfaces/         # Interfaces e tipos
│   └── provider.interface.ts
├── middlewares/        # Middlewares (logging, rate limiting)
│   ├── logging.middleware.ts
│   └── rate-limit.middleware.ts
├── services/          # Serviços principais
│   ├── gateway.service.ts
│   ├── adapter-factory.service.ts
│   └── casino-integration.service.ts
├── gateway.module.ts  # Módulo principal
└── README.md         # Esta documentação
```

## 🚀 Como Usar

### 1. Configuração

Adicione as variáveis de ambiente necessárias:

```env
# EGT Configuration
EGT_API_TOKEN=your_egt_token_here

# BGaming Configuration
BGAMING_API_KEY=your_bgaming_api_key
BGAMING_SECRET_KEY=your_bgaming_secret_key
```

### 2. Importar o Módulo

No seu `app.module.ts`:

```typescript
import { GatewayModule } from './gateway/gateway.module';

@Module({
  imports: [
    // ... outros módulos
    GatewayModule,
  ],
})
export class AppModule {}
```

### 3. Endpoints Disponíveis

#### Consulta de Saldo

**Auto-detecção de provedor:**
```http
POST /gateway/balance
Content-Type: application/json

# Para EGT:
{
  "user_id": "player123",
  "game_id": "game456",
  "currency": "BRL"
}

# Para BGaming:
{
  "playerId": "player123",
  "gameCode": "game456",
  "currency": "BRL"
}
```

**Provedor específico:**
```http
POST /gateway/egt/balance
POST /gateway/bgaming/balance
```

#### Processamento de Aposta

```http
POST /gateway/bet
POST /gateway/{provider}/bet
```

#### Processamento de Ganho

```http
POST /gateway/win
POST /gateway/{provider}/win
```

#### Informações dos Provedores

```http
GET /gateway/providers
GET /gateway/providers/{provider}/status
```

## 🔧 Adicionando Novos Provedores

### 1. Criar o Adapter

```typescript
// src/gateway/adapters/novo-provedor-adapter.ts
import { Injectable } from '@nestjs/common';
import { BaseProviderAdapter } from './base-adapter';

@Injectable()
export class NovoProvedorAdapter extends BaseProviderAdapter {
  readonly name = 'NOVO_PROVEDOR';
  readonly version = '1.0.0';

  transformRequest<T>(rawRequest: any, operation: string): T {
    // Implementar transformação da requisição
  }

  transformResponse<T>(standardResponse: any, operation: string): T {
    // Implementar transformação da resposta
  }

  protected validateSpecificRequest(rawRequest: any, operation: string): boolean {
    // Implementar validação específica
  }
}
```

### 2. Registrar no Factory

```typescript
// src/gateway/services/adapter-factory.service.ts
import { NovoProvedorAdapter } from '../adapters/novo-provedor-adapter';

// No método initializeAdapters():
this.registerAdapter('NOVO_PROVEDOR', new NovoProvedorAdapter(configs.NOVO_PROVEDOR));
```

### 3. Adicionar Configuração

```typescript
// No AdapterFactoryService
const configs: Record<string, IProviderConfig> = {
  // ... outros provedores
  NOVO_PROVEDOR: {
    name: 'NOVO_PROVEDOR',
    version: '1.0.0',
    enabled: true,
    endpoints: {
      balance: '/api/novo-provedor/balance',
      bet: '/api/novo-provedor/bet',
      win: '/api/novo-provedor/win',
    },
    // ... outras configurações
  },
};
```

## 🔍 Detecção Automática de Provedores

O gateway detecta automaticamente o provedor usando:

1. **Header `x-provider`** (mais confiável)
2. **User-Agent** da requisição
3. **Estrutura dos dados** da requisição
4. **Path da URL**

## 📊 Logging e Monitoramento

### Logs Estruturados

Todos os logs são estruturados e incluem:
- Provider identificado
- Operação realizada
- Tempo de resposta
- Status da requisição
- Dados sanitizados (sem informações sensíveis)

### Rate Limiting

Controle automático de rate limiting por provedor:
- EGT: 100 req/min
- BGaming: 200 req/min
- Default: 50 req/min

## 🧪 Testes

Execute os testes:

```bash
# Testes unitários
npm test gateway

# Testes específicos de um adapter
npm test egt-adapter.spec.ts

# Coverage
npm run test:cov
```

## 🔗 Integração com Sistema Existente

### Modificar o GatewayService

No arquivo `gateway.service.ts`, substitua os métodos mock pelos reais:

```typescript
private async callCasinoBalanceService(request: IBalanceRequest): Promise<IBalanceResponse> {
  // Substituir por:
  const result = await this.balanceController.getBalance({
    playerId: request.playerId,
    gameId: request.gameId,
    // ... outros parâmetros
  });

  return {
    playerId: request.playerId,
    balance: result.balance,
    currency: result.currency,
    timestamp: new Date(),
    success: true,
  };
}
```

### Injetar Dependências

```typescript
constructor(
  private readonly adapterFactory: AdapterFactoryService,
  private readonly balanceService: BalanceService, // Adicionar
  private readonly transactionService: TransactionService, // Adicionar
) {}
```

## 🚨 Tratamento de Erros

O gateway trata automaticamente:
- Provedores não suportados
- Requisições inválidas
- Timeouts
- Rate limiting
- Erros de validação

Todos os erros são logados e retornados no formato apropriado para cada provedor.

## 📈 Métricas e Performance

- Logs estruturados para análise
- Rate limiting configurável
- Timeouts por provedor
- Retry automático
- Sanitização de dados sensíveis

## 🔒 Segurança

- Headers sensíveis são sanitizados nos logs
- Rate limiting por IP/User-Agent
- Validação rigorosa de entrada
- Timeouts para prevenir ataques DoS
