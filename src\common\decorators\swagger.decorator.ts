import { applyDecorators, Type } from '@nestjs/common';
import { ApiHeader, ApiResponse, ApiQuery, getSchemaPath, ApiExtraModels, ApiOkResponse } from '@nestjs/swagger';
import { PaginationResponseDto } from '../dto/paginated-response.dto';

export const ApiHmacHeader = () => {
  return applyDecorators(
    ApiHeader({
      name: 'x-checksum',
      description: 'HMAC SHA256 do corpo da requisição',
      required: true,
    }),
  );
};

export const ApiHmacUnauthorizedResponse = () => {
  return applyDecorators(
    ApiResponse({
      status: 401,
      description: 'HMAC inválido ou não fornecido',
      schema: {
        type: 'object',
        properties: {
          errorCode: { type: 'number', example: 401 },
          errorMsg: { type: 'string', example: 'HMAC inválido ou não fornecido' },
        },
      },
    }),
  );
};

export const ApiForbiddenResponse = () => {
  return applyDecorators(
    ApiResponse({
      status: 403,
      description: 'Acesso negado',
      schema: {
        type: 'object',
        properties: {
          statusCode: { type: 'number', example: 403 },
          message: { type: 'string', example: 'Forbidden resource' }
        }
      }
    })
  );
};

export const ApiInternalServerErrorResponse = () => {
  return applyDecorators(
    ApiResponse({
      status: 500,
      description: 'Erro interno do servidor',
      schema: {
        type: 'object',
        properties: {
          statusCode: { type: 'number', example: 500 },
          message: { type: 'string', example: 'Internal server error' }
        },
      },
    }),
  );
};

export const ApiPartnerIdHeader = () => {
  return applyDecorators(
    ApiHeader({
      name: 'partner-id',
      description: 'ID do parceiro',
      required: true,
    }),
  );
};

export const ApiDateRangeQuery = () => {
  return applyDecorators(
    ApiQuery({
      name: 'fromDate',
      type: 'string',
      required: true,
      description: 'Data inicial (YYYY-MM-DD)',
      example: '2024-01-01',
    }),
    ApiQuery({
      name: 'toDate',
      type: 'string',
      required: true,
      description: 'Data final (YYYY-MM-DD)',
      example: '2024-01-31',
    }),
  );
};

export const ApiPaginationQuery = () => {
  return applyDecorators(
    ApiQuery({
      name: 'page',
      type: 'number',
      required: false,
      description: 'Número da página',
      example: 1
    }),
    ApiQuery({
      name: 'pageSize',
      type: 'number',
      required: false,
      description: 'Quantidade de itens por página',
      example: 10
    }),
    ApiQuery({
      name: 'orderBy',
      type: 'string',
      required: false,
      description: 'Campo para ordenação',
      example: 'createdAt'
    }),
    ApiQuery({
      name: 'sortOrder',
      type: 'string',
      required: false,
      description: 'Ordem de classificação',
      enum: ['ASC', 'DESC'],
      example: 'DESC'
    })
  );
};

export function getPaginatedSchema(itemDto: any) {
  return {
    allOf: [
      { $ref: getSchemaPath(PaginationResponseDto) },
      {
        properties: {
          data: {
            type: 'array',
            items: { $ref: getSchemaPath(itemDto) },
          },
        },
      },
    ],
  };
}

export const ApiPaginatedResponse = <TModel extends Type<any>>(description: string, model: TModel) => {
  return applyDecorators(
    ApiExtraModels(PaginationResponseDto, model),
    ApiOkResponse({
      description,
      schema: {
        allOf: [
          {
            $ref: getSchemaPath(PaginationResponseDto),
          },
        ],
        properties: {
          data: {
            type: 'array',
            items: { $ref: getSchemaPath(model) },
          },
        },
      },
    })
  );
};

export const ApiClientIpHeader = () => {
  return applyDecorators(
    ApiHeader({
      name: 'client-ip',
      description: 'IP do cliente',
      required: true,
    }),
  );
}; 