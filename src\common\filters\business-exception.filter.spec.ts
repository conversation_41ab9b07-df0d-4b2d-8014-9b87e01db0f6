import { Test, TestingModule } from '@nestjs/testing';
import { ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { BusinessExceptionFilter } from './business-exception.filter';
import { BusinessException } from '../exceptions/business.exception';
import { ErrorCode } from '../enums/error-codes.enum';

describe('BusinessExceptionFilter', () => {
  let filter: BusinessExceptionFilter;

  const mockJson = jest.fn();
  const mockStatus = jest.fn().mockReturnValue({ json: mockJson });
  const mockGetResponse = jest.fn().mockReturnValue({
    status: mockStatus,
  });
  const mockGetRequest = jest.fn();

  const mockArgumentsHost = {
    switchToHttp: () => ({
      getResponse: mockGetResponse,
      getRequest: mockGetRequest,
    }),
  } as unknown as ArgumentsHost;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [BusinessExceptionFilter],
    }).compile();

    filter = module.get<BusinessExceptionFilter>(BusinessExceptionFilter);
    jest.clearAllMocks();

    // Spy on logger methods
    jest.spyOn(filter['logger'], 'warn').mockImplementation();
    jest.spyOn(filter['logger'], 'error').mockImplementation();
  });

  it('should be defined', () => {
    expect(filter).toBeDefined();
  });

  describe('catch', () => {
    it('deve tratar BusinessException corretamente', () => {
      const exception = new BusinessException(
        ErrorCode.NOT_FOUND,
        'Resource not found',
        { resourceId: '123' },
        HttpStatus.NOT_FOUND,
      );

      mockGetRequest.mockReturnValue({
        headers: { 'trace-id': 'trace-123' },
      });

      filter.catch(exception, mockArgumentsHost);

      expect(mockStatus).toHaveBeenCalledWith(HttpStatus.NOT_FOUND);
      expect(mockJson).toHaveBeenCalledWith({
        traceId: 'trace-123',
        code: ErrorCode.NOT_FOUND,
        message: 'Resource not found',
        meta: { resourceId: '123' },
      });
      expect(filter['logger'].warn).toHaveBeenCalled();
    });

    it('deve tratar HttpException corretamente', () => {
      const exception = new HttpException(
        'Unauthorized access',
        HttpStatus.UNAUTHORIZED,
      );

      mockGetRequest.mockReturnValue({
        headers: { 'trace-id': 'trace-456' },
      });

      filter.catch(exception, mockArgumentsHost);

      expect(mockStatus).toHaveBeenCalledWith(HttpStatus.UNAUTHORIZED);
      expect(mockJson).toHaveBeenCalledWith({
        traceId: 'trace-456',
        code: 'HTTP_EXCEPTION',
        message: 'Unauthorized access',
      });
      expect(filter['logger'].warn).toHaveBeenCalled();
    });

    it('deve tratar HttpException com response object', () => {
      const exception = new HttpException(
        { message: 'Validation failed', errors: ['field1', 'field2'] },
        HttpStatus.BAD_REQUEST,
      );

      mockGetRequest.mockReturnValue({
        headers: { 'trace-id': 'trace-789' },
      });

      filter.catch(exception, mockArgumentsHost);

      expect(mockStatus).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
      expect(mockJson).toHaveBeenCalledWith({
        traceId: 'trace-789',
        code: 'HTTP_EXCEPTION',
        message: 'Validation failed',
      });
    });

    it('deve tratar exceções desconhecidas', () => {
      const exception = new Error('Unknown error');

      mockGetRequest.mockReturnValue({
        headers: { 'trace-id': 'trace-error' },
      });

      filter.catch(exception, mockArgumentsHost);

      expect(mockStatus).toHaveBeenCalledWith(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(mockJson).toHaveBeenCalledWith({
        traceId: 'trace-error',
        code: ErrorCode.INTERNAL_ERROR,
        message: 'Internal server error',
      });
      expect(filter['logger'].error).toHaveBeenCalled();
    });

    it('deve usar trace-id do header quando presente', () => {
      const exception = new BusinessException(
        ErrorCode.BAD_REQUEST,
        'Bad request',
        {},
        HttpStatus.BAD_REQUEST,
      );

      mockGetRequest.mockReturnValue({
        headers: { 'trace-id': 'custom-trace-id' },
      });

      filter.catch(exception, mockArgumentsHost);

      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          traceId: 'custom-trace-id',
        }),
      );
    });

    it('deve usar traceid alternativo do header quando trace-id não existe', () => {
      const exception = new BusinessException(
        ErrorCode.BAD_REQUEST,
        'Bad request',
        {},
        HttpStatus.BAD_REQUEST,
      );

      mockGetRequest.mockReturnValue({
        headers: { traceid: 'alternative-trace-id' },
      });

      filter.catch(exception, mockArgumentsHost);

      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          traceId: 'alternative-trace-id',
        }),
      );
    });

    it('deve usar undefined como traceId quando headers não têm trace-id', () => {
      const exception = new BusinessException(
        ErrorCode.BAD_REQUEST,
        'Bad request',
        {},
        HttpStatus.BAD_REQUEST,
      );

      mockGetRequest.mockReturnValue({
        headers: {},
      });

      filter.catch(exception, mockArgumentsHost);

      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          traceId: undefined,
        }),
      );
    });

    it('deve incluir metadata quando presente em BusinessException', () => {
      const metadata = {
        userId: 'user-123',
        action: 'delete',
        timestamp: '2024-01-01',
      };

      const exception = new BusinessException(
        ErrorCode.FORBIDDEN,
        'Action forbidden',
        metadata,
        HttpStatus.FORBIDDEN,
      );

      mockGetRequest.mockReturnValue({
        headers: { 'trace-id': 'trace-meta' },
      });

      filter.catch(exception, mockArgumentsHost);

      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          meta: metadata,
        }),
      );
    });

    it('deve logar BusinessException com warn level', () => {
      const exception = new BusinessException(
        ErrorCode.NOT_FOUND,
        'Not found',
        { id: '123' },
        HttpStatus.NOT_FOUND,
      );

      mockGetRequest.mockReturnValue({
        headers: { 'trace-id': 'trace-log' },
      });

      filter.catch(exception, mockArgumentsHost);

      expect(filter['logger'].warn).toHaveBeenCalledWith(
        expect.stringContaining('Business Exception'),
      );
    });

    it('deve logar HttpException com warn level', () => {
      const exception = new HttpException('Test error', HttpStatus.BAD_REQUEST);

      mockGetRequest.mockReturnValue({
        headers: { 'trace-id': 'trace-http' },
      });

      filter.catch(exception, mockArgumentsHost);

      expect(filter['logger'].warn).toHaveBeenCalledWith(
        expect.stringContaining('HTTP Exception'),
      );
    });

    it('deve logar exceções desconhecidas com error level', () => {
      const exception = new Error('Unexpected error');

      mockGetRequest.mockReturnValue({
        headers: { 'trace-id': 'trace-unknown' },
      });

      filter.catch(exception, mockArgumentsHost);

      expect(filter['logger'].error).toHaveBeenCalledWith(
        expect.stringContaining('Unhandled Exception'),
      );
    });

    it('deve tratar diferentes códigos de erro de BusinessException', () => {
      const errorCodes = [
        ErrorCode.NOT_FOUND,
        ErrorCode.BAD_REQUEST,
        ErrorCode.FORBIDDEN,
        ErrorCode.INTERNAL_ERROR,
      ];

      errorCodes.forEach(errorCode => {
        jest.clearAllMocks();

        const exception = new BusinessException(
          errorCode,
          'Test message',
          {},
          HttpStatus.BAD_REQUEST,
        );

        mockGetRequest.mockReturnValue({
          headers: { 'trace-id': 'trace-test' },
        });

        filter.catch(exception, mockArgumentsHost);

        expect(mockJson).toHaveBeenCalledWith(
          expect.objectContaining({
            code: errorCode,
          }),
        );
      });
    });

    it('deve retornar resposta JSON formatada corretamente', () => {
      const exception = new BusinessException(
        ErrorCode.BAD_REQUEST,
        'Validation error',
        { field: 'email' },
        HttpStatus.BAD_REQUEST,
      );

      mockGetRequest.mockReturnValue({
        headers: { 'trace-id': 'trace-format' },
      });

      filter.catch(exception, mockArgumentsHost);

      expect(mockJson).toHaveBeenCalledWith({
        traceId: 'trace-format',
        code: ErrorCode.BAD_REQUEST,
        message: 'Validation error',
        meta: { field: 'email' },
      });
    });
  });
});
