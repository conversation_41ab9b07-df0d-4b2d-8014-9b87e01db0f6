sonar.projectKey=h2_pam_backend_api-pam-casino_f07892fc-6b7d-4f93-8bef-f7d620aeb427
sonar.projectName=api-pam-casino
sonar.projectVersion=1.0
sonar.sourceEncoding=UTF-8

# Onde está o código
sonar.sources=src

# Onde estão os testes (se ficam no src)
sonar.tests=src
sonar.test.inclusions=**/*.spec.ts

# NÃO exclua os .spec.ts de sources; foque as exclusões de cobertura
sonar.exclusions=**/node_modules/**,src/common/**,**/*.enum.ts,**/*.interface.ts,src/**/*.entity.ts,src/**/*.dto.ts,src/**/entities/*.ts,src/**/dto/*.ts

# Cobertura (lcov)
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.coverage.exclusions=**/*.module.ts,**/main.ts,**/*.spec.ts,**/*.enum.ts,**/*.interface.ts,src/**/*.entity.ts,src/**/*.dto.ts,src/**/entities/*.ts,src/**/dto/*.ts
