import { ApiProperty } from '@nestjs/swagger';

export class LastCasinoBetDto {
  @ApiProperty({
    description: 'Data da última aposta',
    example: '2026-01-14T20:47:40.196Z',
    nullable: true,
  })
  date: Date | null;

  @ApiProperty({
    description: 'Valor da última aposta',
    example: 3.6,
  })
  amount: number;
}

export class PerformanceDto {
  @ApiProperty({
    description: 'Lucro/Prejuízo (P/L)',
    example: -200798.61,
  })
  profitLoss: number;

  @ApiProperty({
    description: 'Rentabilidade do jogo (ROI) em porcentagem',
    example: -65.29,
  })
  roi: number;
}

export class CasinoTransactionKpisResponseDto {
  @ApiProperty({
    description: 'Última aposta no casino',
    type: LastCasinoBetDto,
    nullable: true,
  })
  lastCasinoBet: LastCasinoBetDto | null;

  @ApiProperty({
    description: 'Total de apostas nos jogos',
    example: 307556.53,
  })
  totalStake: number;

  @ApiProperty({
    description: 'Ganhos totais nos jogos',
    example: 106757.92,
  })
  totalWin: number;

  @ApiProperty({
    description: 'Desempenho do jogador',
    type: PerformanceDto,
  })
  performance: PerformanceDto;
}

export const CasinoTransactionKpisResponseDtoExample = {
  lastCasinoBet: {
    date: '2026-01-14T20:47:40.196Z',
    amount: 3.6,
  },
  totalStake: 307556.53,
  totalWin: 106757.92,
  performance: {
    profitLoss: -200798.61,
    roi: -65.29,
  },
};
