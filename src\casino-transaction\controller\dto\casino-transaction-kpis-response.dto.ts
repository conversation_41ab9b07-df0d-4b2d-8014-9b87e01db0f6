import { ApiProperty } from '@nestjs/swagger';

export class CasinoTransactionKpisResponseDto {
  @ApiProperty({ description: 'Última aposta no casino' })
  lastCasinoBet: number;

  @ApiProperty({ description: 'Total de apostas no casino' })
  totalStake: number;

  @ApiProperty({ description: 'Total de vitórias no casino' })
  totalWin: number;

  @ApiProperty({ description: 'Desempenho do jogador' })
  performance: {
    profitLoss: number;
    roi: number;
  };

  @ApiProperty({ description: 'Bônus do jogador' })
  bonus: {
    stake: number;
    win: number;
  };
}

export const CasinoTransactionKpisResponseDtoExample = {
  lastCasinoBet: 100,
  totalStake: 1000,
  totalWin: 800,
  performance: {
    profitLoss: 200,
    roi: 20,
  },
  bonus: {
    stake: 100,
    win: 50,
  },
};
