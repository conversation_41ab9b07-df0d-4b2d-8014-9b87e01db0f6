import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  Unique,
  UpdateDateColumn,
} from 'typeorm';

@Entity({ schema: 'casino', name: 'daily_report_providers' })
@Unique('uq_daily_report_providers_provider_partner_date', [
  'gameProvider',
  'partnerId',
  'date',
  'gameProviderId',
])
export class DailyReportProviders {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'game_provider', type: 'varchar', nullable: true })
  gameProvider: string;

  @Column({ type: 'varchar', nullable: true })
  currency: string;

  @Column('numeric', { name: 'total_bet', nullable: true })
  totalBet: number;

  @Column('numeric', { name: 'total_settle', nullable: true })
  totalSettle: number;

  @Column('numeric', { nullable: true })
  ggr: number;

  @Column('numeric', { name: 'total_round', nullable: true })
  totalRound: number;

  @Column('uuid', { name: 'partner_id', nullable: true })
  partnerId: string;

  @Column({ type: 'date', nullable: true })
  date: Date;

  @Column({ name: 'game_provider_id', type: 'uuid', nullable: true })
  gameProviderId: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;
}
