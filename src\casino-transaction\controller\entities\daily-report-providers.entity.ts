import { Entity, PrimaryGeneratedColumn, Column } from "typeorm";

@Entity({ schema: "casino", name: "daily_report_providers" })
export class DailyReportProviders {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "game_provider", type: "varchar", nullable: true })
  gameProvider: string;

  @Column({ type: "varchar", nullable: true })
  currency: string;

  @Column("numeric", { name: "total_bet", nullable: true })
  totalBet: number;

  @Column("numeric", { name: "total_settle", nullable: true })
  totalSettle: number;

  @Column("numeric", { nullable: true })
  ggr: number;

  @Column("numeric", { name: "total_round", nullable: true })
  totalRound: number;

  @Column("uuid", { name: "partner_id", nullable: true })
  partnerId: string;

  @Column({ type: "timestamptz", nullable: true })
  date: Date;
}
