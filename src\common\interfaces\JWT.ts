import { GenderEnum } from "@/gamelaunch/enum/gender.enum";
import { UUID } from "crypto";

export interface Jwt {
  payload: {
    id: UUID;
    currency: string;
    language: string;
    country: string;
    firstName: string;
    lastName: string;
    alias: string;
    gender: GenderEnum;
    balance: string;
    email: string;
    password: string;
    partnerId: string;
    partner: {
      id: UUID;
      cashierUrl: string;
      closeUrl: string;
      name: string;
    };
  };
  iat: number;
  exp: number;
}
