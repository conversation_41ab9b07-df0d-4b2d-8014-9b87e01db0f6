import {
  Injectable,
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

@Injectable()
export class BackofficeGuard implements CanActivate {
  constructor(private jwtService: JwtService) {}
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest();

    const token = this.extractTokenFromHeader(req);
    if (!token) {
      throw new HttpException(
        'No authentication header provided.',
        HttpStatus.UNAUTHORIZED,
      );
    }
    const isValid = await this.isTokenValid(token, req);

    if (!isValid) {
      throw new HttpException(
        'Authentication failed: Token expired or invalid.',
        HttpStatus.FORBIDDEN,
      );
    }

    const payload = JSON.parse(
      Buffer.from(token.split('.')[1], 'base64').toString(),
    );

    req.user = payload;
    return true;
  }

  private async isTokenValid(token: string, req): Promise<boolean> {
    try {
      const jwt = await this.jwtService.verify(token, {
        secret: process.env.JWT_SECRET,
      });

      const url = `${process.env.BASE_BACKOFFICE_URL}/v1/pam/users/${jwt.userId}/check-permission?method=${req.method}&path=${req.route.path}&customerPartnerId=${req.headers['partner-id']}`;
      const response = await fetch(url, {
        method: 'GET',
        headers: { Authorization: `Bearer ${token}` },
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Erro ao validar token:', error);
      const errorStatus = error.expiredAt
        ? HttpStatus.UNAUTHORIZED
        : HttpStatus.INTERNAL_SERVER_ERROR;
      throw new HttpException(
        error.message || 'Erro ao executar a validação do token',
        errorStatus,
      );
    }
  }

  private extractTokenFromHeader(req: Request): string | null {
    const authorizationHeader = req.headers.authorization;
    if (!authorizationHeader) {
      return null;
    }
    const [bearer, token] = authorizationHeader.split(' ');
    return bearer === 'Bearer' ? token : null;
  }
}
