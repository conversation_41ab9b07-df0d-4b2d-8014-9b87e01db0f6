import { UUID } from 'crypto';
import { GameModeEnum } from '../enum/game-mode.enum';
import { GenderEnum } from '../enum/gender.enum';

export interface IParams {
  gameMode: GameModeEnum;
  gameId: string;
  playerId: UUID;
  partnerId: UUID;
  partnerCloseUrl: string;
  playerAlias: string;
  playerBalance: number;
  playerCountry: string;
  playerCurrency: string;
  playerEmail: string;
  playerFirstName: string;
  playerGender: GenderEnum;
  playerLanguage: string;
  playerLastName: string;
  requestId: UUID;
  partnerCashierUrl: string;
}
