import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ErrorCode } from '../enums/error-codes.enum';
import { BusinessException } from '../exceptions/business.exception';

/**
 * Standardized error response interface
 */
export interface ErrorResponse {
  traceId: string;
  code: ErrorCode | string;
  message: string;
  meta?: Record<string, any>;
}

/**
 * Global exception filter that catches all exceptions and formats them
 * in a standardized format with traceId, code, message, and metadata
 */
@Catch()
export class BusinessExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(BusinessExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    // Extract or generate traceId from headers
    const traceId =
      (request.headers['trace-id'] as string) ||
      (request.headers['traceid'] as string);

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let errorResponse: ErrorResponse;

    if (exception instanceof BusinessException) {
      // Handle our custom business exceptions
      status = exception.getStatus();
      errorResponse = {
        traceId,
        code: exception.getErrorCode(),
        message: exception.message,
        meta: exception.getMetadata(),
      };

      this.logger.warn(
        `Business Exception: ${JSON.stringify({
          traceId,
          code: exception.getErrorCode(),
          message: exception.message,
          meta: exception.getMetadata(),
        })}`,
      );
    } else if (exception instanceof HttpException) {
      // Handle standard NestJS HttpExceptions
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();

      errorResponse = {
        traceId,
        code: 'HTTP_EXCEPTION',
        message:
          typeof exceptionResponse === 'string'
            ? exceptionResponse
            : (exceptionResponse as any).message || exception.message,
      };

      this.logger.warn(
        `HTTP Exception: ${JSON.stringify({
          traceId,
          status,
          message: exception.message,
        })}`,
      );
    } else {
      // Handle unknown errors
      const error = exception as Error;
      errorResponse = {
        traceId,
        code: ErrorCode.INTERNAL_ERROR,
        message: 'Internal server error',
      };

      this.logger.error(
        `Unhandled Exception: ${JSON.stringify({
          traceId,
          message: error.message,
        })}`,
      );
    }

    response.status(status).json(errorResponse);
  }
}
