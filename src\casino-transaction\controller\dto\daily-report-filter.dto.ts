import { GenericFilter } from '@/common/filters/generic-filter.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class DailyReportGameFilterDto extends GenericFilter {
  @ApiPropertyOptional()
  fromDate: string;

  @ApiPropertyOptional()
  toDate: string;

  @ApiPropertyOptional()
  gameId: string;

  @ApiPropertyOptional()
  provider: string;

  @ApiPropertyOptional()
  accountType: string;
}

export class DailyReportGamePlayerPartnerFilterDto extends GenericFilter {
  @ApiPropertyOptional()
  fromDate: string;

  @ApiPropertyOptional()
  toDate: string;

  @ApiPropertyOptional()
  gameId: string;

  @ApiPropertyOptional()
  provider: string;

  @ApiPropertyOptional()
  playerId: string;
}
