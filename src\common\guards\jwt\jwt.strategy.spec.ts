import { Test, TestingModule } from '@nestjs/testing';
import { JwtStrategy } from './jwt.strategy';
import { PamService } from '../../../pam/pam.service';
import { TokenPayload } from '../../entities/token-payload.entity';

describe('JwtStrategy', () => {
  let strategy: JwtStrategy;
  let pamService: PamService;

  const mockPamService = {
    getUser: jest.fn(),
  };

  beforeEach(async () => {
    // Set JWT_SECRET BEFORE creating the module
    process.env.JWT_SECRET = 'test-jwt-secret';
    process.env.JWT_EXPIRATION = '1h';

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JwtStrategy,
        { provide: PamService, useValue: mockPamService },
      ],
    }).compile();

    strategy = module.get<JwtStrategy>(JwtStrategy);
    pamService = module.get<PamService>(PamService);
    jest.clearAllMocks();
  });

  afterEach(() => {
    delete process.env.JWT_SECRET;
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  describe('validate', () => {
    it('deve retornar usuário quando encontrado e 2FA não está habilitado', async () => {
      const mockPayload: TokenPayload = {
        email: '<EMAIL>',
        iat: **********,
        exp: **********,
        isTwoFactorAuthenticationEnabled: false,
      } as TokenPayload;

      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        isTwoFactorAuthenticationEnabled: false,
      };

      const mockRequest = {
        headers: { authorization: 'Bearer token' },
      };

      mockPamService.getUser.mockResolvedValue(mockUser);

      const result = await strategy.validate(mockRequest, mockPayload);

      expect(result).toEqual(mockUser);
      expect(pamService.getUser).toHaveBeenCalledWith(
        '<EMAIL>',
        mockRequest,
      );
    });

    it('deve retornar true quando usuário tem 2FA habilitado', async () => {
      const mockPayload: TokenPayload = {
        email: '<EMAIL>',
        iat: **********,
        exp: **********,
        isTwoFactorAuthenticationEnabled: true,
      } as TokenPayload;

      const mockUser = {
        id: 'user-456',
        email: '<EMAIL>',
        name: 'Test User',
        isTwoFactorAuthenticationEnabled: true,
      };

      const mockRequest = {
        headers: { authorization: 'Bearer token' },
      };

      mockPamService.getUser.mockResolvedValue(mockUser);

      const result = await strategy.validate(mockRequest, mockPayload);

      expect(result).toBe(true);
      expect(pamService.getUser).toHaveBeenCalledWith(
        '<EMAIL>',
        mockRequest,
      );
    });

    it('deve retornar undefined quando usuário não é encontrado', async () => {
      const mockPayload: TokenPayload = {
        email: '<EMAIL>',
        iat: **********,
        exp: **********,
        isTwoFactorAuthenticationEnabled: false,
      } as TokenPayload;

      const mockRequest = {
        headers: { authorization: 'Bearer token' },
      };

      mockPamService.getUser.mockResolvedValue(null);

      const result = await strategy.validate(mockRequest, mockPayload);

      expect(result).toBeUndefined();
      expect(pamService.getUser).toHaveBeenCalledWith(
        '<EMAIL>',
        mockRequest,
      );
    });

    it('deve chamar getUser com email correto do payload', async () => {
      const mockPayload: TokenPayload = {
        email: '<EMAIL>',
        iat: **********,
        exp: **********,
        isTwoFactorAuthenticationEnabled: false,
      } as TokenPayload;

      const mockUser = {
        id: 'user-specific',
        email: '<EMAIL>',
        isTwoFactorAuthenticationEnabled: false,
      };

      const mockRequest = {};

      mockPamService.getUser.mockResolvedValue(mockUser);

      await strategy.validate(mockRequest, mockPayload);

      expect(pamService.getUser).toHaveBeenCalledWith(
        '<EMAIL>',
        mockRequest,
      );
    });

    it('deve passar request completo para getUser', async () => {
      const mockPayload: TokenPayload = {
        email: '<EMAIL>',
        iat: **********,
        exp: **********,
        isTwoFactorAuthenticationEnabled: false,
      } as TokenPayload;

      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        isTwoFactorAuthenticationEnabled: false,
      };

      const mockRequest = {
        headers: {
          authorization: 'Bearer token',
          'x-custom-header': 'value',
        },
        body: { data: 'test' },
      };

      mockPamService.getUser.mockResolvedValue(mockUser);

      await strategy.validate(mockRequest, mockPayload);

      expect(pamService.getUser).toHaveBeenCalledWith(
        '<EMAIL>',
        mockRequest,
      );
    });

    it('deve propagar erro quando getUser falha', async () => {
      const mockPayload: TokenPayload = {
        email: '<EMAIL>',
        iat: **********,
        exp: **********,
        isTwoFactorAuthenticationEnabled: false,
      } as TokenPayload;

      const mockRequest = {};

      mockPamService.getUser.mockRejectedValue(new Error('Service error'));

      await expect(strategy.validate(mockRequest, mockPayload)).rejects.toThrow(
        'Service error',
      );
    });
  });
});
