import { ApiProperty } from '@nestjs/swagger';

export class DailyReportPartnerResponseDto {
  @ApiProperty({ description: 'ID do parceiro' })
  partnerId: string;

  @ApiProperty({ description: 'Nome do parceiro' })
  partnerName: string;

  @ApiProperty({ description: 'Total de apostas' })
  totalBet: number;

  @ApiProperty({ description: 'Total de ganhos' })
  totalSettle: number;

  @ApiProperty({ description: 'Moeda' })
  currency: string;

  @ApiProperty({ description: 'GGR (Gross Gaming Revenue)' })
  ggr: number;

  @ApiProperty({ description: 'Total de rodadas' })
  totalRound: number;

  @ApiProperty({ description: 'Data do relatório' })
  date: Date;
} 