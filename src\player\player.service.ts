import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { Request } from 'express';
import { firstValueFrom } from 'rxjs';
import { PlayerResponseDto } from './dto/player-response.dto';

@Injectable()
export class PlayerService {
  constructor(private readonly httpService: HttpService) {}
  private readonly urlPlayer = `${process.env.API_PLAYER}/v1/pam`;
  private readonly logger = new Logger(PlayerService.name);

  public async getPlayerById(
    playerId: string,
    req: Request,
  ): Promise<PlayerResponseDto> {
    this.logger.log(`[Inicio] getPlayerById:${playerId}`);

    const auth = req.headers.authorization;
    const authUserHeader = req.headers['x-user-context'];

    try {
      const response = await firstValueFrom(
        this.httpService.get<PlayerResponseDto>(
          `${this.urlPlayer}/player/${playerId}/id`,
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: auth,
              'x-user-context': authUserHeader,
            },
          },
        ),
      );

      this.logger.log(`[Sucesso] getPlayerById: ${response.data.id}`);
      return response.data;
    } catch (error) {
      this.logger.error(
        `[Erro] getPlayerById: ${JSON.stringify(error.response?.data)}`,
      );

      throw new HttpException(
        error.response?.data?.message ||
          'Erro ao buscar informações do jogador',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
