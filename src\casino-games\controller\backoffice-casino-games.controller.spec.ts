import { Test, TestingModule } from '@nestjs/testing';
import { BackofficeCasinoGamesController } from './backoffice-casino-games.controller';
import { BackofficeCasinoGamesService } from '../services/backoffice-casino-games.service';
import { BackofficeGuard } from '@/common/guards/backoffice/backoffice.guard';

describe('BackofficeCasinoGamesController', () => {
  let controller: BackofficeCasinoGamesController;

  const mockBackofficeCasinoGamesService = {};
  const mockBackofficeGuard = { canActivate: jest.fn(() => true) };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BackofficeCasinoGamesController],
      providers: [
        { provide: BackofficeCasinoGamesService, useValue: mockBackofficeCasinoGamesService },
      ],
    })
      .overrideGuard(BackofficeGuard)
      .useValue(mockBackofficeGuard)
      .compile();

    controller = module.get<BackofficeCasinoGamesController>(BackofficeCasinoGamesController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
}); 