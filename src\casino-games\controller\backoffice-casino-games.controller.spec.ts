import { Test, TestingModule } from '@nestjs/testing';
import { BackofficeCasinoGamesController } from './backoffice-casino-games.controller';
import { BackofficeCasinoGamesService } from '../services/backoffice-casino-games.service';
import { BackofficeGuard } from '@/common/guards/backoffice/backoffice.guard';
import { CreateCasinoGameDto } from '../dto/create-casino-game.dto';
import { UpdateCasinoGameDto } from '../dto/update-casino-game.dto';
import { CasinoGames } from '../entities/casino-game.entity';

describe('BackofficeCasinoGamesController', () => {
  let controller: BackofficeCasinoGamesController;
  let casinoGamesService: jest.Mocked<BackofficeCasinoGamesService>;

  const mockBackofficeCasinoGamesService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    getAllProvides: jest.fn(),
  };

  const mockBackofficeGuard = { canActivate: jest.fn(() => true) };

  const mockRequest = {
    headers: {
      'partner-id': 'test-partner-id',
      'client-ip': '127.0.0.1',
    },
  } as any;

  const mockCasinoGame: CasinoGames = {
    id: 'game-uuid-123',
    name: 'Fortune Tiger',
    description: 'Jogo de slots com tema de tigre',
    gameId: '12345',
    gameProvider: 'Pragmatic Play',
    isDisable: false,
    image: 'https://example.com/games/fortune-tiger.jpg',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    deletedAt: null,
    isDeleted: '0',
    sessions: [],
  };

  const mockCreateCasinoGameDto: CreateCasinoGameDto = {
    id: 'game-123',
    name: 'Fortune Tiger',
    description: 'Jogo de slots com tema de tigre',
    image: 'https://example.com/games/fortune-tiger.jpg',
    isDisable: false,
    gameId: '12345',
    gameProvider: 'Pragmatic Play',
    userLastUpdate: 'admin',
  };

  const mockUpdateCasinoGameDto: UpdateCasinoGameDto = {
    name: 'Fortune Tiger Updated',
    description: 'Jogo de slots atualizado',
    isDisable: true,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BackofficeCasinoGamesController],
      providers: [
        { provide: BackofficeCasinoGamesService, useValue: mockBackofficeCasinoGamesService },
      ],
    })
      .overrideGuard(BackofficeGuard)
      .useValue(mockBackofficeGuard)
      .compile();

    controller = module.get<BackofficeCasinoGamesController>(BackofficeCasinoGamesController);
    casinoGamesService = module.get(BackofficeCasinoGamesService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    const successMessage = 'The record has been successfully created.';

    beforeEach(() => {
      casinoGamesService.create.mockResolvedValue(successMessage);
    });

    it('should create a new casino game successfully', async () => {
      // Act
      const result = await controller.create(mockCreateCasinoGameDto);

      // Assert
      expect(result).toBe(successMessage);
      expect(casinoGamesService.create).toHaveBeenCalledWith(mockCreateCasinoGameDto);
    });

    it('should handle creation with all required fields', async () => {
      // Arrange
      const completeGameDto: CreateCasinoGameDto = {
        id: 'complete-game-123',
        name: 'Complete Game',
        description: 'A complete game description',
        image: 'https://example.com/complete-game.jpg',
        isDisable: false,
        gameId: '54321',
        gameProvider: 'Complete Provider',
        userLastUpdate: 'test-admin',
      };

      // Act
      const result = await controller.create(completeGameDto);

      // Assert
      expect(result).toBe(successMessage);
      expect(casinoGamesService.create).toHaveBeenCalledWith(completeGameDto);
    });

    it('should handle creation with disabled game', async () => {
      // Arrange
      const disabledGameDto: CreateCasinoGameDto = {
        ...mockCreateCasinoGameDto,
        isDisable: true,
      };

      // Act
      const result = await controller.create(disabledGameDto);

      // Assert
      expect(result).toBe(successMessage);
      expect(casinoGamesService.create).toHaveBeenCalledWith(disabledGameDto);
    });

    it('should propagate service errors', async () => {
      // Arrange
      const error = new Error('Game already exists');
      casinoGamesService.create.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.create(mockCreateCasinoGameDto)).rejects.toThrow('Game already exists');
      expect(casinoGamesService.create).toHaveBeenCalledWith(mockCreateCasinoGameDto);
    });

    it('should handle creation with special characters in name', async () => {
      // Arrange
      const specialCharGameDto: CreateCasinoGameDto = {
        ...mockCreateCasinoGameDto,
        name: 'Jogo Especial: Açúcar & Mel™ (2024)',
        description: 'Descrição com acentos: ção, ã, é, ü',
      };

      // Act
      const result = await controller.create(specialCharGameDto);

      // Assert
      expect(result).toBe(successMessage);
      expect(casinoGamesService.create).toHaveBeenCalledWith(specialCharGameDto);
    });
  });

  describe('getAllProvides', () => {
    const mockProviders = [
      'AGS',
      'BETKING',
      'BETRIVER',
      'BETSOFT',
      'BETWIN',
      'EVO',
      'PRAGMATIC',
    ];

    beforeEach(() => {
      casinoGamesService.getAllProvides.mockResolvedValue(mockProviders);
    });

    it('should return all game providers successfully', async () => {
      // Act
      const result = await controller.getAllProvides();

      // Assert
      expect(result).toEqual(mockProviders);
      expect(casinoGamesService.getAllProvides).toHaveBeenCalled();
    });

    it('should return empty array when no providers exist', async () => {
      // Arrange
      casinoGamesService.getAllProvides.mockResolvedValue([]);

      // Act
      const result = await controller.getAllProvides();

      // Assert
      expect(result).toEqual([]);
      expect(casinoGamesService.getAllProvides).toHaveBeenCalled();
    });

    it('should return providers in sorted order', async () => {
      // Arrange
      const unsortedProviders = ['PRAGMATIC', 'AGS', 'BETSOFT', 'EVO'];
      casinoGamesService.getAllProvides.mockResolvedValue(unsortedProviders);

      // Act
      const result = await controller.getAllProvides();

      // Assert
      expect(result).toEqual(unsortedProviders);
      expect(casinoGamesService.getAllProvides).toHaveBeenCalled();
    });

    it('should handle single provider', async () => {
      // Arrange
      const singleProvider = ['PRAGMATIC'];
      casinoGamesService.getAllProvides.mockResolvedValue(singleProvider);

      // Act
      const result = await controller.getAllProvides();

      // Assert
      expect(result).toEqual(singleProvider);
      expect(casinoGamesService.getAllProvides).toHaveBeenCalled();
    });

    it('should propagate service errors', async () => {
      // Arrange
      const error = new Error('Database connection failed');
      casinoGamesService.getAllProvides.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getAllProvides()).rejects.toThrow('Database connection failed');
      expect(casinoGamesService.getAllProvides).toHaveBeenCalled();
    });
  });

  describe('findAll', () => {
    const mockGames: CasinoGames[] = [
      {
        id: 'game-1',
        name: 'Game 1',
        description: 'Description 1',
        gameId: '1001',
        gameProvider: 'Provider A',
        isDisable: false,
        image: 'https://example.com/game1.jpg',
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        deletedAt: null,
        isDeleted: '0',
        sessions: [],
      },
      {
        id: 'game-2',
        name: 'Game 2',
        description: 'Description 2',
        gameId: '1002',
        gameProvider: 'Provider B',
        isDisable: true,
        image: 'https://example.com/game2.jpg',
        createdAt: new Date('2024-01-02'),
        updatedAt: new Date('2024-01-02'),
        deletedAt: null,
        isDeleted: '0',
        sessions: [],
      },
    ];

    beforeEach(() => {
      casinoGamesService.findAll.mockResolvedValue(mockGames);
    });

    it('should return all casino games successfully', async () => {
      // Act
      const result = await controller.findAll(mockRequest);

      // Assert
      expect(result).toEqual(mockGames);
      expect(casinoGamesService.findAll).toHaveBeenCalledWith(mockRequest);
    });

    it('should return empty array when no games exist', async () => {
      // Arrange
      casinoGamesService.findAll.mockResolvedValue([]);

      // Act
      const result = await controller.findAll(mockRequest);

      // Assert
      expect(result).toEqual([]);
      expect(casinoGamesService.findAll).toHaveBeenCalledWith(mockRequest);
    });

    it('should handle request without headers', async () => {
      // Arrange
      const requestWithoutHeaders = {} as any;

      // Act
      const result = await controller.findAll(requestWithoutHeaders);

      // Assert
      expect(result).toEqual(mockGames);
      expect(casinoGamesService.findAll).toHaveBeenCalledWith(requestWithoutHeaders);
    });

    it('should propagate service errors', async () => {
      // Arrange
      const error = new Error('Database query failed');
      casinoGamesService.findAll.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.findAll(mockRequest)).rejects.toThrow('Database query failed');
      expect(casinoGamesService.findAll).toHaveBeenCalledWith(mockRequest);
    });

    it('should handle large number of games', async () => {
      // Arrange
      const largeGamesList = Array.from({ length: 1000 }, (_, index) => ({
        ...mockCasinoGame,
        id: `game-${index}`,
        name: `Game ${index}`,
        gameId: `${1000 + index}`,
      }));
      casinoGamesService.findAll.mockResolvedValue(largeGamesList);

      // Act
      const result = await controller.findAll(mockRequest);

      // Assert
      expect(result).toEqual(largeGamesList);
      expect(result).toHaveLength(1000);
      expect(casinoGamesService.findAll).toHaveBeenCalledWith(mockRequest);
    });
  });

  describe('findOne', () => {
    const gameId = 'test-game-id';

    beforeEach(() => {
      casinoGamesService.findOne.mockResolvedValue(mockCasinoGame);
    });

    it('should return a specific casino game successfully', async () => {
      // Act
      const result = await controller.findOne(gameId);

      // Assert
      expect(result).toEqual(mockCasinoGame);
      expect(casinoGamesService.findOne).toHaveBeenCalledWith(gameId);
    });

    it('should handle UUID format game ID', async () => {
      // Arrange
      const uuidGameId = '550e8400-e29b-41d4-a716-************';

      // Act
      const result = await controller.findOne(uuidGameId);

      // Assert
      expect(result).toEqual(mockCasinoGame);
      expect(casinoGamesService.findOne).toHaveBeenCalledWith(uuidGameId);
    });

    it('should handle numeric string game ID', async () => {
      // Arrange
      const numericGameId = '12345';

      // Act
      const result = await controller.findOne(numericGameId);

      // Assert
      expect(result).toEqual(mockCasinoGame);
      expect(casinoGamesService.findOne).toHaveBeenCalledWith(numericGameId);
    });

    it('should handle special characters in game ID', async () => {
      // Arrange
      const specialGameId = 'game-123_special!@#';

      // Act
      const result = await controller.findOne(specialGameId);

      // Assert
      expect(result).toEqual(mockCasinoGame);
      expect(casinoGamesService.findOne).toHaveBeenCalledWith(specialGameId);
    });

    it('should propagate service errors for non-existent game', async () => {
      // Arrange
      const error = new Error('Game not found');
      casinoGamesService.findOne.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.findOne(gameId)).rejects.toThrow('Game not found');
      expect(casinoGamesService.findOne).toHaveBeenCalledWith(gameId);
    });

    it('should handle empty string game ID', async () => {
      // Arrange
      const emptyGameId = '';

      // Act
      const result = await controller.findOne(emptyGameId);

      // Assert
      expect(result).toEqual(mockCasinoGame);
      expect(casinoGamesService.findOne).toHaveBeenCalledWith(emptyGameId);
    });
  });

  describe('update', () => {
    const gameId = 'test-game-id';
    const successMessage = 'The record has been successfully updated.';

    beforeEach(() => {
      casinoGamesService.update.mockResolvedValue(successMessage);
    });

    it('should update a casino game successfully', async () => {
      // Act
      const result = await controller.update(gameId, mockUpdateCasinoGameDto);

      // Assert
      expect(result).toBe(successMessage);
      expect(casinoGamesService.update).toHaveBeenCalledWith(gameId, mockUpdateCasinoGameDto);
    });

    it('should handle partial update with single field', async () => {
      // Arrange
      const partialUpdate: UpdateCasinoGameDto = {
        name: 'Updated Game Name Only',
      };

      // Act
      const result = await controller.update(gameId, partialUpdate);

      // Assert
      expect(result).toBe(successMessage);
      expect(casinoGamesService.update).toHaveBeenCalledWith(gameId, partialUpdate);
    });

    it('should handle update with all fields', async () => {
      // Arrange
      const completeUpdate: UpdateCasinoGameDto = {
        id: 'updated-id',
        name: 'Updated Name',
        description: 'Updated Description',
        image: 'https://example.com/updated-image.jpg',
        isDisable: true,
        gameId: '99999',
        gameProvider: 'Updated Provider',
        userLastUpdate: 'updated-admin',
      };

      // Act
      const result = await controller.update(gameId, completeUpdate);

      // Assert
      expect(result).toBe(successMessage);
      expect(casinoGamesService.update).toHaveBeenCalledWith(gameId, completeUpdate);
    });

    it('should handle update with empty DTO', async () => {
      // Arrange
      const emptyUpdate: UpdateCasinoGameDto = {};

      // Act
      const result = await controller.update(gameId, emptyUpdate);

      // Assert
      expect(result).toBe(successMessage);
      expect(casinoGamesService.update).toHaveBeenCalledWith(gameId, emptyUpdate);
    });

    it('should handle update with boolean toggle', async () => {
      // Arrange
      const toggleUpdate: UpdateCasinoGameDto = {
        isDisable: false,
      };

      // Act
      const result = await controller.update(gameId, toggleUpdate);

      // Assert
      expect(result).toBe(successMessage);
      expect(casinoGamesService.update).toHaveBeenCalledWith(gameId, toggleUpdate);
    });

    it('should propagate service errors', async () => {
      // Arrange
      const error = new Error('Update failed');
      casinoGamesService.update.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.update(gameId, mockUpdateCasinoGameDto)).rejects.toThrow('Update failed');
      expect(casinoGamesService.update).toHaveBeenCalledWith(gameId, mockUpdateCasinoGameDto);
    });

    it('should handle UUID game ID in update', async () => {
      // Arrange
      const uuidGameId = '550e8400-e29b-41d4-a716-************';

      // Act
      const result = await controller.update(uuidGameId, mockUpdateCasinoGameDto);

      // Assert
      expect(result).toBe(successMessage);
      expect(casinoGamesService.update).toHaveBeenCalledWith(uuidGameId, mockUpdateCasinoGameDto);
    });
  });

  describe('remove', () => {
    const gameId = 'test-game-id';
    const successMessage = 'The record has been successfully deleted.';

    beforeEach(() => {
      casinoGamesService.remove.mockResolvedValue(successMessage);
    });

    it('should remove a casino game successfully', async () => {
      // Act
      const result = await controller.remove(gameId);

      // Assert
      expect(result).toBe(successMessage);
      expect(casinoGamesService.remove).toHaveBeenCalledWith(gameId);
    });

    it('should handle UUID game ID in removal', async () => {
      // Arrange
      const uuidGameId = '550e8400-e29b-41d4-a716-************';

      // Act
      const result = await controller.remove(uuidGameId);

      // Assert
      expect(result).toBe(successMessage);
      expect(casinoGamesService.remove).toHaveBeenCalledWith(uuidGameId);
    });

    it('should handle numeric string game ID in removal', async () => {
      // Arrange
      const numericGameId = '12345';

      // Act
      const result = await controller.remove(numericGameId);

      // Assert
      expect(result).toBe(successMessage);
      expect(casinoGamesService.remove).toHaveBeenCalledWith(numericGameId);
    });

    it('should handle special characters in game ID for removal', async () => {
      // Arrange
      const specialGameId = 'game-123_special!@#';

      // Act
      const result = await controller.remove(specialGameId);

      // Assert
      expect(result).toBe(successMessage);
      expect(casinoGamesService.remove).toHaveBeenCalledWith(specialGameId);
    });

    it('should propagate service errors for non-existent game', async () => {
      // Arrange
      const error = new Error('Game not found for deletion');
      casinoGamesService.remove.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.remove(gameId)).rejects.toThrow('Game not found for deletion');
      expect(casinoGamesService.remove).toHaveBeenCalledWith(gameId);
    });

    it('should handle empty string game ID in removal', async () => {
      // Arrange
      const emptyGameId = '';

      // Act
      const result = await controller.remove(emptyGameId);

      // Assert
      expect(result).toBe(successMessage);
      expect(casinoGamesService.remove).toHaveBeenCalledWith(emptyGameId);
    });

    it('should handle database constraint errors', async () => {
      // Arrange
      const constraintError = new Error('Cannot delete game with active sessions');
      casinoGamesService.remove.mockRejectedValue(constraintError);

      // Act & Assert
      await expect(controller.remove(gameId)).rejects.toThrow('Cannot delete game with active sessions');
      expect(casinoGamesService.remove).toHaveBeenCalledWith(gameId);
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete CRUD workflow', async () => {
      // Arrange
      const gameId = 'integration-game-123';
      const createDto: CreateCasinoGameDto = {
        id: gameId,
        name: 'Integration Test Game',
        description: 'A game for integration testing',
        image: 'https://example.com/integration-game.jpg',
        isDisable: false,
        gameId: '99999',
        gameProvider: 'Integration Provider',
        userLastUpdate: 'integration-admin',
      };

      const updateDto: UpdateCasinoGameDto = {
        name: 'Updated Integration Game',
        isDisable: true,
      };

      casinoGamesService.create.mockResolvedValue('The record has been successfully created.');
      casinoGamesService.findOne.mockResolvedValue({ ...mockCasinoGame, id: gameId });
      casinoGamesService.update.mockResolvedValue('The record has been successfully updated.');
      casinoGamesService.remove.mockResolvedValue('The record has been successfully deleted.');

      // Act & Assert - Create
      const createResult = await controller.create(createDto);
      expect(createResult).toBe('The record has been successfully created.');

      // Act & Assert - Read
      const findResult = await controller.findOne(gameId);
      expect(findResult.id).toBe(gameId);

      // Act & Assert - Update
      const updateResult = await controller.update(gameId, updateDto);
      expect(updateResult).toBe('The record has been successfully updated.');

      // Act & Assert - Delete
      const deleteResult = await controller.remove(gameId);
      expect(deleteResult).toBe('The record has been successfully deleted.');

      // Verify all service methods were called
      expect(casinoGamesService.create).toHaveBeenCalledWith(createDto);
      expect(casinoGamesService.findOne).toHaveBeenCalledWith(gameId);
      expect(casinoGamesService.update).toHaveBeenCalledWith(gameId, updateDto);
      expect(casinoGamesService.remove).toHaveBeenCalledWith(gameId);
    });

    it('should handle provider workflow', async () => {
      // Arrange
      const providers = ['PRAGMATIC', 'EVO', 'BETSOFT'];
      const gamesForProvider = [
        { ...mockCasinoGame, gameProvider: 'PRAGMATIC' },
        { ...mockCasinoGame, gameProvider: 'PRAGMATIC', id: 'game-2' },
      ];

      casinoGamesService.getAllProvides.mockResolvedValue(providers);
      casinoGamesService.findAll.mockResolvedValue(gamesForProvider);

      // Act
      const providersResult = await controller.getAllProvides();
      const gamesResult = await controller.findAll(mockRequest);

      // Assert
      expect(providersResult).toEqual(providers);
      expect(gamesResult).toEqual(gamesForProvider);
      expect(casinoGamesService.getAllProvides).toHaveBeenCalled();
      expect(casinoGamesService.findAll).toHaveBeenCalledWith(mockRequest);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle concurrent operations', async () => {
      // Arrange
      const gameId1 = 'concurrent-game-1';
      const gameId2 = 'concurrent-game-2';

      casinoGamesService.findOne
        .mockResolvedValueOnce({ ...mockCasinoGame, id: gameId1 })
        .mockResolvedValueOnce({ ...mockCasinoGame, id: gameId2 });

      // Act
      const [result1, result2] = await Promise.all([
        controller.findOne(gameId1),
        controller.findOne(gameId2),
      ]);

      // Assert
      expect(result1.id).toBe(gameId1);
      expect(result2.id).toBe(gameId2);
      expect(casinoGamesService.findOne).toHaveBeenCalledTimes(2);
    });

    it('should handle very long strings in DTOs', async () => {
      // Arrange
      const longString = 'a'.repeat(10000);
      const longStringDto: CreateCasinoGameDto = {
        ...mockCreateCasinoGameDto,
        name: longString,
        description: longString,
      };

      casinoGamesService.create.mockResolvedValue('The record has been successfully created.');

      // Act
      const result = await controller.create(longStringDto);

      // Assert
      expect(result).toBe('The record has been successfully created.');
      expect(casinoGamesService.create).toHaveBeenCalledWith(longStringDto);
    });

    it('should handle null and undefined values in update DTO', async () => {
      // Arrange
      const nullUpdateDto: UpdateCasinoGameDto = {
        name: null as any,
        description: undefined,
        isDisable: null as any,
      };

      casinoGamesService.update.mockResolvedValue('The record has been successfully updated.');

      // Act
      const result = await controller.update('test-id', nullUpdateDto);

      // Assert
      expect(result).toBe('The record has been successfully updated.');
      expect(casinoGamesService.update).toHaveBeenCalledWith('test-id', nullUpdateDto);
    });

    it('should handle network timeout errors', async () => {
      // Arrange
      const timeoutError = new Error('Network timeout');
      timeoutError.name = 'TimeoutError';
      casinoGamesService.findAll.mockRejectedValue(timeoutError);

      // Act & Assert
      await expect(controller.findAll(mockRequest)).rejects.toThrow('Network timeout');
    });

    it('should handle database connection errors', async () => {
      // Arrange
      const dbError = new Error('Database connection lost');
      dbError.name = 'DatabaseError';
      casinoGamesService.create.mockRejectedValue(dbError);

      // Act & Assert
      await expect(controller.create(mockCreateCasinoGameDto)).rejects.toThrow('Database connection lost');
    });
  });

  describe('Guards and Security', () => {
    it('should have BackofficeGuard configured', () => {
      // This test verifies the BackofficeGuard is properly mocked and configured
      expect(mockBackofficeGuard.canActivate).toBeDefined();
      expect(typeof mockBackofficeGuard.canActivate).toBe('function');
    });

    it('should have guard returning true by default', () => {
      // Verify that guard is configured to allow access in tests
      expect(mockBackofficeGuard.canActivate()).toBe(true);
    });

    it('should be able to simulate guard rejection', () => {
      // Arrange
      mockBackofficeGuard.canActivate.mockReturnValue(false);

      // Act & Assert
      expect(mockBackofficeGuard.canActivate()).toBe(false);

      // Reset for other tests
      mockBackofficeGuard.canActivate.mockReturnValue(true);
    });
  });
});