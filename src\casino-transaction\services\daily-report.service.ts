import { CasinoGameProvider } from '@/casino-games/entities/casino-game-provider.entity';
import { CasinoGames } from '@/casino-games/entities/casino-game.entity';
import { GenericFilter } from '@/common/filters/generic-filter.dto';
import { SortOrder } from '@/common/filters/sortOrder';
import { DateUtilsService } from '@/common/utils/date';
import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
} from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import {
  endOfDay,
  isToday,
  isWithinInterval,
  parseISO,
  startOfDay,
  subDays,
} from 'date-fns';
import { Between, EntityManager, IsNull, Not } from 'typeorm';
import { BetReportFilterDto } from '../controller/dto/bet-report-filter.dto';
import {
  DailyReportGameFilterDto,
  DailyReportGamePlayerPartnerFilterDto,
} from '../controller/dto/daily-report-filter.dto';
import { GameProfitabilityFilterDto } from '../controller/dto/game-profitability-filter.dto';
import { GameProfitabilityResponseDto } from '../controller/dto/game-profitability-response.dto';
import { CasinoTransactionEntity } from '../controller/entities/casino-transactions.entity';
import { DailyReportGamePlayerPartner } from '../controller/entities/daily-report-game-player-partner.entity';
import { DailyReportGame } from '../controller/entities/daily-report-game.entity';
import { DailyReportPartner } from '../controller/entities/daily-report-partner.entity';
import { DailyReportPlayer } from '../controller/entities/daily-report-player.entity';
import { DailyReportProviders } from '../controller/entities/daily-report-providers.entity';
import { PartnersEntity } from '../controller/entities/partner.entity';

@Injectable()
export class DailyReportService {
  private readonly logger = new Logger(DailyReportService.name);
  constructor(
    @InjectEntityManager() private readonly manager: EntityManager,
    private readonly dateService: DateUtilsService,
  ) {}

  async saveDailyReportGame(fromDate: Date, toDate: Date) {
    try {
      this.logger.log(`Processando daily report game para o dia: ${fromDate}`);
      const report = await this.manager
        .createQueryBuilder(CasinoTransactionEntity, 'transaction')
        .select('transaction.gameId', 'gameId')
        .addSelect('game.name', 'gameName')
        .addSelect('gameProvider.name', 'gameProvider')
        .addSelect('transaction.partnerId', 'partnerId')
        .addSelect('SUM(transaction.amount)', 'totalBetAmount')
        .addSelect('SUM(transaction.winAmount)', 'totalWinAmount')
        .addSelect('COUNT(transaction.roundEnded)', 'totalBets')
        .addSelect('COUNT(DISTINCT transaction.playerId)', 'totalPlayer')
        .leftJoin(CasinoGames, 'game', 'game.id = transaction.gameId')
        .leftJoin(
          CasinoGameProvider,
          'gameProvider',
          'gameProvider.id = game.gameProviderId',
        )
        .where('transaction.createdAt BETWEEN :fromDate AND :toDate', {
          fromDate,
          toDate,
        })
        .andWhere('transaction.status = :status', { status: 'success' })
        .andWhere('transaction.deletedAt IS NULL')
        .groupBy('transaction.gameId')
        .addGroupBy('game.name')
        .addGroupBy('gameProvider.name')
        .addGroupBy('transaction.partnerId')
        .getRawMany();

      for (const game of report) {
        const existing = await this.manager.findOne(DailyReportGame, {
          where: {
            gameId: game.gameId,
            partnerId: game.partnerId,
            date: fromDate,
          },
        });

        const newData = {
          gameId: game.gameId,
          gameName: game.gameName,
          gameProvider: game.gameProvider,
          partnerId: game.partnerId,
          totalBet: Number(game.totalBetAmount || 0),
          totalSettle: Number(game.totalWinAmount || 0),
          currency: 'BRL',
          ggr:
            Number(game.totalBetAmount || 0) - Number(game.totalWinAmount || 0),
          totalRound: Number(game.totalBets || 0),
          totalPlayers: Number(game.totalPlayer || 0),
          date: fromDate,
        };
        console.log(existing, newData);
        if (existing) {
          await this.manager.update(
            DailyReportGame,
            { id: existing.id },
            newData,
          );
        } else {
          await this.manager.insert(DailyReportGame, newData);
        }
      }
      this.logger.log(`Daily report processado: ${report.length} jogos`);
    } catch (error) {
      this.logger.error(error);
    }
  }

  async getDailyReportGame(
    startDate: string,
    endDate: string,
    req: Request,
    gameProvider?: string,
  ) {
    try {
      const partnerId = req.headers['partner-id'] as string;
      const today = startOfDay(new Date());

      const parsedStartDate = startDate ? parseISO(startDate) : null;
      const parsedEndDate = endDate ? parseISO(endDate) : null;

      const fromDate = parsedStartDate ? startOfDay(parsedStartDate) : null;
      const toDate = parsedEndDate ? endOfDay(parsedEndDate) : null;

      const containsToday =
        fromDate &&
        toDate &&
        isWithinInterval(today, { start: fromDate, end: toDate });

      const previousRange = containsToday
        ? {
            from: fromDate,
            to: endOfDay(subDays(today, 1)),
          }
        : { from: fromDate, to: toDate };

      const todayRange = containsToday
        ? {
            from: startOfDay(today),
            to: endOfDay(today),
          }
        : null;

      const aggregatedConditions: any = {
        partnerId,
        date: previousRange.from
          ? Between(previousRange.from, previousRange.to)
          : Not(IsNull()),
        ...(gameProvider && { gameProvider }),
      };

      let aggregatedData = [];

      if (!containsToday || previousRange.from <= previousRange.to) {
        aggregatedData = await this.manager.find(DailyReportGame, {
          where: aggregatedConditions,
        });
      }

      let todayData = [];

      if (containsToday) {
        const report = await this.manager
          .createQueryBuilder(CasinoTransactionEntity, 'transaction')
          .select('transaction.gameId', 'gameId')
          .addSelect('game.name', 'gameName')
          .addSelect('gameProvider.name', 'gameProvider')
          .addSelect('transaction.partnerId', 'partnerId')
          .addSelect('SUM(transaction.amount)', 'totalBetAmount')
          .addSelect('SUM(transaction.winAmount)', 'totalWinAmount')
          .addSelect('COUNT(transaction.roundEnded)', 'totalBets')
          .addSelect('COUNT(DISTINCT transaction.playerId)', 'totalPlayer')
          .leftJoin(CasinoGames, 'game', 'game.gameId = transaction.gameId')
          .leftJoin(
            CasinoGameProvider,
            'gameProvider',
            'gameProvider.id = game.gameProviderId',
          )
          .where('transaction.partnerId = :partnerId', { partnerId })
          .andWhere(
            gameProvider ? 'game.gameProvider = :gameProvider' : '1=1',
            { gameProvider },
          )
          .andWhere('transaction.createdAt BETWEEN :from AND :to', {
            from: todayRange.from,
            to: todayRange.to,
          })
          .groupBy('transaction.gameId')
          .addGroupBy('game.name')
          .addGroupBy('gameProvider.name')
          .addGroupBy('transaction.partnerId')
          .getRawMany();

        todayData = report.map(game => ({
          gameId: game.gameId,
          gameName: game.gameName,
          gameProvider: game.gameProviderName,
          partnerId: game.partnerId,
          totalBet: Number(game.totalBetAmount),
          totalSettle: Number(game.totalWinAmount),
          currency: 'BRL',
          ggr: Number(game.totalBetAmount) - Number(game.totalWinAmount),
          totalRound: Number(game.totalBets),
          totalPlayers: Number(game.totalPlayer),
          date: today,
        }));
      }

      return [...aggregatedData, ...todayData];
    } catch (e) {
      this.logger.error(`[Error] erro ao consultar report game`);
      throw new HttpException(
        `Erro ao buscar report game: ${e.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getDailyReportGamePagination(
    filter: DailyReportGameFilterDto,
    req: Request,
  ) {
    try {
      const partnerId = req.headers['partner-id'] as string;
      const { fromDate, toDate, gameId, provider } = filter;

      if (!fromDate || !toDate) {
        throw new BadRequestException(
          'É necessário informar a data inicial e final',
        );
      }
      const parsedEndDate = parseISO(toDate);
      if (isToday(parsedEndDate)) {
        const today = new Date();
        const todayRange = this.dateService.getDateRange(
          today.toISOString(),
          today.toISOString(),
        );

        await this.saveDailyReportGame(todayRange.fromDate, todayRange.toDate);

        this.logger.log(
          'Dados do dia atual atualizados na tabela daily_report_game',
        );
      }

      const date = this.dateService.getDateRange(fromDate, toDate);

      const page = !filter.page || filter.page < 1 ? 1 : filter.page;
      const pageSize =
        !filter.pageSize || filter.pageSize < 1 ? 10 : filter.pageSize;
      const offset = (page - 1) * pageSize;
      const orderBy = [
        'gameId',
        'gameName',
        'gameProvider',
        'currency',
      ].includes(filter.orderBy)
        ? filter.orderBy
        : 'gameId';
      const sortOrder = filter.sortOrder || SortOrder.ASC;

      const qb = this.manager
        .createQueryBuilder(DailyReportGame, 'drg')
        .select([
          'drg.partnerId AS "partnerId"',
          'drg.gameId AS "gameId"',
          'MAX(drg.gameName) AS "gameName"',
          'MAX(drg.gameProvider) AS "gameProvider"',
          'MAX(drg.currency) AS "currency"',
          'SUM(drg.totalBet) AS "totalBet"',
          'SUM(drg.totalSettle) AS "totalSettle"',
          'SUM(drg.ggr) AS "ggr"',
          'SUM(drg.totalRound) AS "totalRound"',
          'SUM(drg.totalPlayers) AS "totalPlayers"',
        ])
        .where('drg.partnerId = :partnerId', { partnerId })
        .andWhere('drg.date BETWEEN :fromDate AND :toDate', {
          fromDate: date.fromDate,
          toDate: date.toDate,
        })
        .groupBy(
          'drg.partnerId, drg.gameId, drg.gameName, drg.gameProvider, drg.currency',
        )
        .orderBy(`"${orderBy}"`, sortOrder)
        .limit(pageSize)
        .offset(offset);

      if (gameId) qb.andWhere('drg.gameId = :gameId', { gameId });
      if (provider) qb.andWhere('drg.gameProvider = :provider', { provider });

      const rawResult = await qb.getRawMany();

      const countQb = this.manager
        .createQueryBuilder(DailyReportGame, 'drg')
        .select('COUNT(DISTINCT drg.gameId)', 'totalItems')
        .where('drg.partnerId = :partnerId', { partnerId })
        .andWhere('drg.date BETWEEN :fromDate AND :toDate', {
          fromDate: date.fromDate,
          toDate: date.toDate,
        });

      if (gameId) countQb.andWhere('drg.gameId = :gameId', { gameId });
      if (provider)
        countQb.andWhere('drg.gameProvider = :provider', { provider });

      const countResult = await countQb.getRawOne();
      const result = rawResult.map(item => ({
        ...item,
        totalBet: Number(item.totalBet),
        totalSettle: Number(item.totalSettle),
        ggr: Number(item.ggr),
        totalRound: Number(item.totalRound),
        totalPlayers: Number(item.totalPlayers),
      }));

      return {
        data: result,
        totalItems: Number(countResult.totalItems),
        currentPage: page,
        pageSize,
        totalPages: Math.ceil(Number(countResult.totalItems) / pageSize),
      };
    } catch (e) {
      this.logger.error(`[Error] erro ao consultar report game`);
      throw new HttpException(
        `Erro ao buscar report game: ${e.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async saveDailyReportGamePlayerPartner(fromDate: Date, toDate: Date) {
    try {
      this.logger.log(
        `Processando daily report game por player para o dia: ${fromDate}`,
      );

      const report = await this.manager
        .createQueryBuilder(CasinoTransactionEntity, 'transaction')
        .select('transaction.playerId', 'playerId')
        .addSelect('transaction.gameId', 'gameId')
        .addSelect('transaction.partnerId', 'partnerId')
        .addSelect('transaction.currency', 'currency')
        .addSelect('game.name', 'gameName')
        .addSelect('gameProvider.name', 'gameProvider')
        .addSelect('game.gameProviderId', 'gameProviderId')
        .addSelect('SUM(transaction.amount)', 'totalBetAmount')
        .addSelect('SUM(transaction.winAmount)', 'totalWinAmount')
        .addSelect('MAX(session.email)', 'email')
        .leftJoin(CasinoGames, 'game', 'game.id = transaction.gameId')
        .leftJoin(
          CasinoGameProvider,
          'gameProvider',
          'gameProvider.id = game.gameProviderId',
        )
        .leftJoin('transaction.session', 'session')
        .where('transaction.createdAt BETWEEN :fromDate AND :toDate', {
          fromDate,
          toDate,
        })
        .andWhere('transaction.status = :status', { status: 'success' })
        .andWhere('transaction.deletedAt IS NULL')
        .groupBy('transaction.playerId')
        .addGroupBy('transaction.gameId')
        .addGroupBy('transaction.partnerId')
        .addGroupBy('transaction.currency')
        .addGroupBy('game.name')
        .addGroupBy('game.gameProviderId')
        .addGroupBy('gameProvider.name')
        .getRawMany();

      if (report.length === 0) {
        this.logger.log('Nenhuma transação encontrada para processar');
        return;
      }

      // Preparar dados para bulk upsert
      const dataToUpsert = report.map(game => ({
        playerId: game.playerId,
        gameId: game.gameId,
        gameName: game.gameName,
        gameProvider: game.gameProvider,
        gameProviderId: game.gameProviderId,
        partnerId: game.partnerId,
        email: game.email || null,
        totalBet: Number(game.totalBetAmount || 0),
        totalSettle: Number(game.totalWinAmount || 0),
        currency: game.currency || 'BRL',
        ggr:
          Number(game.totalBetAmount || 0) - Number(game.totalWinAmount || 0),
        date: fromDate,
      }));

      // Usar bulk upsert do PostgreSQL (muito mais performático)
      await this.manager
        .createQueryBuilder()
        .insert()
        .into(DailyReportGamePlayerPartner)
        .values(dataToUpsert)
        .orUpdate(
          [
            'game_name',
            'game_provider',
            'game_provider_id',
            'email',
            'total_bet',
            'total_settle',
            'ggr',
            'total_round',
            'currency',
          ],
          ['player_id', 'game_id', 'partner_id', 'date'],
        )
        .execute();

      this.logger.log(
        `Daily report processado: ${report.length} jogos por player`,
      );
    } catch (error) {
      this.logger.error(
        `Erro ao salvar daily report game player partner: ${error.message}`,
      );
      throw error;
    }
  }

  async getDailyReportGamePlayerPartner(
    startDate: string,
    endDate: string,
    req: Request,
  ) {
    try {
      const partnerId = req.headers['partner-id'] as string;

      const today = startOfDay(new Date());

      const parsedStartDate = startDate ? parseISO(startDate) : null;
      const parsedEndDate = endDate ? parseISO(endDate) : null;

      const fromDate = parsedStartDate ? startOfDay(parsedStartDate) : null;
      const toDate = parsedEndDate ? endOfDay(parsedEndDate) : null;

      const containsToday =
        fromDate &&
        toDate &&
        isWithinInterval(today, { start: fromDate, end: toDate });

      const previousRange = containsToday
        ? {
            from: fromDate,
            to: endOfDay(subDays(today, 1)),
          }
        : { from: fromDate, to: toDate };

      const todayRange = containsToday
        ? {
            from: startOfDay(today),
            to: endOfDay(today),
          }
        : null;

      const aggregatedConditions: any = {
        partnerId,
        date: previousRange.from
          ? Between(previousRange.from, previousRange.to)
          : Not(IsNull()),
      };

      let aggregatedData = [];

      if (!containsToday || previousRange.from <= previousRange.to) {
        aggregatedData = await this.manager.find(DailyReportGamePlayerPartner, {
          where: aggregatedConditions,
        });
      }

      let todayData = [];

      if (containsToday) {
        const report = await this.manager
          .createQueryBuilder(CasinoTransactionEntity, 'transaction')
          .select('transaction.playerId', 'playerId')
          .addSelect('transaction.gameId', 'gameId')
          .addSelect('transaction.partnerId', 'partnerId')
          .addSelect('transaction.currency', 'currency')
          .addSelect('MAX(game.name)', 'gameName')
          .addSelect('MAX(gameProvider.name)', 'gameProvider')
          .addSelect('SUM(transaction.amount)', 'totalBetAmount')
          .addSelect('SUM(transaction.winAmount)', 'totalWinAmount')
          .addSelect('COUNT(*)', 'totalBets')
          .addSelect('MAX(session.email)', 'email')
          .leftJoin(CasinoGames, 'game', 'game.id = transaction.gameId')
          .leftJoin(
            CasinoGameProvider,
            'gameProvider',
            'gameProvider.id = game.gameProviderId',
          )
          .leftJoin('transaction.session', 'session')
          .where('transaction.partnerId = :partnerId', { partnerId })
          .andWhere('transaction.createdAt BETWEEN :from AND :to', {
            from: todayRange.from,
            to: todayRange.to,
          })
          .andWhere('transaction.status = :status', { status: 'success' })
          .andWhere('transaction.deletedAt IS NULL')
          .groupBy('transaction.playerId')
          .addGroupBy('transaction.partnerId')
          .addGroupBy('transaction.gameId')
          .addGroupBy('transaction.currency')
          .getRawMany();

        todayData = report.map(game => ({
          playerId: game.playerId,
          gameId: game.gameId,
          gameName: game.gameName,
          gameProvider: game.gameProvider,
          partnerId: game.partnerId,
          email: game.email,
          totalBet: Number(game.totalBetAmount),
          totalSettle: Number(game.totalWinAmount),
          currency: game.currency || 'BRL',
          ggr: Number(game.totalBetAmount) - Number(game.totalWinAmount),
          totalRound: Number(game.totalBets),
          date: today,
        }));
      }

      return [...aggregatedData, ...todayData];
    } catch (e) {
      this.logger.error(`[Error] erro ao consultar report game`);
      throw new HttpException(
        `Erro ao buscar report game: ${e.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getDailyReportGamePlayerPartnerPagination(
    filter: DailyReportGamePlayerPartnerFilterDto,
    req: Request,
  ) {
    try {
      const partnerId = req.headers['partner-id'] as string;
      const { gameId, playerId, gameProviderId } = filter;

      if (!filter.fromDate || !filter.toDate) {
        throw new BadRequestException(
          'É necessário informar a data inicial e final',
        );
      }

      const parsedEndDate = parseISO(filter.toDate);
      if (isToday(parsedEndDate)) {
        const today = new Date();
        const todayRange = this.dateService.getDateRange(
          today.toISOString(),
          today.toISOString(),
        );

        await this.saveDailyReportGamePlayerPartner(
          todayRange.fromDate,
          todayRange.toDate,
        );

        this.logger.log(
          'Dados do dia atual atualizados na tabela daily_report_game_player_partner',
        );
      }

      const { fromDate, toDate } = this.dateService.getDateRange(
        filter.fromDate,
        filter.toDate,
      );

      const page = !filter.page || filter.page < 1 ? 1 : filter.page;
      const pageSize =
        !filter.pageSize || filter.pageSize < 1 ? 10 : filter.pageSize;
      const offset = (page - 1) * pageSize;

      const allowedOrderFields = [
        'playerId',
        'gameId',
        'gameName',
        'gameProvider',
        'currency',
        'totalBet',
        'totalSettle',
        'ggr',
      ];
      const orderBy = allowedOrderFields.includes(filter.orderBy)
        ? filter.orderBy
        : 'playerId';

      const sortOrder = filter.sortOrder === SortOrder.DESC ? 'DESC' : 'ASC';

      const qb = this.manager
        .createQueryBuilder()
        .select([
          'partner_id AS "partnerId"',
          'player_id AS "playerId"',
          'game_id AS "gameId"',
          'game_provider_id AS "gameProviderId"',
          'MAX(email) AS "email"',
          'MAX(game_name) AS "gameName"',
          'MAX(game_provider) AS "gameProvider"',
          'MAX(currency) AS "currency"',
          'SUM(total_bet)::numeric AS "totalBet"',
          'SUM(total_settle)::numeric AS "totalSettle"',
          'SUM(ggr)::numeric AS "ggr"',
        ])
        .from('casino.daily_report_game_player_partner', 'dr')
        .where('partner_id = :partnerId', { partnerId })
        .andWhere('date BETWEEN :fromDate AND :toDate', { fromDate, toDate })
        .groupBy('partner_id, player_id, game_id, game_provider_id')
        .orderBy(`"${orderBy}"`, sortOrder as 'ASC' | 'DESC')
        .limit(pageSize)
        .offset(offset);

      if (gameId) {
        qb.andWhere('game_id = :gameId', { gameId });
      }

      if (playerId) {
        qb.andWhere('player_id = :playerId', { playerId });
      }

      if (gameProviderId) {
        qb.andWhere('game_provider_id = :gameProviderId', { gameProviderId });
      }

      const [data, total] = await Promise.all([
        qb.getRawMany(),
        this.manager
          .createQueryBuilder()
          .select('COUNT(*)', 'count')
          .from(qb2 => {
            return qb2
              .select('player_id')
              .addSelect('game_id')
              .from('casino.daily_report_game_player_partner', 'dr')
              .where('partner_id = :partnerId', { partnerId })
              .andWhere('date BETWEEN :fromDate AND :toDate', {
                fromDate,
                toDate,
              })
              .groupBy('partner_id, player_id, game_id, game_provider_id')
              .andWhere(gameId ? 'game_id = :gameId' : '1=1', { gameId })
              .andWhere(playerId ? 'player_id = :playerId' : '1=1', {
                playerId,
              })
              .andWhere(
                gameProviderId ? 'game_provider_id = :gameProviderId' : '1=1',
                { gameProviderId },
              );
          }, 'sub')
          .getRawOne<{ count: string }>(),
      ]);

      const formatted = data.map(item => ({
        partnerId: item.partnerId,
        playerId: item.playerId,
        gameId: item.gameId,
        gameProviderId: item.gameProviderId,
        email: item.email,
        gameName: item.gameName,
        gameProvider: item.gameProvider,
        currency: item.currency,
        totalBet: Number(item.totalBet),
        totalSettle: Number(item.totalSettle),
        ggr: Number(item.ggr),
      }));

      return {
        data: formatted,
        totalItems: Number(total.count),
        totalPages: Math.ceil(Number(total.count) / pageSize),
        currentPage: page,
        pageSize: pageSize,
      };
    } catch (e) {
      this.logger.error(`[Error] erro ao consultar report game por player`);
      throw new HttpException(
        `Erro ao buscar report game por player: ${e.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async saveDailyReportPlayer(fromDate: Date, toDate: Date) {
    try {
      console.log(`Processando daily report player para o dia: ${fromDate}`);

      const report = await this.manager
        .createQueryBuilder(CasinoTransactionEntity, 'transaction')
        .select('transaction.playerId', 'playerId')
        .addSelect('transaction.partnerId', 'partnerId')
        .addSelect('transaction.currency', 'currency')
        .addSelect('SUM(transaction.amount)', 'totalBetAmount')
        .addSelect('SUM(transaction.winAmount)', 'totalWinAmount')
        .addSelect('MAX(session.email)', 'email')
        .leftJoin('transaction.session', 'session')
        .where('transaction.createdAt BETWEEN :fromDate AND :toDate', {
          fromDate,
          toDate,
        })
        .andWhere('transaction.status = :status', { status: 'success' })
        .andWhere('transaction.deletedAt IS NULL')
        .groupBy('transaction.playerId')
        .addGroupBy('transaction.partnerId')
        .addGroupBy('transaction.currency')
        .getRawMany();

      if (report.length === 0) {
        this.logger.log('Nenhuma transação encontrada para processar');
        return;
      }

      // Preparar dados para bulk upsert
      const dataToUpsert = report.map(player => ({
        playerId: player.playerId,
        partnerId: player.partnerId,
        email: player.email || null,
        totalBet: Number(player.totalBetAmount || 0),
        totalSettle: Number(player.totalWinAmount || 0),
        currency: player.currency || 'BRL',
        ggr:
          Number(player.totalBetAmount || 0) -
          Number(player.totalWinAmount || 0),
        date: fromDate,
      }));

      // Usar bulk upsert do PostgreSQL (muito mais performático)
      await this.manager
        .createQueryBuilder()
        .insert()
        .into(DailyReportPlayer)
        .values(dataToUpsert)
        .orUpdate(
          ['total_bet', 'total_settle', 'ggr', 'email', 'currency'],
          ['player_id', 'partner_id', 'date'],
        )
        .execute();

      this.logger.log(`Daily report processado: ${report.length} players`);
    } catch (error) {
      this.logger.error(`Erro ao salvar daily report player: ${error.message}`);
      throw error;
    }
  }

  async getDailyReportPlayer(
    startDate: string,
    endDate: string,
    filter: GenericFilter,
    req: Request,
    playerId: string | undefined,
    email: string | undefined,
  ) {
    try {
      const partnerId = req.headers['partner-id'] as string;

      if (!startDate || !endDate) {
        throw new BadRequestException(
          'É necessário informar a data inicial e final',
        );
      }

      const parsedEndDate = parseISO(endDate);
      if (isToday(parsedEndDate)) {
        const today = new Date();
        const todayRange = this.dateService.getDateRange(
          today.toISOString(),
          today.toISOString(),
        );

        await this.saveDailyReportPlayer(
          todayRange.fromDate,
          todayRange.toDate,
        );

        this.logger.log(
          'Dados do dia atual atualizados na tabela daily_report_player',
        );
      }

      const { fromDate, toDate } = this.dateService.getDateRange(
        startDate,
        endDate,
      );

      const page = !filter.page || filter.page < 1 ? 1 : +filter.page;
      const pageSize =
        !filter.pageSize || filter.pageSize < 1 ? 10 : +filter.pageSize;
      const offset = (page - 1) * pageSize;

      const allowedOrderFields = [
        'playerId',
        'currency',
        'totalBet',
        'totalSettle',
        'ggr',
      ];
      const orderBy = allowedOrderFields.includes(filter.orderBy)
        ? filter.orderBy
        : 'playerId';

      const sortOrder = filter.sortOrder === 'DESC' ? 'DESC' : 'ASC';

      const qb = this.manager
        .createQueryBuilder()
        .select([
          'partner_id AS "partnerId"',
          'player_id AS "playerId"',
          'email AS "email"',
          'MAX(currency) AS "currency"',
          'SUM(total_bet)::numeric AS "totalBet"',
          'SUM(total_settle)::numeric AS "totalSettle"',
          'SUM(ggr)::numeric AS "ggr"',
        ])
        .from('casino.daily_report_player', 'drp')
        .where('partner_id = :partnerId', { partnerId })
        .andWhere('date BETWEEN :fromDate AND :toDate', { fromDate, toDate })
        .groupBy('partner_id, player_id, email, currency')
        .orderBy(`"${orderBy}"`, sortOrder as 'ASC' | 'DESC')
        .limit(pageSize)
        .offset(offset);

      if (playerId) {
        qb.andWhere('player_id = :playerId', { playerId });
      }

      if (email) {
        qb.andWhere('email = :email', { email });
      }

      const [data, total] = await Promise.all([
        qb.getRawMany(),
        this.manager
          .createQueryBuilder()
          .select('COUNT(*)', 'count')
          .from(qb2 => {
            return qb2
              .select('player_id')
              .from('casino.daily_report_player', 'drp')
              .where('partner_id = :partnerId', { partnerId })
              .andWhere('date BETWEEN :fromDate AND :toDate', {
                fromDate,
                toDate,
              })
              .groupBy('partner_id, player_id, email, currency')
              .andWhere(playerId ? 'player_id = :playerId' : '1=1', {
                playerId,
              });
          }, 'sub')
          .getRawOne<{ count: string }>(),
      ]);

      const formatted = data.map(item => ({
        partnerId: item.partnerId,
        playerId: item.playerId,
        email: item.email,
        currency: item.currency,
        totalBet: Number(item.totalBet),
        totalSettle: Number(item.totalSettle),
        ggr: Number(item.ggr),
      }));

      return {
        data: formatted,
        totalItems: Number(total.count),
        totalPages: Math.ceil(Number(total.count) / pageSize),
        currentPage: page,
        pageSize: pageSize,
      };
    } catch (e) {
      this.logger.error(`[Error] erro ao consultar report game`);
      throw new HttpException(
        `Erro ao buscar report game: ${e.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getDailyReportPlayerById(playerId: string, req: Request) {
    const partnerId = req.headers['partner-id'] as string;
    const whereCondition = {
      partnerId: partnerId,
      playerId: playerId,
    };

    const result = await this.manager.find(DailyReportPlayer, {
      where: whereCondition,
    });

    return result;
  }

  async saveDailyReportProviders(fromDate: Date, toDate: Date) {
    try {
      this.logger.log(
        `Processando daily report provider para o dia: ${fromDate}`,
      );

      const report = await this.manager
        .createQueryBuilder(CasinoTransactionEntity, 'transaction')
        .select('gameProvider.name', 'gameProvider')
        .addSelect('transaction.partnerId', 'partnerId')
        .addSelect('transaction.currency', 'currency')
        .addSelect('SUM(transaction.amount)', 'totalBetAmount')
        .addSelect('SUM(transaction.winAmount)', 'totalWinAmount')
        .addSelect('COUNT(transaction.roundEnded)', 'totalRound')
        .addSelect('game.gameProviderId', 'gameProviderId')
        .leftJoin(CasinoGames, 'game', 'game.id = transaction.gameId')
        .leftJoin(
          CasinoGameProvider,
          'gameProvider',
          'gameProvider.id = game.gameProviderId',
        )
        .where('transaction.createdAt BETWEEN :fromDate AND :toDate', {
          fromDate,
          toDate,
        })
        .andWhere('transaction.status = :status', { status: 'success' })
        .andWhere('transaction.deletedAt IS NULL')
        .groupBy('gameProvider.name')
        .addGroupBy('transaction.partnerId')
        .addGroupBy('transaction.currency')
        .addGroupBy('game.gameProviderId')
        .getRawMany();

      if (report.length === 0) {
        this.logger.log('Nenhuma transação encontrada para processar');
        return;
      }

      // Preparar dados para bulk upsert
      const dataToUpsert = report.map(provider => ({
        gameProvider: provider.gameProvider,
        gameProviderId: provider.gameProviderId,
        partnerId: provider.partnerId,
        totalBet: Number(provider.totalBetAmount || 0),
        totalSettle: Number(provider.totalWinAmount || 0),
        totalRound: Number(provider.totalRound || 0),
        currency: provider.currency || 'BRL',
        ggr:
          Number(provider.totalBetAmount || 0) -
          Number(provider.totalWinAmount || 0),
        date: fromDate,
      }));

      // Usar bulk upsert do PostgreSQL (muito mais performático)
      await this.manager
        .createQueryBuilder()
        .insert()
        .into(DailyReportProviders)
        .values(dataToUpsert)
        .orUpdate(
          ['total_bet', 'total_settle', 'total_round', 'ggr', 'currency'],
          ['game_provider', 'partner_id', 'date', 'game_provider_id'],
        )
        .execute();

      this.logger.log(`Daily report processado: ${report.length} providers`);
    } catch (error) {
      this.logger.error(
        `Erro ao salvar daily report providers: ${error.message}`,
      );
      throw error;
    }
  }

  async getDailyReportProviders(
    startDate: string,
    endDate: string,
    req: Request,
  ) {
    const partnerId = req.headers['partner-id'] as string;
    const { fromDate, toDate } = this.dateService.getDateRange(
      startDate,
      endDate,
    );

    const whereCondition = {
      partnerId: partnerId,
      date: fromDate ? Between(fromDate, toDate) : Not(IsNull()),
    };

    const result = await this.manager.find(DailyReportProviders, {
      where: whereCondition,
    });

    return result;
  }

  async getDailyReportProvidersPagination(
    startDate: string,
    endDate: string,
    filter: GenericFilter,
    req: Request,
    gameProviderId?: string,
  ) {
    try {
      const partnerId = req.headers['partner-id'] as string;

      if (!startDate || !endDate) {
        throw new BadRequestException(
          'É necessário informar a data inicial e final',
        );
      }

      const parsedEndDate = parseISO(endDate);
      if (isToday(parsedEndDate)) {
        const today = new Date();
        const todayRange = this.dateService.getDateRange(
          today.toISOString(),
          today.toISOString(),
        );

        await this.saveDailyReportProviders(
          todayRange.fromDate,
          todayRange.toDate,
        );

        this.logger.log(
          'Dados do dia atual atualizados na tabela daily_report_providers',
        );
      }

      const { fromDate, toDate } = this.dateService.getDateRange(
        startDate,
        endDate,
      );

      const page = !filter.page || filter.page < 1 ? 1 : filter.page;
      const pageSize =
        !filter.pageSize || filter.pageSize < 1 ? 10 : filter.pageSize;
      const offset = (page - 1) * pageSize;

      const allowedOrderFields = [
        'gameProvider',
        'currency',
        'totalBet',
        'totalSettle',
        'ggr',
        'totalRound',
      ];
      const orderBy = allowedOrderFields.includes(filter.orderBy)
        ? filter.orderBy
        : 'gameProvider';

      const sortOrder = filter.sortOrder === SortOrder.DESC ? 'DESC' : 'ASC';

      const qb = this.manager
        .createQueryBuilder()
        .select([
          'partner_id AS "partnerId"',
          'game_provider_id AS "gameProviderId"',
          'MAX(game_provider) AS "gameProvider"',
          'MAX(currency) AS "currency"',
          'SUM(total_bet)::numeric AS "totalBet"',
          'SUM(total_settle)::numeric AS "totalSettle"',
          'SUM(ggr)::numeric AS "ggr"',
          'SUM(total_round)::numeric AS "totalRound"',
        ])
        .from('casino.daily_report_providers', 'drp')
        .where('partner_id = :partnerId', { partnerId })
        .andWhere('date BETWEEN :fromDate AND :toDate', { fromDate, toDate })
        .groupBy('partner_id, game_provider_id, game_provider, currency')
        .orderBy(`"${orderBy}"`, sortOrder as 'ASC' | 'DESC')
        .limit(pageSize)
        .offset(offset);

      if (gameProviderId) {
        qb.andWhere('game_provider_id = :gameProviderId', { gameProviderId });
      }

      const [data, total] = await Promise.all([
        qb.getRawMany(),
        this.manager
          .createQueryBuilder()
          .select('COUNT(*)', 'count')
          .from(qb2 => {
            return qb2
              .select('game_provider_id')
              .from('casino.daily_report_providers', 'drp')
              .where('partner_id = :partnerId', { partnerId })
              .andWhere('date BETWEEN :fromDate AND :toDate', {
                fromDate,
                toDate,
              })
              .groupBy('partner_id, game_provider_id, game_provider, currency')
              .andWhere(
                gameProviderId ? 'game_provider_id = :gameProviderId' : '1=1',
                {
                  gameProviderId,
                },
              );
          }, 'sub')
          .getRawOne<{ count: string }>(),
      ]);

      const formatted = data.map(item => ({
        partnerId: item.partnerId,
        gameProviderId: item.gameProviderId,
        gameProvider: item.gameProvider,
        currency: item.currency,
        totalBet: Number(item.totalBet),
        totalSettle: Number(item.totalSettle),
        ggr: Number(item.ggr),
        totalRound: Number(item.totalRound),
      }));

      return {
        data: formatted,
        totalItems: Number(total.count),
        totalPages: Math.ceil(Number(total.count) / pageSize),
        currentPage: page,
        pageSize: pageSize,
      };
    } catch (e) {
      this.logger.error(`[Error] erro ao consultar report providers`);
      throw new HttpException(
        `Erro ao buscar report providers: ${e.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async saveDailyReportPartner(fromDate: Date, toDate: Date) {
    try {
      this.logger.log(
        `Processando daily report partner para o dia: ${fromDate}`,
      );
      const report = await this.manager
        .createQueryBuilder(CasinoTransactionEntity, 'transaction')
        .select('transaction.partnerId', 'partnerId')
        .addSelect('SUM(transaction.amount)', 'totalBetAmount')
        .addSelect('SUM(transaction.winAmount)', 'totalWinAmount')
        .addSelect('COUNT(transaction.roundEnded)', 'totalBets')
        .addSelect('partner.descriptionPartner', 'partnerName')
        .leftJoin(
          PartnersEntity,
          'partner',
          'partner.id = transaction.partnerId',
        )
        .groupBy('transaction.partnerId')
        .addGroupBy('partner.descriptionPartner')
        .where('transaction.createdAt BETWEEN :fromDate AND :toDate', {
          fromDate,
          toDate,
        })
        .getRawMany();

      for (const partner of report) {
        const existing = await this.manager.findOneBy(DailyReportPartner, {
          partnerId: partner.partnerId,
          date: fromDate,
        });
        const newData = {
          partnerId: partner.partnerId,
          totalBet: Number(partner.totalBetAmount),
          totalSettle: Number(partner.totalWinAmount),
          partnerName: partner.partnerName,
          totalRound: Number(partner.totalBets),
          currency: 'BRL',
          ggr: Number(partner.totalBetAmount) - Number(partner.totalWinAmount),
          date: fromDate,
        };
        if (existing) {
          await this.manager.update(
            DailyReportPartner,
            { id: existing.id },
            newData,
          );
        } else {
          await this.manager.insert(DailyReportPartner, newData);
        }
      }
      this.logger.log(`Daily report processado: ${report.length} partners`);
    } catch (error) {
      this.logger.error(error);
    }
  }

  async getDailyReportPartner(
    startDate: string,
    endDate: string,
    req: Request,
  ) {
    const partnerId = req.headers['partner-id'] as string;
    const { fromDate, toDate } = this.dateService.getDateRange(
      startDate,
      endDate,
    );
    const whereCondition = {
      partnerId: partnerId,
      date: fromDate ? Between(fromDate, toDate) : Not(IsNull()),
    };

    const result = await this.manager.find(DailyReportPartner, {
      where: whereCondition,
    });

    return result;
  }

  async getDailyReportPartnerPagination(
    startDate: string,
    endDate: string,
    filter: GenericFilter,
    req: Request,
  ) {
    const partnerId = req.headers['partner-id'] as string;

    if (!startDate || !endDate)
      throw new BadRequestException(
        'É necessário informar a data inicial e final',
      );

    const { fromDate, toDate } = this.dateService.getDateRange(
      startDate,
      endDate,
    );

    if (!filter.sortOrder) {
      filter.sortOrder = SortOrder.ASC;
    }
    const page = !filter.page || filter.page < 1 ? 1 : filter.page;
    const pageSize =
      !filter.pageSize || filter.pageSize < 1 ? 10 : filter.pageSize;
    const offset = (page - 1) * pageSize;

    if (['partnerId', 'currency'].includes(filter.orderBy)) {
      filter.orderBy = filter.orderBy;
    } else {
      filter.orderBy = 'partnerId';
    }

    //Query para obter os dados
    const query = `
    SELECT 
      partner_id as "partnerId",      
      MAX(currency) as "currency",
      SUM(total_bet) as "totalBet",
      SUM(total_settle) as "totalSettle",
      SUM(ggr) as "ggr",
      SUM(total_round) as "totalRound"
    FROM 
      casino.daily_report_partner
      WHERE 
        partner_id = $1
        AND date BETWEEN $2 AND $3
      GROUP BY 
        partner_id, currency
      ORDER BY 
        "${filter.orderBy}" ${filter.sortOrder}
      LIMIT $4
      OFFSET $5
    `;

    // Query para obter o total de registros para paginação
    const countQuery = `
      SELECT 
        COUNT(*) as "totalItems"
      FROM (
        SELECT 
          partner_id
        FROM 
          casino.daily_report_partner
        WHERE 
          partner_id = $1
          AND date BETWEEN $2 AND $3 
        GROUP BY 
         partner_id, currency
      ) as count_table
    `;

    try {
      const queryParams = [partnerId, fromDate, toDate, pageSize, offset];

      const countParams = [partnerId, fromDate, toDate];

      const result = await this.manager.query<DailyReportPartner[]>(
        query,
        queryParams,
      );
      const countResult = await this.manager.query(countQuery, countParams);

      const totalItems = parseInt(countResult[0].totalItems);
      const totalPages = Math.ceil(totalItems / pageSize);

      // Parse do resultado
      const formattedResult = result.map(item => ({
        partnerId: item.partnerId,
        currency: item.currency,
        totalBet: Number(item.totalBet || 0),
        totalSettle: Number(item.totalSettle || 0),
        ggr: Number(item.ggr || 0),
        totalRound: Number(item.totalRound || 0),
      }));

      return {
        data: formattedResult,
        totalItems: totalItems,
        totalPages: totalPages,
        currentPage: page,
        pageSize: pageSize,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async findGameProfitability(
    filter: GenericFilter & GameProfitabilityFilterDto,
    partnerId: string,
  ): Promise<{
    data: GameProfitabilityResponseDto[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    try {
      if (!filter.dateRange?.from || !filter.dateRange?.to) {
        throw new HttpException(
          'É necessário informar o período de datas',
          HttpStatus.BAD_REQUEST,
        );
      }

      const isStartToday =
        filter.dateRange.from && isToday(parseISO(filter.dateRange.from));
      const isEndToday =
        filter.dateRange.to && isToday(parseISO(filter.dateRange.to));

      if (isStartToday || isEndToday) {
        const todayData = await this.getDailyReportGame(
          filter.dateRange.from,
          filter.dateRange.to,
          { headers: { 'partner-id': partnerId } } as unknown as Request,
          filter.gameProvider,
        );

        const groupedData = todayData.reduce(
          (acc: Record<string, GameProfitabilityResponseDto>, curr: any) => {
            const gameId = curr.gameId || 'unknown';
            if (!acc[gameId]) {
              acc[gameId] = {
                gameId: gameId,
                gameName: curr.gameName || 'Unknown Game',
                gameProvider: curr.gameProvider || 'Unknown Provider',
                totalBet: 0,
                totalSettle: 0,
                ggr: 0,
                profitability: 0,
              };
            }
            acc[gameId].totalBet += Number(
              Number(curr.totalBet || 0).toFixed(2),
            );
            acc[gameId].totalSettle += Number(
              Number(curr.totalSettle || 0).toFixed(2),
            );
            acc[gameId].ggr += Number(Number(curr.ggr || 0).toFixed(2));
            acc[gameId].profitability =
              acc[gameId].totalBet > 0
                ? Number(
                    (
                      (Number(acc[gameId].ggr || 0) /
                        Number(acc[gameId].totalBet || 1)) *
                      100
                    ).toFixed(2),
                  )
                : 0;
            return acc;
          },
          {},
        );

        const page = !filter.page || filter.page < 1 ? 1 : filter.page;
        const pageSize =
          !filter.pageSize || filter.pageSize < 1 ? 10 : filter.pageSize;
        const offset = (page - 1) * pageSize;

        const sortedData = Object.values(groupedData).sort(
          (
            a: GameProfitabilityResponseDto,
            b: GameProfitabilityResponseDto,
          ) => {
            const order = filter.sortOrder === SortOrder.ASC ? 1 : -1;
            const orderBy = filter.orderBy || 'profitability';
            const aValue = a[orderBy as keyof GameProfitabilityResponseDto];
            const bValue = b[orderBy as keyof GameProfitabilityResponseDto];

            if (typeof aValue === 'number' && typeof bValue === 'number') {
              return (aValue - bValue) * order;
            }

            const aStr = String(aValue || '');
            const bStr = String(bValue || '');
            return (
              aStr.localeCompare(bStr, undefined, {
                numeric: true,
                sensitivity: 'base',
              }) * order
            );
          },
        );

        const paginatedData = sortedData.slice(offset, offset + pageSize);

        return {
          data: paginatedData as GameProfitabilityResponseDto[],
          totalItems: Object.keys(groupedData).length,
          totalPages: Math.ceil(Object.keys(groupedData).length / pageSize),
          currentPage: page,
          pageSize: pageSize,
        };
      }

      const dateRange = this.dateService.getDateRange(
        filter.dateRange.from,
        filter.dateRange.to,
      );

      if (!filter.sortOrder) {
        filter.sortOrder = SortOrder.ASC;
      }
      const page = !filter.page || filter.page < 1 ? 1 : filter.page;
      const pageSize =
        !filter.pageSize || filter.pageSize < 1 ? 10 : filter.pageSize;
      const offset = (page - 1) * pageSize;

      const query = `
        SELECT 
          game_id as "gameId",
          MAX(game_name) as "gameName",
          MAX(game_provider) as "gameProvider",
          SUM(total_bet) as "totalBet",
          SUM(total_settle) as "totalSettle",
          SUM(ggr) as "ggr",
          CASE 
            WHEN SUM(total_bet) > 0 
            THEN ROUND((SUM(ggr) / SUM(total_bet) * 100)::numeric, 2)
            ELSE 0 
          END as "profitability"
        FROM 
          casino.daily_report_game
        WHERE 
          partner_id = $1
          AND date BETWEEN $2 AND $3
          ${filter.gameProvider ? 'AND game_provider = $6' : ''}
        GROUP BY 
          game_id
        ORDER BY 
          "${filter.orderBy || 'profitability'}" ${filter.sortOrder}
        LIMIT $4
        OFFSET $5
      `;

      const countQuery = `
        SELECT COUNT(*) as "totalItems"
        FROM (
          SELECT game_id
          FROM casino.daily_report_game
          WHERE 
            partner_id = $1
            AND date BETWEEN $2 AND $3
            ${filter.gameProvider ? 'AND game_provider = $4' : ''}
          GROUP BY game_id
        ) as count_table
      `;

      const queryParams = [
        partnerId,
        dateRange.fromDate,
        dateRange.toDate,
        pageSize,
        offset,
        ...(filter.gameProvider ? [filter.gameProvider] : []),
      ];

      const countParams = [
        partnerId,
        dateRange.fromDate,
        dateRange.toDate,
        ...(filter.gameProvider ? [filter.gameProvider] : []),
      ];

      const result = await this.manager.query(query, queryParams);
      const countResult = await this.manager.query(countQuery, countParams);

      const totalItems = parseInt(countResult[0].totalItems);
      const totalPages = Math.ceil(totalItems / pageSize);

      const formattedResult = result.map(item => ({
        gameId: item.gameId,
        gameName: item.gameName,
        gameProvider: item.gameProvider,
        totalBet: Number(item.totalBet || 0),
        totalSettle: Number(item.totalSettle || 0),
        ggr: Number(item.ggr || 0),
        profitability: Number(item.profitability || 0),
      }));

      return {
        data: formattedResult,
        totalItems,
        totalPages,
        currentPage: page,
        pageSize,
      };
    } catch (error) {
      this.logger.error(`Error in findGameProfitability: ${error}`);
      throw new HttpException(
        error.message || 'Erro interno do servidor',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getBetReportPagination(
    filter: BetReportFilterDto,
    req: Request,
  ): Promise<{
    data: any[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    try {
      const partnerId = req.headers['partner-id'] as string;
      const { fromDate, toDate, playerId, gameId, gameProviderId } = filter;

      if (!fromDate || !toDate) {
        throw new BadRequestException(
          'É necessário informar a data inicial e final',
        );
      }

      const { fromDate: parsedFromDate, toDate: parsedToDate } =
        this.dateService.getDateRange(fromDate, toDate);

      const page = !filter.page || filter.page < 1 ? 1 : filter.page;
      const pageSize =
        !filter.pageSize || filter.pageSize < 1 ? 10 : filter.pageSize;
      const offset = (page - 1) * pageSize;

      const allowedOrderFields = [
        'transactionId',
        'roundId',
        'playerId',
        'gameId',
        'gameProviderId',
        'totalBet',
        'totalWin',
        'createdAt',
      ];
      const orderBy = allowedOrderFields.includes(filter.orderBy)
        ? filter.orderBy
        : 'createdAt';

      const sortOrder = filter.sortOrder ? filter.sortOrder : SortOrder.DESC;

      const qb = this.manager
        .createQueryBuilder(CasinoTransactionEntity, 't')
        .select([
          't.id AS "transactionId"',
          't.round_id AS "roundId"',
          't.player_id AS "playerId"',
          't.partner_id AS "partnerId"',
          't.game_id AS "gameId"',
          'g.name AS "gameName"',
          'gp.name AS "gameProvider"',
          't.amount AS "totalBet"',
          't.win_amount AS "totalWin"',
          `CASE WHEN t.round_ended = true THEN 'Finalizada' ELSE 'Em andamento' END AS "betStatus"`,
          't.currency AS "currency"',
          't.created_at AS "createdAt"',
        ])
        .leftJoin(CasinoGames, 'g', 'g.id = t.game_id')
        .leftJoin(CasinoGameProvider, 'gp', 'gp.id = g.game_provider_id')
        .where('t.partner_id = :partnerId', { partnerId })
        .andWhere('t.created_at BETWEEN :fromDate AND :toDate', {
          fromDate: parsedFromDate,
          toDate: parsedToDate,
        })
        .andWhere('t.deleted_at IS NULL')
        .andWhere('t.status = :status', { status: 'success' });

      if (playerId) {
        qb.andWhere('t.player_id = :playerId', { playerId });
      }

      if (gameId) {
        qb.andWhere('t.game_id = :gameId', { gameId });
      }

      if (gameProviderId) {
        qb.andWhere('g.game_provider_id = :gameProviderId', {
          gameProviderId,
        });
      }

      const countQb = this.manager
        .createQueryBuilder(CasinoTransactionEntity, 't')
        .select('COUNT(*)', 'count')
        .leftJoin(CasinoGames, 'g', 'g.id = t.game_id')
        .where('t.partner_id = :partnerId', { partnerId })
        .andWhere('t.created_at BETWEEN :fromDate AND :toDate', {
          fromDate: parsedFromDate,
          toDate: parsedToDate,
        })
        .andWhere('t.deleted_at IS NULL')
        .andWhere('t.status = :status', { status: 'success' });

      if (playerId) {
        countQb.andWhere('t.player_id = :playerId', { playerId });
      }

      if (gameId) {
        countQb.andWhere('t.game_id = :gameId', { gameId });
      }

      if (gameProviderId) {
        countQb.andWhere('g.game_provider_id = :gameProviderId', {
          gameProviderId,
        });
      }

      qb.orderBy(`"${orderBy}"`, sortOrder as 'ASC' | 'DESC')
        .limit(pageSize)
        .offset(offset);

      const [data, countResult] = await Promise.all([
        qb.getRawMany(),
        countQb.getRawOne<{ count: string }>(),
      ]);

      const formatted = data.map(item => ({
        transactionId: item.transactionId,
        roundId: item.roundId,
        playerId: item.playerId,
        partnerId: item.partnerId,
        gameId: item.gameId,
        gameName: item.gameName,
        gameProvider: item.gameProvider,
        totalBet: Number(item.totalBet),
        totalWin: Number(item.totalWin),
        betStatus: item.betStatus,
        currency: item.currency,
        createdAt: item.createdAt,
      }));

      return {
        data: formatted,
        totalItems: Number(countResult.count),
        totalPages: Math.ceil(Number(countResult.count) / pageSize),
        currentPage: page,
        pageSize: pageSize,
      };
    } catch (e) {
      this.logger.error(`[Error] erro ao consultar relatório de apostas`);
      throw new HttpException(
        `Erro ao buscar relatório de apostas: ${e.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
