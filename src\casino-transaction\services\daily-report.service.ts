import { DateUtilsService } from '@/common/utils/date';
import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
} from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, In, <PERSON><PERSON><PERSON>, Not } from 'typeorm';
import { CasinoTransactionEntity } from '../controller/entities/casino-transactions.entity';
import { CasinoGames } from '@/casino-games/entities/casino-game.entity';
import { DailyReportGame } from '../controller/entities/daily-report-game.entity';
import { endOfDay, isToday, isWithinInterval, parseISO, startOfDay, subDays } from 'date-fns';
import { DailyReportGamePlayerPartner } from '../controller/entities/daily-report-game-player-partner.entity';
import { DailyReportPlayer } from '../controller/entities/daily-report-player.entity';
import { Between } from 'typeorm';
import { DailyReportProviders } from '../controller/entities/daily-report-providers.entity';
import { DailyReportPartner } from '../controller/entities/daily-report-partner.entity';
import { GenericFilter } from '@/common/filters/generic-filter.dto';
import { SortOrder } from '@/common/filters/sortOrder';
import { AccountInfoEntity } from '../controller/entities/player-account-info.entity';
import { ContactInfoEntity } from '../controller/entities/player-contact-info.entity';
import { RegionEntity } from '../controller/entities/region-utils.entity';
import { PartnersEntity } from '../controller/entities/partner.entity';
import { GameProfitabilityFilterDto } from '../controller/dto/game-profitability-filter.dto';
import { GameProfitabilityResponseDto } from '../controller/dto/game-profitability-response.dto';
import {
  DailyReportGameFilterDto,
  DailyReportGamePlayerPartnerFilterDto,
} from '../controller/dto/daily-report-filter.dto';

@Injectable()
export class DailyReportService {
  private readonly logger = new Logger(DailyReportService.name);
  constructor(
    @InjectEntityManager() private readonly manager: EntityManager,
    private readonly dateService: DateUtilsService
  ) {}

  async saveDailyReportGame() {
    try {
      const yesterday = subDays(new Date(), 1);
      const { fromDate, toDate } = this.dateService.getDateRange(
        yesterday.toISOString(),
        yesterday.toISOString()
      );
      const report = await this.manager
        .createQueryBuilder(CasinoTransactionEntity, 'transaction')
        .select('transaction.gameId', 'gameId')
        .addSelect('game.name', 'gameName')
        .addSelect('game.gameProvider', 'gameProvider')
        .addSelect('transaction.partnerId', 'partnerId')
        .addSelect('SUM(transaction.amount)', 'totalBetAmount')
        .addSelect('SUM(transaction.winAmount)', 'totalWinAmount')
        .addSelect('COUNT(transaction.roundEnded)', 'totalBets')
        .addSelect('COUNT(DISTINCT transaction.playerId)', 'totalPlayer')
        .leftJoin(CasinoGames, 'game', 'game.gameId = transaction.gameId')
        .groupBy('transaction.gameId')
        .addGroupBy('game.name')
        .addGroupBy('game.gameProvider')
        .addGroupBy('transaction.partnerId')
        .where('transaction.createdAt BETWEEN :fromDate AND :toDate', {
          fromDate,
          toDate,
        })
        .getRawMany();

      for (const game of report) {
        const existing = await this.manager.findOne(DailyReportGame, {
          where: {
            gameId: game.gameId,
            partnerId: game.partnerId,
            date: fromDate,
          },
        });

        const newData = {
          gameId: game.gameId,
          gameName: game.gameName,
          gameProvider: game.gameProvider,
          partnerId: game.partnerId,
          totalBet: Number(game.totalBetAmount),
          totalSettle: Number(game.totalWinAmount),
          currency: 'BRL',
          ggr: Number(game.totalBetAmount) - Number(game.totalWinAmount),
          totalRound: Number(game.totalBets),
          totalPlayers: Number(game.totalPlayer),
          date: fromDate,
        };
        console.log(existing, newData);
        if (existing) {
          await this.manager.update(
            DailyReportGame,
            { id: existing.id },
            newData
          );
        } else {
          await this.manager.insert(DailyReportGame, newData);
        }
      }
    } catch (error) {
      this.logger.error(error);
    }
  }

  async getDailyReportGame(
    startDate: string,
    endDate: string,
    req: Request,
    gameProvider?: string
  ) {
    try {
      const partnerId = req.headers['partner-id'] as string;
      const today = startOfDay(new Date());

      const parsedStartDate = startDate ? parseISO(startDate) : null;
      const parsedEndDate = endDate ? parseISO(endDate) : null;

      const fromDate = parsedStartDate ? startOfDay(parsedStartDate) : null;
      const toDate = parsedEndDate ? endOfDay(parsedEndDate) : null;

      const containsToday =
        fromDate &&
        toDate &&
        isWithinInterval(today, { start: fromDate, end: toDate });

      const previousRange = containsToday
        ? {
            from: fromDate,
            to: endOfDay(subDays(today, 1)),
          }
        : { from: fromDate, to: toDate };

      const todayRange = containsToday
        ? {
            from: startOfDay(today),
            to: endOfDay(today),
          }
        : null;

      const aggregatedConditions: any = {
        partnerId,
        date: previousRange.from
          ? Between(previousRange.from, previousRange.to)
          : Not(IsNull()),
        ...(gameProvider && { gameProvider }),
      };

      let aggregatedData = [];

      if (!containsToday || previousRange.from <= previousRange.to) {
        aggregatedData = await this.manager.find(DailyReportGame, {
          where: aggregatedConditions,
        });
      }

      let todayData = [];

      if (containsToday) {
        const report = await this.manager
          .createQueryBuilder(CasinoTransactionEntity, 'transaction')
          .select('transaction.gameId', 'gameId')
          .addSelect('game.name', 'gameName')
          .addSelect('game.gameProvider', 'gameProvider')
          .addSelect('transaction.partnerId', 'partnerId')
          .addSelect('SUM(transaction.amount)', 'totalBetAmount')
          .addSelect('SUM(transaction.winAmount)', 'totalWinAmount')
          .addSelect('COUNT(transaction.roundEnded)', 'totalBets')
          .addSelect('COUNT(DISTINCT transaction.playerId)', 'totalPlayer')
          .leftJoin(CasinoGames, 'game', 'game.gameId = transaction.gameId')
          .where('transaction.partnerId = :partnerId', { partnerId })
          .andWhere(
            gameProvider ? 'game.gameProvider = :gameProvider' : '1=1',
            { gameProvider }
          )
          .andWhere('transaction.createdAt BETWEEN :from AND :to', {
            from: todayRange.from,
            to: todayRange.to,
          })
          .groupBy('transaction.gameId')
          .addGroupBy('game.name')
          .addGroupBy('game.gameProvider')
          .addGroupBy('transaction.partnerId')
          .getRawMany();

        todayData = report.map((game) => ({
          gameId: game.gameId,
          gameName: game.gameName,
          gameProvider: game.gameProvider,
          partnerId: game.partnerId,
          totalBet: Number(game.totalBetAmount),
          totalSettle: Number(game.totalWinAmount),
          currency: 'BRL',
          ggr: Number(game.totalBetAmount) - Number(game.totalWinAmount),
          totalRound: Number(game.totalBets),
          totalPlayers: Number(game.totalPlayer),
          date: today,
        }));
      }

      return [...aggregatedData, ...todayData];
      
    } catch (e) {
      this.logger.error(`[Error] erro ao consultar report game`);
      throw new HttpException(
        `Erro ao buscar report game: ${e.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async getDailyReportGamePagination(
    filter: DailyReportGameFilterDto,
    req: Request
  ) {
    try {
      const partnerId = req.headers['partner-id'] as string;
      const { fromDate, toDate, gameId, provider, accountType } = filter;

      if (!fromDate || !toDate) {
        throw new BadRequestException(
          'É necessário informar a data inicial e final'
        );
      }

      const date = this.dateService.getDateRange(fromDate, toDate);

      const page = !filter.page || filter.page < 1 ? 1 : filter.page;
      const pageSize =
        !filter.pageSize || filter.pageSize < 1 ? 10 : filter.pageSize;
      const offset = (page - 1) * pageSize;
      const orderBy = [
        'gameId',
        'gameName',
        'gameProvider',
        'currency',
      ].includes(filter.orderBy)
        ? filter.orderBy
        : 'gameId';
      const sortOrder = filter.sortOrder || SortOrder.ASC;

      const qb = this.manager
        .createQueryBuilder(DailyReportGame, 'drg')
        .select([
          'drg.partnerId AS "partnerId"',
          'drg.gameId AS "gameId"',
          'MAX(drg.gameName) AS "gameName"',
          'MAX(drg.gameProvider) AS "gameProvider"',
          'MAX(drg.currency) AS "currency"',
          'SUM(drg.totalBet) AS "totalBet"',
          'SUM(drg.totalSettle) AS "totalSettle"',
          'SUM(drg.ggr) AS "ggr"',
          'SUM(drg.totalRound) AS "totalRound"',
          'SUM(drg.totalPlayers) AS "totalPlayers"',
        ])
        .where('drg.partnerId = :partnerId', { partnerId })
        .andWhere('drg.date BETWEEN :fromDate AND :toDate', {
          fromDate: date.fromDate,
          toDate: date.toDate,
        })
        .groupBy(
          'drg.partnerId, drg.gameId, drg.gameName, drg.gameProvider, drg.currency'
        )
        .orderBy(`"${orderBy}"`, sortOrder)
        .limit(pageSize)
        .offset(offset);

      if (gameId) qb.andWhere('drg.gameId = :gameId', { gameId });
      if (provider) qb.andWhere('drg.gameProvider = :provider', { provider });
      if (accountType)
        qb.andWhere('drg.accountType = :accountType', { accountType });

      const rawResult = await qb.getRawMany();

      const countQb = this.manager
        .createQueryBuilder(DailyReportGame, 'drg')
        .select('COUNT(DISTINCT drg.gameId)', 'totalItems')
        .where('drg.partnerId = :partnerId', { partnerId })
        .andWhere('drg.date BETWEEN :fromDate AND :toDate', {
          fromDate: date.fromDate,
          toDate: date.toDate,
        });

      if (gameId) countQb.andWhere('drg.gameId = :gameId', { gameId });
      if (provider)
        countQb.andWhere('drg.gameProvider = :provider', { provider });
      if (accountType)
        countQb.andWhere('drg.accountType = :accountType', { accountType });

      const countResult = await countQb.getRawOne();
      const result = rawResult.map((item) => ({
        ...item,
        totalBet: Number(item.totalBet),
        totalSettle: Number(item.totalSettle),
        ggr: Number(item.ggr),
        totalRound: Number(item.totalRound),
        totalPlayers: Number(item.totalPlayers),
      }));

      return {
        data: result,
        totalItems: Number(countResult.totalItems),
        currentPage: page,
        pageSize,
        totalPages: Math.ceil(Number(countResult.totalItems) / pageSize),
      };
    } catch (e) {
      this.logger.error(`[Error] erro ao consultar report game`);
      throw new HttpException(
        `Erro ao buscar report game: ${e.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async saveDailyReportGamePlayerPartner() {
    try {
      const yesterday = subDays(new Date(), 1);
      const { fromDate, toDate } = this.dateService.getDateRange(
        yesterday.toISOString(),
        yesterday.toISOString()
      );
      const report = await this.manager
        .createQueryBuilder(CasinoTransactionEntity, 'transaction')
        .select('transaction.playerId', 'playerId')
        .addSelect('transaction.gameId', 'gameId')
        .addSelect('game.name', 'gameName')
        .addSelect('game.gameProvider', 'gameProvider')
        .addSelect('SUM(transaction.amount)', 'totalBetAmount')
        .addSelect('SUM(transaction.winAmount)', 'totalWinAmount')
        .addSelect('COUNT(transaction.roundEnded)', 'totalBets')
        .addSelect('transaction.partnerId', 'partnerId')
        .addSelect('contactInfo.email', 'email')
        .addSelect('accountInfo.btag', 'btag')
        .addSelect('region.country', 'region')
        .leftJoin(CasinoGames, 'game', 'game.gameId = transaction.gameId')
        .leftJoin(
          ContactInfoEntity,
          'contactInfo',
          'contactInfo.id_player = transaction.playerId'
        )
        .leftJoin(RegionEntity, 'region', 'region.id = contactInfo.region')
        .leftJoin(
          AccountInfoEntity,
          'accountInfo',
          'accountInfo.id_player = transaction.playerId'
        )
        .groupBy('transaction.playerId')
        .addGroupBy('transaction.partnerId')
        .addGroupBy('transaction.gameId')
        .addGroupBy('game.name')
        .addGroupBy('game.gameProvider')
        .addGroupBy('contactInfo.email')
        .addGroupBy('accountInfo.btag')
        .addGroupBy('region.country')
        .where('transaction.createdAt BETWEEN :fromDate AND :toDate', {
          fromDate,
          toDate,
        })
        .getRawMany();

      for (const game of report) {
        const existing = await this.manager.findOneBy(
          DailyReportGamePlayerPartner,
          {
            playerId: game.playerId,
            gameId: game.gameId,
            partnerId: game.partnerId,
            date: fromDate,
          }
        );
        const newData = {
          playerId: game.playerId,
          gameId: game.gameId,
          gameName: game.gameName,
          gameProvider: game.gameProvider,
          partnerId: game.partnerId,
          email: game.email,
          btag: game.btag,
          region: game.region,
          totalBet: Number(game.totalBetAmount),
          totalSettle: Number(game.totalWinAmount),
          currency: 'BRL',
          ggr: Number(game.totalBetAmount) - Number(game.totalWinAmount),
          totalRound: Number(game.totalBets),
          date: fromDate,
        };
        console.log(existing, newData);
        if (existing) {
          await this.manager.update(
            DailyReportGamePlayerPartner,
            { id: existing.id },
            newData
          );
        } else {
          await this.manager.insert(DailyReportGamePlayerPartner, newData);
        }
      }
    } catch (error) {
      this.logger.error(error);
    }
  }

  async getDailyReportGamePlayerPartner(
    startDate: string,
    endDate: string,
    req: Request
  ) {
    try{
      const partnerId = req.headers['partner-id'] as string;

      const today = startOfDay(new Date());

      const parsedStartDate = startDate ? parseISO(startDate) : null;
      const parsedEndDate = endDate ? parseISO(endDate) : null;

      const fromDate = parsedStartDate ? startOfDay(parsedStartDate) : null;
      const toDate = parsedEndDate ? endOfDay(parsedEndDate) : null;

      const containsToday =
        fromDate &&
        toDate &&
        isWithinInterval(today, { start: fromDate, end: toDate });

      const previousRange = containsToday
        ? {
            from: fromDate,
            to: endOfDay(subDays(today, 1)),
          }
        : { from: fromDate, to: toDate };

      const todayRange = containsToday
        ? {
            from: startOfDay(today),
            to: endOfDay(today),
          }
        : null;

      const aggregatedConditions: any = {
        partnerId,
        date: previousRange.from
          ? Between(previousRange.from, previousRange.to)
          : Not(IsNull()),
      };

      let aggregatedData = [];

      if (!containsToday || previousRange.from <= previousRange.to) {
        aggregatedData = await this.manager.find(DailyReportGamePlayerPartner, {
          where: aggregatedConditions,
        });
      }

      let todayData = [];

      if (containsToday) {
        const report = await this.manager
          .createQueryBuilder(CasinoTransactionEntity, 'transaction')
          .select('transaction.playerId', 'playerId')
          .addSelect('transaction.gameId', 'gameId')
          .addSelect('game.name', 'gameName')
          .addSelect('game.gameProvider', 'gameProvider')
          .addSelect('SUM(transaction.amount)', 'totalBetAmount')
          .addSelect('SUM(transaction.winAmount)', 'totalWinAmount')
          .addSelect('COUNT(transaction.roundEnded)', 'totalBets')
          .addSelect('transaction.partnerId', 'partnerId')
          .addSelect('contactInfo.email', 'email')
          .addSelect('accountInfo.btag', 'btag')
          .addSelect('region.country', 'region')
          .leftJoin(CasinoGames, 'game', 'game.gameId = transaction.gameId')
          .leftJoin(
            ContactInfoEntity,
            'contactInfo',
            'contactInfo.id_player = transaction.playerId'
          )
          .leftJoin(RegionEntity, 'region', 'region.id = contactInfo.region')
          .leftJoin(
            AccountInfoEntity,
            'accountInfo',
            'accountInfo.id_player = transaction.playerId'
          )
          .where('transaction.partnerId = :partnerId', { partnerId })
          .andWhere('transaction.createdAt BETWEEN :from AND :to', {
            from: todayRange.from,
            to: todayRange.to,
          })
          .groupBy('transaction.playerId')
          .addGroupBy('transaction.partnerId')
          .addGroupBy('transaction.gameId')
          .addGroupBy('game.name')
          .addGroupBy('game.gameProvider')
          .addGroupBy('contactInfo.email')
          .addGroupBy('accountInfo.btag')
          .addGroupBy('region.country')
          .getRawMany();

        todayData = report.map((game) => ({
          playerId: game.playerId,
          gameId: game.gameId,
          gameName: game.gameName,
          gameProvider: game.gameProvider,
          partnerId: game.partnerId,
          email: game.email,
          btag: game.btag,
          region: game.region,
          totalBet: Number(game.totalBetAmount),
          totalSettle: Number(game.totalWinAmount),
          currency: 'BRL',
          ggr: Number(game.totalBetAmount) - Number(game.totalWinAmount),
          totalRound: Number(game.totalBets),
          date: today,
        }));
      }

      return [...aggregatedData, ...todayData];
      
    } catch (e) {
      this.logger.error(`[Error] erro ao consultar report game`);
      throw new HttpException(
        `Erro ao buscar report game: ${e.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async getDailyReportGamePlayerPartnerPagination(
    filter: DailyReportGamePlayerPartnerFilterDto,
    req: Request
  ) {
    try {
      const partnerId = req.headers['partner-id'] as string;
      const { gameId, provider, playerId, sortOrder, orderBy } = filter;
      const { fromDate, toDate } = this.dateService.getDateRange(
        filter.fromDate,
        filter.toDate
      );

      if (!fromDate || !toDate) {
        throw new BadRequestException(
          'É necessário informar a data inicial e final'
        );
      }

      const page = !filter.page || filter.page < 1 ? 1 : filter.page;
      const pageSize =
        !filter.pageSize || filter.pageSize < 1 ? 10 : filter.pageSize;
      const offset = (page - 1) * pageSize;
      const sort = sortOrder || SortOrder.ASC;
      const orderColumn = [
        'playerId',
        'gameId',
        'gameName',
        'gameProvider',
        'currency',
      ].includes(orderBy)
        ? orderBy
        : 'playerId';

      const qb = this.manager
        .createQueryBuilder()
        .select('dr.partner_id', 'partnerId')
        .addSelect('dr.player_id', 'playerId')
        .addSelect('dr.email', 'email')
        .addSelect('dr.btag', 'btag')
        .addSelect('dr.region', 'region')
        .addSelect('dr.game_id', 'gameId')
        .addSelect('MAX(dr.game_name)', 'gameName')
        .addSelect('MAX(dr.game_provider)', 'gameProvider')
        .addSelect('MAX(dr.currency)', 'currency')
        .addSelect('SUM(dr.total_bet)::numeric', 'totalBet')
        .addSelect('SUM(dr.total_settle)::numeric', 'totalSettle')
        .addSelect('SUM(dr.ggr)::numeric', 'ggr')
        .addSelect('SUM(dr.total_round)::numeric', 'totalRound')
        .from('casino.daily_report_game_player_partner', 'dr')
        .where('dr.partner_id = :partnerId', { partnerId })
        .andWhere('dr.date BETWEEN :fromDate AND :toDate', {
          fromDate,
          toDate,
        });

      if (gameId) {
        qb.andWhere('dr.game_id = :gameId', { gameId });
      }

      if (provider) {
        qb.andWhere('dr.game_provider = :provider', { provider });
      }

      if (playerId) {
        qb.andWhere('dr.player_id = :playerId', { playerId });
      }

      qb.groupBy(
        'dr.partner_id, dr.player_id, dr.email, dr.btag, dr.region, dr.game_id, dr.game_provider, dr.currency'
      )
        .orderBy(`"${orderColumn}"`, sort)
        .limit(pageSize)
        .offset(offset);

      const data = await qb.getRawMany();

      const countQb = this.manager
        .createQueryBuilder()
        .select('COUNT(*)', 'totalItems')
        .from((subQb) => {
          return subQb
            .select('dr.player_id', 'player_id')
            .addSelect('dr.game_id', 'game_id')
            .from('casino.daily_report_game_player_partner', 'dr')
            .where('dr.partner_id = :partnerId', { partnerId })
            .andWhere('dr.date BETWEEN :fromDate AND :toDate', {
              fromDate,
              toDate,
            })
            .groupBy(
              'dr.partner_id, dr.player_id, dr.email, dr.btag, dr.region, dr.game_id, dr.game_provider, dr.currency'
            )
            .andWhere(gameId ? 'dr.game_id = :gameId' : '1=1', { gameId })
            .andWhere(provider ? 'dr.game_provider = :provider' : '1=1', {
              provider,
            })
            .andWhere(playerId ? 'dr.player_id = :playerId' : '1=1', {
              playerId,
            });
        }, 'count_table');

      const countResult = await countQb.getRawOne();
      const totalItems = parseInt(countResult.totalItems, 10);
      const totalPages = Math.ceil(totalItems / pageSize);

      const formattedData = data.map((item) => ({
        partnerId: item.partnerId,
        playerId: item.playerId,
        email: item.email,
        btag: item.btag,
        region: item.region,
        gameId: item.gameId,
        gameName: item.gameName,
        gameProvider: item.gameProvider,
        currency: item.currency,
        totalBet: Number(item.totalBet),
        totalSettle: Number(item.totalSettle),
        ggr: Number(item.ggr),
        totalRound: Number(item.totalRound),
      }));

      return {
        data: formattedData,
        totalItems,
        totalPages,
        currentPage: page,
        pageSize,
      };
    } catch (e) {
      this.logger.error(`[Error] erro ao consultar report game por player`);
      throw new HttpException(
        `Erro ao buscar report game por player: ${e.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async saveDailyReportPlayer() {
    try {
      const yesterday = subDays(new Date(), 1);
      const { fromDate, toDate } = this.dateService.getDateRange(
        yesterday.toISOString(),
        yesterday.toISOString()
      );
      const report = await this.manager
        .createQueryBuilder(CasinoTransactionEntity, 'transaction')
        .select('transaction.playerId', 'playerId')
        .addSelect('transaction.partnerId', 'partnerId')
        .addSelect('SUM(transaction.amount)', 'totalBetAmount')
        .addSelect('SUM(transaction.winAmount)', 'totalWinAmount')
        .addSelect('contactInfo.email', 'email')
        .leftJoin(
          ContactInfoEntity,
          'contactInfo',
          'contactInfo.id_player = transaction.playerId'
        )
        .groupBy('transaction.playerId')
        .addGroupBy('contactInfo.email')
        .addGroupBy('transaction.partnerId')
        .where('transaction.createdAt BETWEEN :fromDate AND :toDate', {
          fromDate,
          toDate,
        })
        .getRawMany();

      for (const player of report) {
        const existing = await this.manager.findOneBy(DailyReportPlayer, {
          playerId: player.playerId,
          partnerId: player.partnerId,
          date: fromDate,
        });
        const newData = {
          playerId: player.playerId,
          partnerId: player.partnerId,
          email: player.email,
          totalBet: Number(player.totalBetAmount),
          totalSettle: Number(player.totalWinAmount),
          currency: 'BRL',
          ggr: Number(player.totalBetAmount) - Number(player.totalWinAmount),
          date: fromDate,
        };
        if (existing) {
          await this.manager.update(
            DailyReportPlayer,
            { id: existing.id },
            newData
          );
        } else {
          await this.manager.insert(DailyReportPlayer, newData);
        }
      }
    } catch (error) {
      this.logger.error(error);
    }
  }

  async getDailyReportPlayer(
    startDate: string,
    endDate: string,
    filter: GenericFilter,
    req: Request,
    playerId: string | undefined,
    email: string | undefined
  ) {
    try {
      const partnerId = req.headers['partner-id'] as string;

      if (!startDate || !endDate) {
        throw new BadRequestException(
          'É necessário informar a data inicial e final'
        );
      }

      const { fromDate, toDate } = this.dateService.getDateRange(
        startDate,
        endDate
      );

      const page = !filter.page || filter.page < 1 ? 1 : +filter.page;
      const pageSize =
        !filter.pageSize || filter.pageSize < 1 ? 10 : +filter.pageSize;
      const offset = (page - 1) * pageSize;

      const allowedOrderFields = ['currency', 'partnerId'];
      const orderBy = allowedOrderFields.includes(filter.orderBy)
        ? filter.orderBy
        : 'partnerId';

      const sortOrder = filter.sortOrder === 'DESC' ? 'DESC' : 'ASC';

      const qb = this.manager
        .createQueryBuilder()
        .select([
          'partner_id AS "partnerId"',
          'player_id AS "playerId"',
          'email AS "email"',
          'MAX(currency) AS "currency"',
          'SUM(total_bet)::numeric AS "totalBet"',
          'SUM(total_settle)::numeric AS "totalSettle"',
          'SUM(ggr)::numeric AS "ggr"',
        ])
        .from('casino.daily_report_player', 'drp')
        .where('partner_id = :partnerId', { partnerId })
        .andWhere('date BETWEEN :fromDate AND :toDate', { fromDate, toDate })
        .groupBy('partner_id, player_id, email, currency')
        .orderBy(`"${orderBy}"`, sortOrder as 'ASC' | 'DESC')
        .limit(pageSize)
        .offset(offset);

      if (playerId) {
        qb.andWhere('player_id = :playerId', { playerId });
      }

      if (email) {
        qb.andWhere('email = :email', { email });
      }

      const [data, total] = await Promise.all([
        qb.getRawMany(),
        this.manager
          .createQueryBuilder()
          .select('COUNT(*)', 'count')
          .from((qb2) => {
            return qb2
              .select('player_id')
              .from('casino.daily_report_player', 'drp')
              .where('partner_id = :partnerId', { partnerId })
              .andWhere('date BETWEEN :fromDate AND :toDate', {
                fromDate,
                toDate,
              })
              .groupBy('partner_id, player_id, email, currency')
              .andWhere(playerId ? 'player_id = :playerId' : '1=1', {
                playerId,
              });
          }, 'sub')
          .getRawOne<{ count: string }>(),
      ]);

      const formatted = data.map((item) => ({
        partnerId: item.partnerId,
        playerId: item.playerId,
        email: item.email,
        currency: item.currency,
        totalBet: Number(item.totalBet),
        totalSettle: Number(item.totalSettle),
        ggr: Number(item.ggr),
      }));

      return {
        data: formatted,
        totalItems: Number(total.count),
        totalPages: Math.ceil(Number(total.count) / pageSize),
        currentPage: page,
        pageSize: pageSize,
      };
    } catch (e) {
      this.logger.error(`[Error] erro ao consultar report game`);
      throw new HttpException(
        `Erro ao buscar report game: ${e.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async getDailyReportPlayerById(playerId: string, req: Request) {
    const partnerId = req.headers['partner-id'] as string;
    const whereCondition = {
      partnerId: partnerId,
      playerId: playerId,
    };

    const result = await this.manager.find(DailyReportPlayer, {
      where: whereCondition,
    });

    return result;
  }

  async saveDailyReportProviders() {
    try {
      const yesterday = subDays(new Date(), 1);
      const { fromDate, toDate } = this.dateService.getDateRange(
        yesterday.toISOString(),
        yesterday.toISOString()
      );
      const report = await this.manager
        .createQueryBuilder(CasinoTransactionEntity, 'transaction')
        .select('game.gameProvider', 'gameProvider')
        .addSelect('transaction.partnerId', 'partnerId')
        .addSelect('SUM(transaction.amount)', 'totalBetAmount')
        .addSelect('SUM(transaction.winAmount)', 'totalWinAmount')
        .addSelect('COUNT(transaction.roundEnded)', 'totalRound')
        .leftJoin(CasinoGames, 'game', 'game.gameId = transaction.gameId')
        .groupBy('game.gameProvider')
        .addGroupBy('transaction.partnerId')
        .where('transaction.createdAt BETWEEN :fromDate AND :toDate', {
          fromDate,
          toDate,
        })
        .getRawMany();

      for (const provider of report) {
        const existing = await this.manager.findOneBy(DailyReportProviders, {
          gameProvider: provider.gameProvider,
          partnerId: provider.partnerId,
          date: fromDate,
        });
        const newData = {
          gameProvider: provider.gameProvider,
          partnerId: provider.partnerId,
          totalBet: Number(provider.totalBetAmount),
          totalSettle: Number(provider.totalWinAmount),
          totalRound: Number(provider.totalRound),
          currency: 'BRL',
          ggr:
            Number(provider.totalBetAmount) - Number(provider.totalWinAmount),
          date: fromDate,
        };
        if (existing) {
          await this.manager.update(
            DailyReportProviders,
            { id: existing.id },
            newData
          );
        } else {
          await this.manager.insert(DailyReportProviders, newData);
        }
      }
    } catch (error) {
      this.logger.error(error);
    }
  }

  async getDailyReportProviders(
    startDate: string,
    endDate: string,
    req: Request
  ) {
    const partnerId = req.headers['partner-id'] as string;
    const { fromDate, toDate } = this.dateService.getDateRange(
      startDate,
      endDate
    );

    const whereCondition = {
      partnerId: partnerId,
      date: fromDate ? Between(fromDate, toDate) : Not(IsNull()),
    };

    const result = await this.manager.find(DailyReportProviders, {
      where: whereCondition,
    });

    return result;
  }

  async getDailyReportProvidersPagination(
    startDate: string,
    endDate: string,
    filter: GenericFilter,
    req: Request
  ) {
    const partnerId = req.headers['partner-id'] as string;

    if (!startDate || !endDate)
      throw new BadRequestException(
        'É necessário informar a data inicial e final'
      );

    const { fromDate, toDate } = this.dateService.getDateRange(
      startDate,
      endDate
    );

    if (!filter.sortOrder) {
      filter.sortOrder = SortOrder.ASC;
    }
    const page = !filter.page || filter.page < 1 ? 1 : filter.page;
    const pageSize =
      !filter.pageSize || filter.pageSize < 1 ? 10 : filter.pageSize;
    const offset = (page - 1) * pageSize;

    if (['gameProvider', 'currency'].includes(filter.orderBy)) {
      filter.orderBy = filter.orderBy;
    } else {
      filter.orderBy = 'gameProvider';
    }

    //Query para obter os dados
    const query = `
    SELECT 
      partner_id as "partnerId",
      MAX(game_provider) as "gameProvider",
      MAX(currency) as "currency",
      SUM(total_bet) as "totalBet",
      SUM(total_settle) as "totalSettle",
      SUM(ggr) as "ggr",
      SUM(total_round) as "totalRound"
    FROM 
      casino.daily_report_providers
      WHERE 
        partner_id = $1
        AND date BETWEEN $2 AND $3
      GROUP BY 
        partner_id, game_provider, currency
      ORDER BY 
        "${filter.orderBy}" ${filter.sortOrder}
      LIMIT $4
      OFFSET $5
    `;

    // Query para obter o total de registros para paginação
    const countQuery = `
      SELECT 
        COUNT(*) as "totalItems"
      FROM (
        SELECT 
          game_provider
        FROM 
          casino.daily_report_providers
        WHERE 
          partner_id = $1
          AND date BETWEEN $2 AND $3 
        GROUP BY 
          partner_id, game_provider, currency
      ) as count_table
    `;

    try {
      const queryParams = [partnerId, fromDate, toDate, pageSize, offset];

      const countParams = [partnerId, fromDate, toDate];

      const result = await this.manager.query(query, queryParams);
      const countResult = await this.manager.query(countQuery, countParams);

      const totalItems = parseInt(countResult[0].totalItems);
      const totalPages = Math.ceil(totalItems / pageSize);

      // Parse do resultado
      const formattedResult = result.map((item) => ({
        gameProvider: item.gameProvider,
        partnerId: item.partnerId,
        currency: item.currency,
        totalBet: Number(item.totalBet || 0),
        totalSettle: Number(item.totalSettle || 0),
        ggr: Number(item.ggr || 0),
        totalRound: Number(item.totalRound || 0),
      }));

      return {
        data: formattedResult,
        totalItems: totalItems,
        totalPages: totalPages,
        currentPage: page,
        pageSize: pageSize,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async saveDailyReportPartner() {
    try {
      const yesterday = subDays(new Date(), 1);
      const { fromDate, toDate } = this.dateService.getDateRange(
        yesterday.toISOString(),
        yesterday.toISOString()
      );
      const report = await this.manager
        .createQueryBuilder(CasinoTransactionEntity, 'transaction')
        .select('transaction.partnerId', 'partnerId')
        .addSelect('SUM(transaction.amount)', 'totalBetAmount')
        .addSelect('SUM(transaction.winAmount)', 'totalWinAmount')
        .addSelect('COUNT(transaction.roundEnded)', 'totalBets')
        .addSelect('partner.descriptionPartner', 'partnerName')
        .leftJoin(
          PartnersEntity,
          'partner',
          'partner.id = transaction.partnerId'
        )
        .groupBy('transaction.partnerId')
        .addGroupBy('partner.descriptionPartner')
        .where('transaction.createdAt BETWEEN :fromDate AND :toDate', {
          fromDate,
          toDate,
        })
        .getRawMany();

      for (const partner of report) {
        const existing = await this.manager.findOneBy(DailyReportPartner, {
          partnerId: partner.partnerId,
          date: fromDate,
        });
        const newData = {
          partnerId: partner.partnerId,
          totalBet: Number(partner.totalBetAmount),
          totalSettle: Number(partner.totalWinAmount),
          partnerName: partner.partnerName,
          totalRound: Number(partner.totalBets),
          currency: 'BRL',
          ggr: Number(partner.totalBetAmount) - Number(partner.totalWinAmount),
          date: fromDate,
        };
        if (existing) {
          await this.manager.update(
            DailyReportPartner,
            { id: existing.id },
            newData
          );
        } else {
          await this.manager.insert(DailyReportPartner, newData);
        }
      }
    } catch (error) {
      this.logger.error(error);
    }
  }

  async getDailyReportPartner(
    startDate: string,
    endDate: string,
    req: Request
  ) {
    const partnerId = req.headers['partner-id'] as string;
    const { fromDate, toDate } = this.dateService.getDateRange(
      startDate,
      endDate
    );
    const whereCondition = {
      partnerId: partnerId,
      date: fromDate ? Between(fromDate, toDate) : Not(IsNull()),
    };

    const result = await this.manager.find(DailyReportPartner, {
      where: whereCondition,
    });

    return result;
  }

  async getDailyReportPartnerPagination(
    startDate: string,
    endDate: string,
    filter: GenericFilter,
    req: Request
  ) {
    const partnerId = req.headers['partner-id'] as string;

    if (!startDate || !endDate)
      throw new BadRequestException(
        'É necessário informar a data inicial e final'
      );

    const { fromDate, toDate } = this.dateService.getDateRange(
      startDate,
      endDate
    );

    if (!filter.sortOrder) {
      filter.sortOrder = SortOrder.ASC;
    }
    const page = !filter.page || filter.page < 1 ? 1 : filter.page;
    const pageSize =
      !filter.pageSize || filter.pageSize < 1 ? 10 : filter.pageSize;
    const offset = (page - 1) * pageSize;

    if (['partnerId', 'currency'].includes(filter.orderBy)) {
      filter.orderBy = filter.orderBy;
    } else {
      filter.orderBy = 'partnerId';
    }

    //Query para obter os dados
    const query = `
    SELECT 
      partner_id as "partnerId",      
      MAX(currency) as "currency",
      SUM(total_bet) as "totalBet",
      SUM(total_settle) as "totalSettle",
      SUM(ggr) as "ggr",
      SUM(total_round) as "totalRound"
    FROM 
      casino.daily_report_partner
      WHERE 
        partner_id = $1
        AND date BETWEEN $2 AND $3
      GROUP BY 
        partner_id, currency
      ORDER BY 
        "${filter.orderBy}" ${filter.sortOrder}
      LIMIT $4
      OFFSET $5
    `;

    // Query para obter o total de registros para paginação
    const countQuery = `
      SELECT 
        COUNT(*) as "totalItems"
      FROM (
        SELECT 
          partner_id
        FROM 
          casino.daily_report_partner
        WHERE 
          partner_id = $1
          AND date BETWEEN $2 AND $3 
        GROUP BY 
         partner_id, currency
      ) as count_table
    `;

    try {
      const queryParams = [partnerId, fromDate, toDate, pageSize, offset];

      const countParams = [partnerId, fromDate, toDate];

      const result = await this.manager.query<DailyReportPartner[]>(
        query,
        queryParams
      );
      const countResult = await this.manager.query(countQuery, countParams);

      const totalItems = parseInt(countResult[0].totalItems);
      const totalPages = Math.ceil(totalItems / pageSize);

      // Parse do resultado
      const formattedResult = result.map((item) => ({
        partnerId: item.partnerId,
        currency: item.currency,
        totalBet: Number(item.totalBet || 0),
        totalSettle: Number(item.totalSettle || 0),
        ggr: Number(item.ggr || 0),
        totalRound: Number(item.totalRound || 0),
      }));

      return {
        data: formattedResult,
        totalItems: totalItems,
        totalPages: totalPages,
        currentPage: page,
        pageSize: pageSize,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async findGameProfitability(
    filter: GenericFilter & GameProfitabilityFilterDto,
    partnerId: string
  ): Promise<{
    data: GameProfitabilityResponseDto[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    try {
      if (!filter.dateRange?.from || !filter.dateRange?.to) {
        throw new HttpException(
          'É necessário informar o período de datas',
          HttpStatus.BAD_REQUEST
        );
      }

      const isStartToday =
        filter.dateRange.from && isToday(parseISO(filter.dateRange.from));
      const isEndToday =
        filter.dateRange.to && isToday(parseISO(filter.dateRange.to));

      if (isStartToday || isEndToday) {
        const todayData = await this.getDailyReportGame(
          filter.dateRange.from,
          filter.dateRange.to,
          { headers: { 'partner-id': partnerId } } as unknown as Request,
          filter.gameProvider
        );

        const groupedData = todayData.reduce(
          (acc: Record<string, GameProfitabilityResponseDto>, curr: any) => {
            if (!acc[curr.gameId]) {
              acc[curr.gameId] = {
                gameId: curr.gameId,
                gameName: curr.gameName,
                gameProvider: curr.gameProvider,
                totalBet: 0,
                totalSettle: 0,
                ggr: 0,
                profitability: 0,
              };
            }
            acc[curr.gameId].totalBet += Number(curr.totalBet.toFixed(2));
            acc[curr.gameId].totalSettle += Number(curr.totalSettle.toFixed(2));
            acc[curr.gameId].ggr += Number(curr.ggr.toFixed(2));
            acc[curr.gameId].profitability =
              acc[curr.gameId].totalBet > 0
                ? Number(
                    (
                      (acc[curr.gameId].ggr / acc[curr.gameId].totalBet) *
                      100
                    ).toFixed(2)
                  )
                : 0;
            return acc;
          },
          {}
        );

        const page = !filter.page || filter.page < 1 ? 1 : filter.page;
        const pageSize =
          !filter.pageSize || filter.pageSize < 1 ? 10 : filter.pageSize;
        const offset = (page - 1) * pageSize;

        const sortedData = Object.values(groupedData).sort(
          (
            a: GameProfitabilityResponseDto,
            b: GameProfitabilityResponseDto
          ) => {
            const order = filter.sortOrder === SortOrder.ASC ? 1 : -1;
            const orderBy = filter.orderBy || 'profitability';
            const aValue = a[orderBy as keyof GameProfitabilityResponseDto];
            const bValue = b[orderBy as keyof GameProfitabilityResponseDto];

            if (typeof aValue === 'number' && typeof bValue === 'number') {
              return (aValue - bValue) * order;
            }

            const aStr = String(aValue || '');
            const bStr = String(bValue || '');
            return (
              aStr.localeCompare(bStr, undefined, {
                numeric: true,
                sensitivity: 'base',
              }) * order
            );
          }
        );

        const paginatedData = sortedData.slice(offset, offset + pageSize);

        return {
          data: paginatedData as GameProfitabilityResponseDto[],
          totalItems: Object.keys(groupedData).length,
          totalPages: Math.ceil(Object.keys(groupedData).length / pageSize),
          currentPage: page,
          pageSize: pageSize,
        };
      }

      const dateRange = this.dateService.getDateRange(
        filter.dateRange.from,
        filter.dateRange.to
      );

      if (!filter.sortOrder) {
        filter.sortOrder = SortOrder.ASC;
      }
      const page = !filter.page || filter.page < 1 ? 1 : filter.page;
      const pageSize =
        !filter.pageSize || filter.pageSize < 1 ? 10 : filter.pageSize;
      const offset = (page - 1) * pageSize;

      const query = `
        SELECT 
          game_id as "gameId",
          MAX(game_name) as "gameName",
          MAX(game_provider) as "gameProvider",
          SUM(total_bet) as "totalBet",
          SUM(total_settle) as "totalSettle",
          SUM(ggr) as "ggr",
          CASE 
            WHEN SUM(total_bet) > 0 
            THEN ROUND((SUM(ggr) / SUM(total_bet) * 100)::numeric, 2)
            ELSE 0 
          END as "profitability"
        FROM 
          casino.daily_report_game
        WHERE 
          partner_id = $1
          AND date BETWEEN $2 AND $3
          ${filter.gameProvider ? 'AND game_provider = $6' : ''}
        GROUP BY 
          game_id
        ORDER BY 
          "${filter.orderBy || 'profitability'}" ${filter.sortOrder}
        LIMIT $4
        OFFSET $5
      `;

      const countQuery = `
        SELECT COUNT(*) as "totalItems"
        FROM (
          SELECT game_id
          FROM casino.daily_report_game
          WHERE 
            partner_id = $1
            AND date BETWEEN $2 AND $3
            ${filter.gameProvider ? 'AND game_provider = $4' : ''}
          GROUP BY game_id
        ) as count_table
      `;

      const queryParams = [
        partnerId,
        dateRange.fromDate,
        dateRange.toDate,
        pageSize,
        offset,
        ...(filter.gameProvider ? [filter.gameProvider] : []),
      ];

      const countParams = [
        partnerId,
        dateRange.fromDate,
        dateRange.toDate,
        ...(filter.gameProvider ? [filter.gameProvider] : []),
      ];

      const result = await this.manager.query(query, queryParams);
      const countResult = await this.manager.query(countQuery, countParams);

      const totalItems = parseInt(countResult[0].totalItems);
      const totalPages = Math.ceil(totalItems / pageSize);

      const formattedResult = result.map((item) => ({
        gameId: item.gameId,
        gameName: item.gameName,
        gameProvider: item.gameProvider,
        totalBet: Number(item.totalBet || 0),
        totalSettle: Number(item.totalSettle || 0),
        ggr: Number(item.ggr || 0),
        profitability: Number(item.profitability || 0),
      }));

      return {
        data: formattedResult,
        totalItems,
        totalPages,
        currentPage: page,
        pageSize,
      };
    } catch (error) {
      this.logger.error(`Error in findGameProfitability: ${error}`);
      throw new HttpException(
        error.message || 'Erro interno do servidor',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
