import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsUUID, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
class PromoWinDetailsDto {
  @ApiProperty({ example: '0.01' })
  @IsString()
  wager: string;
}

export class PromoWinDto {
  @ApiProperty({ example: '0.01' })
  @IsString()
  amount: string;

  @ApiProperty({ example: 'EUR' })
  @IsString()
  currency: string;

  @ApiProperty({ type: () => PromoWinDetailsDto })
  @ValidateNested()
  @Type(() => PromoWinDetailsDto)
  details: PromoWinDetailsDto;

  @ApiProperty({ example: '09553597-9d42-4efe-800e-dec3e5b9de33' })
  @IsUUID()
  eventId: string;

  @ApiProperty({ example: 'Tournament' })
  @IsString()
  eventType: string;

  @ApiProperty({ example: '6a73e309-4ec8-4b3b-8082-612ba5329f4c' })
  @IsUUID()
  id: string;

  @ApiProperty({ example: '8b7a9ec9-6e22-42e7-83ed-64d42cd5f5ac' })
  @IsUUID()
  playerId: string;

  @ApiProperty({ example: 'sample_provider' })
  @IsString()
  provider: string;
}
