import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsUUID } from 'class-validator';

export class ResponsePromoPrizeDto {
  @ApiProperty({ example: '9b41d5fc-f4f7-4588-bdd7-8b8f3b220946' })
  @IsUUID()
  id: string;

  @ApiProperty({ example: '7a822265-6073-4274-a19e-a03314ba282e' })
  @IsUUID()
  id_casino: string;

  @ApiProperty({ example: '2022-06-15T15:30:00.000054Z' })
  @IsDateString()
  processed_at: string;
}
