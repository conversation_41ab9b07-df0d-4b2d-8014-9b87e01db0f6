import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { SettleService } from './settle.service';
import { CreateSettleDto } from './dto/create-settle.dto';
import { HMACGuard } from '@/common/guards/hmac.guard';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import {
  ApiHmacHeader,
  ApiHmacUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@/common/decorators/swagger.decorator';
import { SwaggerApiCodeResponses } from '@/common/decorators/swagger-api-code-responses';
import {
  SUCCESS,
  ALREADY_PROCESSED,
  GENERIC_ERROR,
  PLAYER_NOT_FOUND,
  SESSION_NOT_FOUND,
  GAME_NOT_FOUND,
  ROUND_NOT_FOUND,
  BET_NOT_FOUND,
} from '@/common/constants/message-codes';
import {
  CreateSettleResponseDto,
  CreateSettleResponseDtoExample,
} from './dto/create-settle-response.dto';

@Controller('settle')
@ApiTags('Settle')
@ApiHmacHeader()
export class SettleController {
  constructor(private readonly settleService: SettleService) {}

  @UseGuards(HMACGuard)
  @Post()
  @ApiOperation({
    summary: 'Liquidar aposta',
    description: 'Endpoint para liquidar uma aposta realizada',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
      BET_NOT_FOUND.code,
    ],
    CreateSettleResponseDto,
    CreateSettleResponseDtoExample
  )
  @ApiHmacUnauthorizedResponse()
  @ApiForbiddenResponse()
  @ApiInternalServerErrorResponse()
  async create(@Body() createBetDto: CreateSettleDto): Promise<{
    balance?: number;
    errorCode: number;
    errorMsg?: string;
    casinoTransactionId?: string;
    reconcileAmount?: number;
  }> {
    return (await this.settleService.set(createBetDto)) as any;
  }
}
