import {
  ALREADY_PROCESSED,
  BET_NOT_FOUND,
  GAME_NOT_FOUND,
  GENERIC_ERROR,
  PLAYER_NOT_FOUND,
  ROUND_NOT_FOUND,
  SESSION_NOT_FOUND,
  SUCCESS,
} from '@/common/constants/message-codes';
import { SwaggerApiCodeResponses } from '@/common/decorators/swagger-api-code-responses';
import {
  ApiForbiddenResponse,
  ApiHmacHeader,
  ApiHmacUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@/common/decorators/swagger.decorator';
import { Body, Controller, Post } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import {
  CreateSettleResponseDto,
  CreateSettleResponseDtoExample,
} from './dto/create-settle-response.dto';
import { CreateSettleDto } from './dto/create-settle.dto';
import { SettleService } from './settle.service';

@Controller('settle')
@ApiTags('Settle')
@ApiHmacHeader()
export class SettleController {
  constructor(private readonly settleService: SettleService) {}

  //@UseGuards(HMACGuard)
  @Post()
  @ApiOperation({
    summary: 'Liquidar aposta',
    description: 'Endpoint para liquidar uma aposta realizada',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
      BET_NOT_FOUND.code,
    ],
    CreateSettleResponseDto,
    CreateSettleResponseDtoExample,
  )
  @ApiHmacUnauthorizedResponse()
  @ApiForbiddenResponse()
  @ApiInternalServerErrorResponse()
  async create(@Body() createBetDto: CreateSettleDto) {
    return (await this.settleService.set(createBetDto)) as any;
  }
}
