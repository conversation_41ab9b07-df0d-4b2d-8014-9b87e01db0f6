import { Test, TestingModule } from '@nestjs/testing';
import { NewSessionController } from './new-session.controller';
import { NewSessionService } from './new-session.service';
import { HMACGuard } from '@/common/guards/hmac.guard';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { JwtAuthPlayerGuard } from '@/common/guards/player/jwt-auth-player.guard';

describe('NewSessionController', () => {
  let controller: NewSessionController;

  const mockNewSessionService = {};
  const mockHMACGuard = { canActivate: jest.fn(() => true) };
  const mockJwtService = { verify: jest.fn(), decode: jest.fn() };
  const mockConfigService = { get: jest.fn() };
  const mockJwtAuthPlayerGuard = { canActivate: jest.fn(() => true) };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [NewSessionController],
      providers: [
        { provide: NewSessionService, useValue: mockNewSessionService },
        { provide: JwtService, useValue: mockJwtService },
        { provide: ConfigService, useValue: mockConfigService },
      ],
    })
      .overrideGuard(HMACGuard)
      .useValue(mockHMACGuard)
      .overrideGuard(JwtAuthPlayerGuard)
      .useValue(mockJwtAuthPlayerGuard)
      .compile();

    controller = module.get<NewSessionController>(NewSessionController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
