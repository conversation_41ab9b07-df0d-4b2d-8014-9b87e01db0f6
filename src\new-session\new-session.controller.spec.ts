import { Test, TestingModule } from '@nestjs/testing';
import { NewSessionController } from './new-session.controller';
import { NewSessionService } from './new-session.service';
import { HMACGuard } from '@/common/guards/hmac.guard';

describe('NewSessionController', () => {
  let controller: NewSessionController;

  const mockNewSessionService = {};
  const mockHMACGuard = { canActivate: jest.fn(() => true) };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [NewSessionController],
      providers: [
        { provide: NewSessionService, useValue: mockNewSessionService },
      ],
    })
      .overrideGuard(HMACGuard)
      .useValue(mockHMACGuard)
      .compile();

    controller = module.get<NewSessionController>(NewSessionController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
}); 