import { CasinoTransactionEntity } from '@/casino-transaction/controller/entities/casino-transactions.entity';
import { TransactionResponse } from '@/wallet/interfaces/debit';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { CasinoSessionEntity } from '../common/entities/casino-session.entity';
import { WalletService } from '../wallet/wallet.service';
import { GetBalanceDto } from './dto/get-balance.dto';
import { RequestDepositDto } from './dto/request-deposit.dto';

@Injectable()
export class BalanceService {
  private readonly logger = new Logger(BalanceService.name);
  constructor(
    @InjectEntityManager() private readonly manager: EntityManager,
    private readonly walletService: WalletService,
  ) {}

  async getBalance(getBalance: GetBalanceDto) {
    this.logger.log(`[Inicio] Get balance: ${JSON.stringify(getBalance)}`);
    let balance = 0;
    const { playerId, sessionId } = getBalance;
    const casinoSession = await this.manager.findOne(CasinoSessionEntity, {
      where: {
        playerId,
        aggregatorId: sessionId,
      },
    });
    if (!casinoSession) {
      this.logger.error(`Session not found.`);
      throw new HttpException('Sessão não encontrada.', HttpStatus.NOT_FOUND);
    }
    balance = await this.getBalanceInWallet(
      casinoSession.partnerId,
      casinoSession.playerId,
    );
    this.logger.log(`[Fim] Get balance: ${JSON.stringify(getBalance)}`);
    return balance;
  }

  async getBalanceInWallet(partnerId: string, playerId: string) {
    try {
      this.logger.log(`[Inicio] Fetching balance for player ${playerId}`);
      const response = await this.walletService.getBalanceByPlayer(
        partnerId,
        playerId,
      );

      if (response && response.balance !== undefined) {
        this.logger.log(`[Fim] Fetching balance for player ${playerId}`);
        return response.balance;
      } else {
        this.logger.error(`Balance not found for player ${playerId}`);
        throw new HttpException('Saldo não encontrado.', HttpStatus.NOT_FOUND);
      }
    } catch (error) {
      this.logger.error(`Error fetching balance: ${error}`);
      throw new HttpException(
        'Erro ao buscar saldo.',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  async getDetailedBalance(getBalance: GetBalanceDto) {
    this.logger.log(
      `[Inicio] Get detailed balance: ${JSON.stringify(getBalance)}`,
    );
    const { playerId, sessionId } = getBalance;

    try {
      const casinoSession = await this.manager.findOne(CasinoSessionEntity, {
        where: {
          playerId,
          aggregatorId: sessionId,
        },
      });

      if (!casinoSession) {
        this.logger.error(`Session not found.`);
        throw new HttpException('Sessão não encontrada.', HttpStatus.NOT_FOUND);
      }

      const response = await this.walletService.getBalanceByPlayerWithBonus(
        casinoSession.partnerId,
        casinoSession.playerId,
      );

      const realBalance = Number(response?.realBalance?.balance ?? 0);
      const bonusBalance = Number(response?.bonusBalance?.balance ?? 0);

      this.logger.log(
        `[Fim] Get detailed balance: ${JSON.stringify(getBalance)}`,
      );
      return { realBalance, bonusBalance };
    } catch (error) {
      this.logger.error(`Error fetching detailed balance: ${error}`);
      throw new HttpException(
        'Erro ao buscar saldo detalhado.',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  async deposit(depositDto: RequestDepositDto): Promise<TransactionResponse> {
    try {
      this.logger.log(`[Inicio] Deposit: ${JSON.stringify(depositDto)}`);

      const searchPartner = await this.manager.findOne(CasinoSessionEntity, {
        select: ['partnerId'],
        where: {
          playerId: depositDto.playerId,
        },
      });

      if (!searchPartner) {
        this.logger.error(
          `Session not found for player ${depositDto.playerId}`,
        );
        throw new HttpException('Sessão não encontrada.', HttpStatus.NOT_FOUND);
      }

      const partnerId = searchPartner.partnerId;

      await this.manager.insert(CasinoTransactionEntity, {
        partnerId: partnerId,
        playerId: depositDto.playerId,
        gameId: depositDto.gameId,
        // requestType: depositDto.typeId.toString(),
        // transactionId: depositDto.transactionId.toString(),
        winAmount: Number(depositDto.amount),
      });

      const response = await this.walletService.credit(
        {
          amount: depositDto.amount,
          reason: depositDto.typeId.toString(),
          isBonusOperation: false,
          gameId: depositDto.gameId,
          adjustmentType: 'deposit',
        },
        partnerId,
        depositDto.playerId,
      );

      this.logger.log(`[Fim] Deposit: ${JSON.stringify(depositDto)}`);
      return response;
    } catch (error) {
      this.logger.error(`Error processing deposit: ${error}`);

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'Erro ao processar depósito.',
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
