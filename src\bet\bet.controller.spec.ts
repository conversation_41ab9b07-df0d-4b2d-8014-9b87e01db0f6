import { Test, TestingModule } from '@nestjs/testing';
import { BetController } from './bet.controller';
import { BetService } from './bet.service';
import { HMACGuard } from '@/common/guards/hmac.guard';

describe('BetController', () => {
  let controller: BetController;

  const mockBetService = {};
  const mockHMACGuard = { canActivate: jest.fn(() => true) };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BetController],
      providers: [
        { provide: BetService, useValue: mockBetService },
      ],
    })
      .overrideGuard(HMACGuard)
      .useValue(mockHMACGuard)
      .compile();

    controller = module.get<BetController>(BetController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
}); 