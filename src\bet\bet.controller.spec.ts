import { Test, TestingModule } from '@nestjs/testing';
import { Bet<PERSON>ontroller } from './bet.controller';
import { BetService } from './bet.service';
import { HMACGuard } from '@/common/guards/hmac.guard';
import { CreateBetDto } from './dto/create-bet.dto';
import { Jwt } from '@/common/interfaces/JWT';
import { GenderEnum } from '@/gamelaunch/enum/gender.enum';

describe('BetController', () => {
  let controller: BetController;
  let service: BetService;

  const mockBetService = {
    create: jest.fn(),
  };
  const mockHMACGuard = { canActivate: jest.fn(() => true) };

  const uuid = '123e4567-e89b-12d3-a456-************';
  const mockAuth: Jwt = {
    payload: {
      id: uuid,
      currency: 'BRL',
      language: 'pt',
      country: 'BR',
      firstName: 'Test',
      lastName: 'User',
      alias: 'testuser',
      gender: GenderEnum.MALE,
      balance: '100',
      email: '<EMAIL>',
      password: '123',
      partnerId: uuid,
      partner: {
        id: uuid,
        cashierUrl: '',
        closeUrl: '',
        name: '<PERSON>rceiro',
      },
    },
    iat: 0,
    exp: 0,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BetController],
      providers: [{ provide: BetService, useValue: mockBetService }],
    })
      .overrideGuard(HMACGuard)
      .useValue(mockHMACGuard)
      .compile();

    controller = module.get<BetController>(BetController);
    service = module.get<BetService>(BetService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('deve criar aposta com sucesso', async () => {
      const createBetDto: CreateBetDto = {
        sessionId: 'session-123',
        transactionId: 'tx-123',
        playerId: 'player-123',
        requestType: 'RealMoney',
        amount: 100.5,
        gameId: 'game-123',
        roundId: 'round-123',
      };

      const expectedResponse = {
        balance: 50.5,
        casinoTransactionId: 'casino-tx-123',
        reconcileAmount: 0,
      };

      mockBetService.create.mockResolvedValue(expectedResponse);

      const result = await controller.create(createBetDto, mockAuth);

      expect(result).toEqual(expectedResponse);
      expect(service.create).toHaveBeenCalledWith(createBetDto, mockAuth);
    });

    it('deve chamar service com parâmetros corretos', async () => {
      const createBetDto: CreateBetDto = {
        sessionId: 'session-456',
        transactionId: 'tx-456',
        playerId: 'player-456',
        requestType: 'RealMoney',
        amount: 50,
        gameId: 'game-456',
        roundId: 'round-456',
      };

      mockBetService.create.mockResolvedValue({
        balance: 150,
        casinoTransactionId: 'tx-casino-456',
        reconcileAmount: 0,
      });

      await controller.create(createBetDto, mockAuth);

      expect(service.create).toHaveBeenCalledTimes(1);
      expect(service.create).toHaveBeenCalledWith(createBetDto, mockAuth);
    });

    it('deve propagar erro do service', async () => {
      const createBetDto: CreateBetDto = {
        sessionId: 'session-error',
        transactionId: 'tx-error',
        playerId: 'player-error',
        requestType: 'RealMoney',
        amount: 100,
        gameId: 'game-error',
        roundId: 'round-error',
      };

      mockBetService.create.mockRejectedValue(new Error('Service error'));

      await expect(controller.create(createBetDto, mockAuth)).rejects.toThrow(
        'Service error',
      );
    });

    it('deve retornar balance atualizado após aposta', async () => {
      const createBetDto: CreateBetDto = {
        sessionId: 'session-balance',
        transactionId: 'tx-balance',
        playerId: 'player-balance',
        requestType: 'RealMoney',
        amount: 25.5,
        gameId: 'game-balance',
        roundId: 'round-balance',
      };

      const expectedResponse = {
        balance: 74.5,
        casinoTransactionId: 'casino-tx-balance',
        reconcileAmount: 0,
      };

      mockBetService.create.mockResolvedValue(expectedResponse);

      const result = await controller.create(createBetDto, mockAuth);

      expect(result.balance).toBe(74.5);
      expect(result.casinoTransactionId).toBe('casino-tx-balance');
    });

    it('deve processar aposta com valores decimais', async () => {
      const createBetDto: CreateBetDto = {
        sessionId: 'session-decimal',
        transactionId: 'tx-decimal',
        playerId: 'player-decimal',
        requestType: 'RealMoney',
        amount: 10.99,
        gameId: 'game-decimal',
        roundId: 'round-decimal',
      };

      mockBetService.create.mockResolvedValue({
        balance: 89.01,
        casinoTransactionId: 'tx-decimal',
        reconcileAmount: 0,
      });

      const result = await controller.create(createBetDto, mockAuth);

      expect(result).toBeDefined();
      expect(service.create).toHaveBeenCalledWith(createBetDto, mockAuth);
    });
  });
});
