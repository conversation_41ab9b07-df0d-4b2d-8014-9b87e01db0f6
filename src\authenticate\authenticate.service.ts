import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { CreateRegisterAuthenticateDto } from './dto/create-register-authenticate.dto';
import { CasinoSessionEntity } from '@/common/entities/casino-session.entity';
import { EntityManager } from 'typeorm';
import { WalletService } from '@/wallet/wallet.service';
import { InjectEntityManager } from '@nestjs/typeorm';

@Injectable()
export class AuthenticateService {
  private readonly logger = new Logger(AuthenticateService.name);
  constructor(
    @InjectEntityManager() private readonly manager: EntityManager,
    private readonly walletService: WalletService,
  ) {}

  async authenticate(
    createRegisterAuthenticate: CreateRegisterAuthenticateDto,
  ) {
    this.logger.log(
      `[Inicio] Authenticate: ${JSON.stringify(createRegisterAuthenticate)}`,
    );
    let balance = 0;
    const { token, sessionId } = createRegisterAuthenticate;
    const casinoSession = await this.manager.findOne(CasinoSessionEntity, {
      where: {
        launchToken: token,
      },
    });
    if (!casinoSession || casinoSession.launchToken !== token) {
      this.logger.error(`Invalid or Expired token`);
      throw new HttpException(
        'Token inválido ou expirado.',
        HttpStatus.NOT_FOUND,
      );
    }

    try {
      const [fetchedBalance] = await Promise.all([
        this.getBalance(casinoSession.playerId, casinoSession.partnerId),
        this.updateSessionAuthId(casinoSession, sessionId, token),
      ]);
      balance = fetchedBalance;
    } catch (error) {
      this.logger.error(`Erro ao buscar balance: ${error}`);
      balance = 0;
    }

    this.logger.log(
      `[Fim] Authenticate: ${JSON.stringify(createRegisterAuthenticate)}`,
    );
    return balance;
  }

  async getBalance(playerId: string, partnerId: string) {
    try {
      this.logger.log(`[Inicio] Fetching balance for player ${playerId}`);
      const response = await this.walletService.getBalanceByPlayer(
        partnerId,
        playerId,
      );
      if (response?.balance !== undefined) {
        this.logger.log(`[Fim] Fetching balance for player ${playerId}`);
        return response.balance;
      } else {
        this.logger.error(`Balance not found for player ${playerId}`);
        throw new HttpException('Saldo não encontrado.', HttpStatus.NOT_FOUND);
      }
    } catch (error) {
      this.logger.error(`Error fetching balance: ${error}`);
      throw new HttpException(
        'Erro ao buscar saldo.',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  private async updateSessionAuthId(
    casinoSession: CasinoSessionEntity,
    sessionId: string,
    token: string,
  ): Promise<void> {
    this.logger.log(
      `[Inicio] Updating session auth id: ${JSON.stringify(casinoSession)}`,
    );
    await this.manager.save(CasinoSessionEntity, {
      ...casinoSession,
      aggregatorId: sessionId,
      token,
    });
    this.logger.log(
      `[Fim] Updating session auth id: ${JSON.stringify(casinoSession)}`,
    );
  }
}
