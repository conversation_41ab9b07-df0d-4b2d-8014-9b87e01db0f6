import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Request } from 'express';

@Injectable()
export class InternalApiKeyGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request: Request = context.switchToHttp().getRequest();
    // console.log('InternalApiKeyGuard activated');
    // console.log('Request Headers:', request.headers);
    const apiKey = request.headers['x-api-key'];

    if (!apiKey || apiKey !== process.env.CASINO_INTERNAL_API_KEY) {
      throw new UnauthorizedException('Invalid or missing Internal API key');
    }

    return true;
  }
}
