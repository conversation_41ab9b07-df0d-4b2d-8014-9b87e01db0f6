import { Test, TestingModule } from '@nestjs/testing';
import { HMACService } from './hmac.service';

describe('HMACService', () => {
  let service: HMACService;
  const SECRET_KEY = 'test_secret';

  beforeEach(async () => {
    process.env.SECRET_KEY = SECRET_KEY;
    const module: TestingModule = await Test.createTestingModule({
      providers: [HMACService],
    }).compile();

    service = module.get<HMACService>(HMACService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateHMAC', () => {
    it('deve gerar o HMAC correto para um dado', () => {
      const data = JSON.stringify({ foo: 'bar' });
      const expected = require('crypto')
        .createHmac('sha256', SECRET_KEY)
        .update(data)
        .digest('hex');
      expect(service.generateHMAC(data)).toBe(expected);
    });
    it('deve gerar HMAC mesmo sem SECRET_KEY definida', () => {
      delete process.env.SECRET_KEY;
      const data = 'abc';
      // O resultado será diferente, mas não deve lançar erro
      expect(() => service.generateHMAC(data)).not.toThrow();
    });
  });

  describe('validateHMAC', () => {
    it('deve retornar true para HMAC válido', () => {
      const data = JSON.stringify({ foo: 'bar' });
      const hmac = service.generateHMAC(data);
      expect(service.validateHMAC(data, hmac)).toBe(true);
    });
    it('deve retornar false para HMAC inválido', () => {
      const data = JSON.stringify({ foo: 'bar' });
      const hmac = 'invalido';
      expect(service.validateHMAC(data, hmac)).toBe(false);
    });
    it('deve retornar false para HMAC de tamanho diferente', () => {
      const data = JSON.stringify({ foo: 'bar' });
      const hmac = service.generateHMAC(data) + 'extra';
      expect(service.validateHMAC(data, hmac)).toBe(false);
    });
  });
}); 