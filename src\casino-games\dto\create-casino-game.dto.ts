import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsBoolean,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

export class CreateCasinoGameDto {
  @ApiProperty({
    description: 'Nome do jogo',
    example: 'Fortune Tiger',
    required: true,
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Descrição do jogo',
    example: 'Jogo de slots com tema de tigre',
    required: true,
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'URL da imagem do jogo (gerado automaticamente após upload)',
    example: 'https://example.com/games/fortune-tiger.jpg',
    type: String,
    format: 'binary',
    required: false,
  })
  @IsOptional()
  @IsString()
  image?: string;

  @ApiProperty({
    description:
      'Caminho (key) da imagem no S3 (gerado automaticamente após upload)',
    example: 'casino_games/a1b2c3d4-e5f6-4a5b-8c9d-0e1f2a3b4c5d.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  path?: string;

  @ApiProperty({
    description:
      'Imagem do jogo em formato base64 (alternativa ao upload de arquivo). Formato: data:image/[tipo];base64,[dados]. Exemplo: data:image/png;base64,iVBORw0KGgoAAAANS...',
    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUA...',
    required: false,
  })
  @IsOptional()
  @IsString()
  imageBase64?: string;

  @ApiProperty({
    description:
      'URL da imagem do aggregator (gerado automaticamente após upload)',
    example: 'https://example.com/games/fortune-tiger-aggregator.jpg',
    type: String,
    format: 'binary',
    required: false,
  })
  @IsOptional()
  @IsString()
  imageAggregator?: string;

  @ApiProperty({
    description:
      'Caminho (key) da imagem do aggregator no S3 (gerado automaticamente após upload)',
    example: 'casino_games/a1b2c3d4-e5f6-4a5b-8c9d-0e1f2a3b4c5d_aggregator.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  pathAggregator?: string;

  @ApiProperty({
    description:
      'Imagem do aggregator em formato base64 (alternativa ao upload de arquivo). Formato: data:image/[tipo];base64,[dados]. Exemplo: data:image/png;base64,iVBORw0KGgoAAAANS...',
    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUA...',
    required: false,
  })
  @IsOptional()
  @IsString()
  imageBase64Aggregator?: string;

  @ApiProperty({
    description: 'Indica se o jogo está desabilitado',
    example: false,
    required: true,
  })
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  isDisable: boolean;

  @ApiProperty({
    description: 'ID do jogo no provedor',
    example: '12345',
    required: true,
  })
  @IsString()
  gameId: string;

  @ApiProperty({
    description: 'Nome do provedor do jogo',
    example: 'Pragmatic Play',
    required: true,
  })
  @IsString()
  gameProvider: string;

  @ApiProperty({
    description:
      'ID do provedor do jogo (UUID da tabela casino_game_providers)',
    example: 'a1b2c3d4-e5f6-4a5b-8c9d-0e1f2a3b4c5d',
    required: true,
  })
  @IsUUID()
  gameProviderId: string;

  @ApiProperty({
    description:
      'ID da categoria do jogo (UUID da tabela casino_game_categories)',
    example: 'f6e5d4c3-b2a1-9f8e-7d6c-5b4a3e2d1c0b',
    required: true,
  })
  @IsUUID()
  gameCategoryId: string;

  @ApiProperty({
    description:
      'Posição do jogo na listagem (deve ser única). Valores comuns: 1, 2, 3, etc.',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  position?: number;

  @ApiProperty({
    description: 'Indica se o jogo está disponível para desktop',
    example: true,
    required: false,
  })
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsOptional()
  @IsBoolean()
  isDesktop?: boolean;

  @ApiProperty({
    description: 'Indica se o jogo está disponível para mobile',
    example: true,
    required: false,
  })
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsOptional()
  @IsBoolean()
  isMobile?: boolean;
}

export const CreateCasinoGameDtoExample = {
  id: 'game-123',
  name: 'Fortune Tiger',
  description: 'Jogo de slots com tema de tigre',
  image: 'https://example.com/games/fortune-tiger.jpg',
  isDisable: false,
  gameId: '12345',
  gameProvider: 'Pragmatic Play',
  gameProviderId: 'a1b2c3d4-e5f6-4a5b-8c9d-0e1f2a3b4c5d',
  gameCategoryId: 'f6e5d4c3-b2a1-9f8e-7d6c-5b4a3e2d1c0b',
};
