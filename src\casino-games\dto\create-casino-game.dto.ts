import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsString } from 'class-validator';

export class CreateCasinoGameDto {
  @ApiProperty({
    description: 'ID único do jogo',
    example: 'game-123',
    required: true,
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Nome do jogo',
    example: 'Fortune Tiger',
    required: true,
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Descri<PERSON> do jogo',
    example: 'Jogo de slots com tema de tigre',
    required: true,
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'URL da imagem do jogo',
    example: 'https://example.com/games/fortune-tiger.jpg',
    required: true,
  })
  @IsString()
  image: string;

  @ApiProperty({
    description: 'Indica se o jogo está desabilitado',
    example: false,
    required: true,
  })
  @IsBoolean()
  isDisable: boolean;

  @ApiProperty({
    description: 'ID do jogo no provedor',
    example: '12345',
    required: true,
  })
  @IsNumber()
  gameId: string;

  @ApiProperty({
    description: 'Nome do provedor do jogo',
    example: 'Pragmatic Play',
    required: true,
  })
  @IsString()
  gameProvider: string;

  @ApiProperty({
    description: 'Usuário que fez a última atualização',
    example: 'admin',
    required: true,
  })
  @IsString()
  userLastUpdate: string;
}

export const CreateCasinoGameDtoExample = {
  id: 'game-123',
  name: 'Fortune Tiger',
  description: 'Jogo de slots com tema de tigre',
  image: 'https://example.com/games/fortune-tiger.jpg',
  isDisable: false,
};
