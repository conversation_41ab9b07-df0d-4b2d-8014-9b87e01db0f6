import { CasinoTransactionEntity } from '@/casino-transaction/controller/entities/casino-transactions.entity';
import { WalletService } from '@/wallet/wallet.service';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import 'dotenv/config';
import { EntityManager } from 'typeorm';
import { CreateSettleDto } from './dto/create-settle.dto';

@Injectable()
export class SettleService {
  private readonly logger = new Logger(SettleService.name);
  constructor(
    @InjectEntityManager() private readonly manager: EntityManager,
    private readonly walletService: WalletService,
  ) {}

  async set(createSetDto: CreateSettleDto): Promise<{
    balance?: number;
    casinoTransactionId?: string;
    reconcileAmount?: number;
  }> {
    try {
      this.logger.log(
        `[Inicio] Create settle: ${JSON.stringify(createSetDto)}`,
      );
      const { amount, playerId, roundEnded, roundId, gameId, sessionId } =
        createSetDto;

      const transaction = await this.manager.findOne(CasinoTransactionEntity, {
        select: ['partnerId'],
        where: { roundId: roundId, sessionCasinoId: sessionId },
      });

      if (!transaction) {
        this.logger.error(`Bet not found`);
        throw new HttpException('Bet não encontrado', HttpStatus.NOT_FOUND);
      }
      await this.manager.update(
        CasinoTransactionEntity,
        {
          roundId: roundId,
          sessionCasinoId: sessionId,
        },
        {
          // transactionSettleId: transactionId,
          partnerId: transaction.partnerId,
          playerId: playerId,
          gameId: gameId.toString(),
          // requestType: requestType,
          roundId: roundId,
          winAmount: amount,
          roundEnded: roundEnded,
        },
      );

      if (amount > 0) {
        const walletResult = await this.walletService.credit(
          {
            amount: amount,
            reason: 'Set',
            isBonusOperation: false,
            gameId: gameId.toString(),
          },
          transaction.partnerId,
          playerId,
        );
        this.logger.log(
          `[Fim] Create settle amount > 0: ${JSON.stringify(createSetDto)}`,
        );
        return {
          balance: walletResult.subAccount.balance,
        };
      } else {
        const walletResult = await this.walletService.getBalanceByPlayer(
          transaction.partnerId,
          playerId,
        );
        this.logger.log(
          `[Fim] Create settle amount < 0: ${JSON.stringify(createSetDto)}`,
        );
        return {
          balance: walletResult.balance,
        };
      }
    } catch (error) {
      this.logger.error(`Error creating settle: ${error}`);
      throw new HttpException(
        'Erro ao liquidar aposta.',
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
