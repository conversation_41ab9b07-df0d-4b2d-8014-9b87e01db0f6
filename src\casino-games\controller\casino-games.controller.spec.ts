import { Test, TestingModule } from '@nestjs/testing';
import { CasinoGamesController } from './casino-games.controller';
import { CasinoGamesService } from '../services/casino-games.service';

describe('CasinoGamesController', () => {
  let controller: CasinoGamesController;

  const mockCasinoGamesService = {};

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CasinoGamesController],
      providers: [
        { provide: CasinoGamesService, useValue: mockCasinoGamesService },
      ],
    }).compile();

    controller = module.get<CasinoGamesController>(CasinoGamesController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
}); 