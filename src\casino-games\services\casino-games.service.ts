import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { GenericFilter } from '../../common/filters/generic-filter.dto';
import { SortOrder } from '../../common/filters/sortOrder';
import { CasinoGameProvider } from '../entities/casino-game-provider.entity';
import { CasinoGames } from '../entities/casino-game.entity';
import { CasinoGamesCategories } from '../entities/casino-games-categories.entity';

@Injectable()
export class CasinoGamesService {
  private readonly logger = new Logger(CasinoGamesService.name);
  constructor(
    @InjectEntityManager() private readonly casinoManager: EntityManager,
  ) {}

  async findAll(
    filter: GenericFilter,
    _req,
  ): Promise<{
    data: CasinoGames[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    try {
      this.logger.log(
        `[Inicio] Find all casino games: ${JSON.stringify(filter)}`,
      );
      // const partnerId = req.headers['partner-id'];
      if (!filter.sortOrder) {
        filter.sortOrder = SortOrder.ASC;
      }
      const page = !filter.page || filter.page < 1 ? 1 : filter.page;
      const pageSize =
        !filter.pageSize || filter.pageSize < 1 ? 10 : filter.pageSize;
      const totalItems = await this.casinoManager.count(CasinoGames, {
        where: { isDisable: false },
      });

      const totalPages = Math.ceil(totalItems / pageSize);
      const casinoListGames = await this.casinoManager
        .createQueryBuilder(CasinoGames, 'casino_games')
        .select('casino_games.id', 'id')
        .addSelect('casino_games.name', 'name')
        .addSelect('casino_games.description', 'description')
        .addSelect('casino_games.gameId', 'gameId')
        .addSelect('casino_games.gameProvider', 'gameProvider')
        .addSelect('casino_games.gameCategoryId', 'gameCategoryId')
        .addSelect('casino_games.gameProviderId', 'gameProviderId')
        .addSelect('casino_game_providers.name', 'gameProviderName')
        .addSelect('casino_game_categories.name', 'gameCategoryName')
        .leftJoin(
          CasinoGameProvider,
          'casino_game_providers',
          'casino_games.game_provider_id = casino_game_providers.id',
        )
        .leftJoin(
          CasinoGamesCategories,
          'casino_game_categories',
          'casino_games.game_category_id = casino_game_categories.id',
        )
        .where('casino_games.is_disabled = :isDisable', { isDisable: false })
        .skip((page - 1) * pageSize)
        .take(pageSize)
        .orderBy('casino_games.name', filter.sortOrder)
        .getRawMany();
      if (casinoListGames.length == 0) {
        this.logger.warn(`No records found`);
        throw new HttpException('No records found', HttpStatus.NO_CONTENT);
      }
      this.logger.log(`[Fim] Find all casino games: ${JSON.stringify(filter)}`);
      const result = {
        data: casinoListGames,
        totalItems,
        totalPages,
        currentPage: page,
        pageSize,
      };
      return result;
    } catch (err) {
      this.logger.error(`Error finding casino games: ${err}`);
      throw new HttpException(err.message, err.status);
    }
  }

  async findOne(id: string): Promise<CasinoGames> {
    try {
      this.logger.log(`[Inicio] Find one casino game: ${id}`);
      const findOneCasinoGame = await this.casinoManager
        .createQueryBuilder(CasinoGames, 'casino_games')
        .select('casino_games.id', 'id')
        .addSelect('casino_games.name', 'name')
        .addSelect('casino_games.description', 'description')
        .addSelect('casino_games.gameId', 'gameId')
        .addSelect('casino_games.gameProvider', 'gameProvider')
        .addSelect('casino_games.gameCategoryId', 'gameCategoryId')
        .addSelect('casino_games.gameProviderId', 'gameProviderId')
        .addSelect('casino_game_providers.name', 'gameProviderName')
        .addSelect('casino_game_categories.name', 'gameCategoryName')
        .leftJoin(
          CasinoGameProvider,
          'casino_game_providers',
          'casino_games.game_provider_id = casino_game_providers.id',
        )
        .leftJoin(
          CasinoGamesCategories,
          'casino_game_categories',
          'casino_games.game_category_id = casino_game_categories.id',
        )
        .where('casino_games.id = :id', { id })
        .getRawOne();
      if (!findOneCasinoGame) {
        this.logger.warn(`No records found`);
        throw new HttpException('No records found', HttpStatus.NO_CONTENT);
      }
      this.logger.log(`[Fim] Find one casino game: ${id}`);
      return findOneCasinoGame;
    } catch (err) {
      this.logger.error(`Error finding casino game: ${err}`);
      throw new HttpException(err.message, err.status);
    }
  }
}
