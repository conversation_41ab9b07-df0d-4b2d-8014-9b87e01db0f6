import { ApiProperty } from '@nestjs/swagger';

export class GameLaunchResponseDTO {
  @ApiProperty({
    description: 'Token de lançamento',
    example: '12345',
    required: true,
  })
  launchToken: string;

  @ApiProperty({
    description: 'URL de lançamento',
    example: 'https://www.google.com',
    required: true,
  })
  launchUrl: string;
}

export const GameLaunchResponseDTOExample = {
  launchToken: '12345',
  launchUrl: 'https://www.google.com',
};
