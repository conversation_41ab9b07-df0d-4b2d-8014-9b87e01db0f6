import {
  ALREADY_PROCESSED,
  GAME_NOT_FOUND,
  GENERIC_ERROR,
  PLAYER_NOT_FOUND,
  ROUND_NOT_FOUND,
  SESSION_NOT_FOUND,
  SUCCESS,
} from '@/common/constants/message-codes';
import { SwaggerApiCodeResponses } from '@/common/decorators/swagger-api-code-responses';
import {
  ApiDateRangeQuery,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiPaginationQuery,
  ApiPartnerIdHeader,
} from '@/common/decorators/swagger.decorator';
import {
  PaginatedResponseDtoExample,
  PaginationResponseDto,
} from '@/common/dto/paginated-response.dto';
import { GenericFilter } from '@/common/filters/generic-filter.dto';
import { KeycloakBackofficeGuard } from '@/common/guards/keycloak/keycloak-backoffice.guard';
import { Controller, Get, Param, Query, Req, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { CasinoTransactionService } from '../services/casino-transaction.service';
import {
  CasinoTransactionAvgBetResponseDto,
  CasinoTransactionAvgBetResponseDtoExample,
} from './dto/casino-transaction-avgBet-response.dto';
import {
  CasinoTransactionBetCountResponseDto,
  CasinoTransactionBetCountResponseDtoExample,
} from './dto/casino-transaction-betCount-response.dto';
import {
  CasinoTransactionBetProfitableResponseDto,
  CasinoTransactionBetProfitableResponseDtoExample,
} from './dto/casino-transaction-betProfitable-response.dto';
import {
  CasinoTransactionBetTransactionResponseDto,
  CasinoTransactionBetTransactionResponseDtoExample,
} from './dto/casino-transaction-betTransaction-response.dto';
import { CasinoTransactionFilterDto } from './dto/casino-transaction-filter.dto';
import {
  CasinoTransactionKpisResponseDto,
  CasinoTransactionKpisResponseDtoExample,
} from './dto/casino-transaction-kpis-response.dto';
import {
  CasinoTransactionResponseDto,
  CasinoTransactionResponseDtoExample,
} from './dto/casino-transaction-response.dto';

@ApiTags('CasinoTransaction')
@ApiPartnerIdHeader()
@ApiForbiddenResponse()
@ApiInternalServerErrorResponse()
@Controller('casino-transaction')
@UseGuards(KeycloakBackofficeGuard)
@ApiBearerAuth('access-token')
export class CasinoTransactionController {
  constructor(
    private readonly casinoTransactionService: CasinoTransactionService,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Listar transações',
    description:
      'Endpoint para listar todas as transações de casino com paginação',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    PaginationResponseDto<CasinoTransactionResponseDto>,
    PaginatedResponseDtoExample(CasinoTransactionResponseDtoExample),
  )
  @ApiPaginationQuery()
  @ApiQuery({
    name: 'partners',
    required: false,
    example: 'partnersId1,partnersId2',
    type: 'string',
  })
  @ApiQuery({
    name: 'playerId',
    required: false,
    example: 'playerid1',
    type: 'string',
  })
  @ApiQuery({
    name: 'gameId',
    required: false,
    example: 'gameid1',
    type: 'string',
  })
  @ApiQuery({
    name: 'cancelTransaction',
    required: false,
    example: true,
    type: 'boolean',
  })
  @ApiQuery({
    name: 'sessionId',
    required: false,
    example: true,
    type: 'boolean',
  })
  @ApiDateRangeQuery()
  findAll(
    @Query() genericFilter: GenericFilter & CasinoTransactionFilterDto,
    @Req() req,
  ) {
    return this.casinoTransactionService.findAll(genericFilter, req);
  }

  @Get(':id/id')
  @ApiOperation({
    summary: 'Buscar transações por jogador',
    description:
      'Endpoint para buscar transações de um jogador específico com paginação',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    PaginationResponseDto<CasinoTransactionResponseDto>,
    PaginatedResponseDtoExample(CasinoTransactionResponseDtoExample),
  )
  @ApiPaginationQuery()
  @ApiQuery({
    name: 'partners',
    required: false,
    example: 'partnersId1,partnersId2',
    type: 'string',
  })
  @ApiQuery({
    name: 'gameId',
    required: false,
    example: 'gameid1',
    type: 'string',
  })
  @ApiQuery({
    name: 'cancelTransaction',
    required: false,
    example: true,
    type: 'boolean',
  })
  @ApiQuery({
    name: 'sessionId',
    required: false,
    example: true,
    type: 'boolean',
  })
  @ApiDateRangeQuery()
  findOne(
    @Param('id') id: string,
    @Query() genericFilter: GenericFilter & CasinoTransactionFilterDto,
    @Req() req,
  ) {
    genericFilter.playerId = id;
    return this.casinoTransactionService.findAll(genericFilter, req);
  }

  @Get(':id/kpis')
  @ApiOperation({
    summary: 'Buscar KPIs do jogador',
    description: 'Endpoint para buscar indicadores de desempenho de um jogador',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    CasinoTransactionKpisResponseDto,
    CasinoTransactionKpisResponseDtoExample,
  )
  async findKpis(
    @Param('id') id: string,
    @Query() genericFilter: GenericFilter & CasinoTransactionFilterDto,
    @Req() req,
  ) {
    genericFilter.playerId = id;
    return this.casinoTransactionService.findKpisPlayer(genericFilter, req);
  }

  @Get('avg-bet')
  @ApiOperation({
    summary: 'Buscar média de apostas',
    description: 'Endpoint para buscar a média de apostas por jogador',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    CasinoTransactionAvgBetResponseDto,
    CasinoTransactionAvgBetResponseDtoExample,
  )
  @ApiDateRangeQuery()
  async findAvgBet(
    @Query() filter: GenericFilter & { fromDate: string; toDate: string },
    @Req() req,
  ) {
    return this.casinoTransactionService.findAvgBet(filter, req);
  }

  @Get('bet/count/player')
  @ApiOperation({
    summary: 'Contar apostas por jogador',
    description: 'Endpoint para contar o número de apostas por jogador',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    CasinoTransactionBetCountResponseDto,
    CasinoTransactionBetCountResponseDtoExample,
  )
  @ApiDateRangeQuery()
  async findBetCount(
    @Query() filter: GenericFilter & { fromDate: string; toDate: string },
    @Req() req,
  ) {
    return this.casinoTransactionService.findBetCount(filter, req);
  }

  @Get('bet/profitable/casino')
  @ApiOperation({
    summary: 'Buscar apostas lucrativas',
    description: 'Endpoint para buscar as apostas mais lucrativas por casino',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    CasinoTransactionBetProfitableResponseDto,
    CasinoTransactionBetProfitableResponseDtoExample,
  )
  @ApiDateRangeQuery()
  async findBetProfitable(
    @Query() filter: GenericFilter & { fromDate: string; toDate: string },
    @Req() req,
  ) {
    return this.casinoTransactionService.findBetProfitable(filter, req);
  }

  @Get('bet/transaction')
  @ApiOperation({
    summary: 'Buscar transações de apostas',
    description: 'Endpoint para buscar estatísticas de transações de apostas',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    CasinoTransactionBetTransactionResponseDto,
    CasinoTransactionBetTransactionResponseDtoExample,
  )
  @ApiDateRangeQuery()
  async findBetTransaction(
    @Query() filter: GenericFilter & { fromDate: string; toDate: string },
    @Req() req,
  ) {
    return this.casinoTransactionService.findBetTransaction(filter, req);
  }
}
