import { ApiProperty } from '@nestjs/swagger';

export class CasinoTransactionBetProfitableResponseDto {
  @ApiProperty({ description: 'Top 3 jogos mais lucrativos' })
  top3Games: {
    casino: string;
    totalBets: number;
    earnings: number;
    profit: number;
    percentageContribution: number;
  }[];

  @ApiProperty({ description: 'Top 3 jogos menos lucrativos' })
  bottom3Games: {
    casino: string;
    totalBets: number;
    earnings: number;
    profit: number;
    percentageContribution: number;
  }[];
}

export const CasinoTransactionBetProfitableResponseDtoExample = {
  top3Games: [
    {
      casino: 'Casino A',
      totalBets: 100,
      earnings: 1000,
      profit: 500,
      percentageContribution: 40,
    },
  ],
  bottom3Games: [
    {
      casino: 'Casino B',
      totalBets: 80,
      earnings: 800,
      profit: -400,
      percentageContribution: -40,
    },
  ],
};
