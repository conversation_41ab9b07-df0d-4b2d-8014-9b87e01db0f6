import { Controller, Post, Body } from '@nestjs/common';
import { JackpotService } from './jackpot.service';
import { FeedJackpotDto } from './dto/feed-jackpot.dto';
import { ResponseJackpotFeedDto } from './dto/response-feed-jackpot.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
@Controller('jackpot')
@ApiTags('Jackpot')
export class JackpotController {
  constructor(private readonly jackpotService: JackpotService) {}

  @Post('feed')
  @ApiOperation({
    summary: 'Feed de Jackpot',
    description: 'Endpoint para obter informações de jackpot',
  })
  @ApiResponse({
    status: 200,
    description: 'Informações de jackpot retornadas com sucesso',
    type: ResponseJackpotFeedDto,
  })
  async feed(
    @Body() feedJackpotDto: FeedJackpotDto,
  ): Promise<ResponseJackpotFeedDto> {
    return this.jackpotService.feed(feedJackpotDto);
  }
}
