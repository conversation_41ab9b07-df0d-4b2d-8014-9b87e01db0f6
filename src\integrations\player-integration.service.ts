import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';

export interface ContactInfo {
  id: string;
  playerId: string;
  email?: string | null;
  phone?: string | null;
  mobile?: string | null;
  regionId?: string | null;
  region?: {
    id: string;
    name: string;
    country?: string | null;
    code?: string | null;
  } | null;
  zipCode?: string | null;
  city?: string | null;
  address?: string | null;
  state?: string | null;
  createdAt?: Date;
  updatedAt?: Date | null;
}

export interface AccountInfo {
  id: string;
  playerId: string;
  partnerId?: string | null;
  externalId?: string | null;
  status?: boolean | null;
  isVerified?: boolean | null;
  btag?: string | null;
  createdAt?: Date;
  updatedAt?: Date | null;
}

@Injectable()
export class PlayerIntegrationService {
  private readonly logger = new Logger(PlayerIntegrationService.name);
  private readonly BATCH_SIZE = 100; // Limite do endpoint batch
  private readonly apiPlayerUrl: string;

  constructor(private readonly httpService: HttpService) {
    this.apiPlayerUrl = process.env.API_PLAYER || 'http://localhost:3002';
    this.logger.log(
      `PlayerIntegrationService initialized with API_PLAYER: ${this.apiPlayerUrl}`,
    );
  }

  /**
   * Busca contact info de múltiplos players por seus IDs via API do api-pam-player
   * @param playerIds Array de IDs dos players a serem buscados
   * @returns Promise com Map de playerId -> ContactInfo para lookup rápido
   */
  async getContactInfoBatch(
    playerIds: string[],
  ): Promise<Map<string, ContactInfo>> {
    try {
      this.logger.log(
        `[Start] Getting contact info by player ids: ${playerIds.length} player IDs`,
      );

      if (!playerIds || playerIds.length === 0) {
        this.logger.warn(`Empty array of player IDs provided`);
        return new Map();
      }

      // Remove IDs duplicados
      const uniquePlayerIds = [...new Set(playerIds)];
      this.logger.log(
        `[Processing] ${uniquePlayerIds.length} unique player IDs`,
      );

      // Dividir em chunks de 100 (limite do endpoint batch)
      const chunks: string[][] = [];
      for (let i = 0; i < uniquePlayerIds.length; i += this.BATCH_SIZE) {
        chunks.push(uniquePlayerIds.slice(i, i + this.BATCH_SIZE));
      }

      this.logger.log(`[Processing] Split into ${chunks.length} chunks`);

      // Fazer chamadas HTTP em paralelo para cada chunk
      const promises = chunks.map(
        (chunk, index) => this.fetchContactInfoChunk(chunk, index, true), // includeRegion = true
      );

      const results = await Promise.all(promises);

      // Combinar todos os resultados em um Map
      const contactInfoMap = new Map<string, ContactInfo>();
      results.forEach(contactInfos => {
        contactInfos.forEach(contactInfo => {
          contactInfoMap.set(contactInfo.playerId, contactInfo);
        });
      });

      this.logger.log(
        `[End] Retrieved ${contactInfoMap.size} contact infos out of ${uniquePlayerIds.length} requested`,
      );

      return contactInfoMap;
    } catch (error) {
      this.logger.error(
        `[Error] Failed to get contact info by player ids: ${error.message}`,
      );
      // Por enquanto, retornar Map vazio em caso de erro
      return new Map();
    }
  }

  /**
   * Busca account info de múltiplos players por seus IDs via API do api-pam-player
   * @param playerIds Array de IDs dos players a serem buscados
   * @returns Promise com Map de playerId -> AccountInfo para lookup rápido
   */
  async getAccountInfoBatch(
    playerIds: string[],
  ): Promise<Map<string, AccountInfo>> {
    try {
      this.logger.log(
        `[Start] Getting account info by player ids: ${playerIds.length} player IDs`,
      );

      if (!playerIds || playerIds.length === 0) {
        this.logger.warn(`Empty array of player IDs provided`);
        return new Map();
      }

      // Remove IDs duplicados
      const uniquePlayerIds = [...new Set(playerIds)];
      this.logger.log(
        `[Processing] ${uniquePlayerIds.length} unique player IDs`,
      );

      // Dividir em chunks de 100 (limite do endpoint batch)
      const chunks: string[][] = [];
      for (let i = 0; i < uniquePlayerIds.length; i += this.BATCH_SIZE) {
        chunks.push(uniquePlayerIds.slice(i, i + this.BATCH_SIZE));
      }

      this.logger.log(`[Processing] Split into ${chunks.length} chunks`);

      // Fazer chamadas HTTP em paralelo para cada chunk
      const promises = chunks.map((chunk, index) =>
        this.fetchAccountInfoChunk(chunk, index),
      );

      const results = await Promise.all(promises);

      // Combinar todos os resultados em um Map
      const accountInfoMap = new Map<string, AccountInfo>();
      results.forEach(accountInfos => {
        accountInfos.forEach(accountInfo => {
          accountInfoMap.set(accountInfo.playerId, accountInfo);
        });
      });

      this.logger.log(
        `[End] Retrieved ${accountInfoMap.size} account infos out of ${uniquePlayerIds.length} requested`,
      );

      return accountInfoMap;
    } catch (error) {
      this.logger.error(
        `[Error] Failed to get account info by player ids: ${error.message}`,
      );
      // Por enquanto, retornar Map vazio em caso de erro
      return new Map();
    }
  }

  /**
   * Busca um chunk de contact info via HTTP
   */
  private async fetchContactInfoChunk(
    playerIds: string[],
    chunkIndex: number,
    includeRegion: boolean = true,
  ): Promise<ContactInfo[]> {
    try {
      this.logger.log(
        `[Fetching] Contact info chunk ${chunkIndex + 1}: ${playerIds.length} player IDs`,
      );

      const url = `${this.apiPlayerUrl}/v1/pam/player/backoffice/contact-info/batch?includeRegion=${includeRegion}`;
      const response = await firstValueFrom(
        this.httpService.post<ContactInfo[]>(
          url,
          { playerIds },
          {
            timeout: 5000,
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      const contactInfos = response.data || [];
      this.logger.log(
        `[Received] Contact info chunk ${chunkIndex + 1}: ${contactInfos.length} contact infos`,
      );

      return contactInfos;
    } catch (error) {
      this.logger.error(
        `[Error] Failed to fetch contact info chunk ${chunkIndex + 1}: ${error.message}`,
      );
      return [];
    }
  }

  /**
   * Busca um chunk de account info via HTTP
   */
  private async fetchAccountInfoChunk(
    playerIds: string[],
    chunkIndex: number,
  ): Promise<AccountInfo[]> {
    try {
      this.logger.log(
        `[Fetching] Account info chunk ${chunkIndex + 1}: ${playerIds.length} player IDs`,
      );

      const url = `${this.apiPlayerUrl}/v1/pam/player/backoffice/account-info/batch`;
      const response = await firstValueFrom(
        this.httpService.post<AccountInfo[]>(
          url,
          { playerIds },
          {
            timeout: 5000,
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      const accountInfos = response.data || [];
      this.logger.log(
        `[Received] Account info chunk ${chunkIndex + 1}: ${accountInfos.length} account infos`,
      );

      return accountInfos;
    } catch (error) {
      this.logger.error(
        `[Error] Failed to fetch account info chunk ${chunkIndex + 1}: ${error.message}`,
      );
      return [];
    }
  }
}
