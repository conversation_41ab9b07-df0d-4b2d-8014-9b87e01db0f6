import { Controller, Post, Body } from '@nestjs/common';
import { FreespinService } from './freespin.service';
import { FreeSpinsFinishDto } from './dto/finish-freespin.dto';
import { CancelFreeSpinDto } from './dto/cancel-freespin.dto';
import { IssueFreeSpinDto } from './dto/issue-freespin.dto';
import { ResponseFinishFreespinDto } from './dto/response-finish-freespin.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@Controller('freespin')
@ApiTags('Freespin')
export class FreespinController {
  constructor(private readonly freespinService: FreespinService) {}

  @Post('finish')
  @ApiOperation({
    summary: 'Finalizar Free Spins',
    description: 'Endpoint para finalizar free spins e processar ganhos',
  })
  @ApiResponse({
    status: 200,
    description: 'Free spins finalizados com sucesso',
    type: ResponseFinishFreespinDto,
  })
  async finish(
    @Body() finishFreespinDto: FreeSpinsFinishDto,
  ): Promise<ResponseFinishFreespinDto> {
    return this.freespinService.finish(finishFreespinDto);
  }

  @Post('issue')
  @ApiOperation({
    summary: 'Emitir Free Spins',
    description: 'Endpoint para emitir free spins para um jogador',
  })
  @ApiResponse({
    status: 200,
    description: 'Free spins emitidos com sucesso',
  })
  async issue(@Body() createFreespinDto: IssueFreeSpinDto): Promise<any> {
    return this.freespinService.issue(createFreespinDto);
  }

  @Post('cancel')
  @ApiOperation({
    summary: 'Cancelar Free Spins',
    description: 'Endpoint para cancelar free spins',
  })
  @ApiResponse({
    status: 200,
    description: 'Free spins cancelados com sucesso',
  })
  async cancel(@Body() cancelFreespinDto: CancelFreeSpinDto): Promise<any> {
    return this.freespinService.cancel(cancelFreespinDto);
  }
}
