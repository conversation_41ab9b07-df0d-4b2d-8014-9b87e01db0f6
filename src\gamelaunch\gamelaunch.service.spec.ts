import { Test, TestingModule } from '@nestjs/testing';
import { GameLaunchService } from './gamelaunch.service';
import { EntityManager } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { HMACService } from '../hmac/service/hmac.service';
import { WalletService } from '../wallet/wallet.service';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { GameModeEnum } from './enum/game-mode.enum';


// Mock uuid
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'mocked-uuid-v4'),
}));

jest.mock('rxjs', () => ({
  ...jest.requireActual('rxjs'),
  firstValueFrom: jest.fn(),
}));

describe('GameLaunchService', () => {
  let service: GameLaunchService;
  let entityManager: jest.Mocked<EntityManager>;
  let jwtService: jest.Mocked<JwtService>;
  let hmacService: jest.Mocked<HMACService>;
  let walletService: jest.Mocked<WalletService>;
  let httpService: jest.Mocked<HttpService>;

  const mockEntityManager = {
    findOneBy: jest.fn(),
    findOne: jest.fn(),
    insert: jest.fn().mockReturnValue({
      catch: jest.fn().mockReturnThis(),
    }),
  };
  const mockJwtService = { decode: jest.fn() };
  const mockHMACService = { generateHMAC: jest.fn() };
  const mockWalletService = {
    getBalanceByPlayer: jest.fn(),
    limit: jest.fn()
  };
  const mockHttpService = { post: jest.fn() };

  beforeEach(async () => {
    (firstValueFrom as jest.Mock).mockReset();
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GameLaunchService,
        { provide: EntityManager, useValue: mockEntityManager },
        { provide: JwtService, useValue: mockJwtService },
        { provide: HMACService, useValue: mockHMACService },
        { provide: WalletService, useValue: mockWalletService },
        { provide: HttpService, useValue: mockHttpService },
      ],
    }).compile();

    service = module.get<GameLaunchService>(GameLaunchService);
    entityManager = module.get(EntityManager);
    jwtService = module.get(JwtService);
    hmacService = module.get(HMACService);
    walletService = module.get(WalletService);
    httpService = module.get(HttpService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getBalance', () => {
    const mockSubAccount = {
      id: 'sub-account-id',
      accountType: 'regular',
      balance: 100,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      deletedAt: null,
    };

    it('deve retornar o balance do wallet com sucesso', async () => {
      walletService.getBalanceByPlayer.mockResolvedValue(mockSubAccount);
      await expect(service.getBalance({ player_id: 'p1', partner_id: 'pt1' })).resolves.toEqual(mockSubAccount);
    });

    it('deve lançar erro se o walletService lançar erro', () => {
      walletService.getBalanceByPlayer.mockImplementation(() => { throw new Error('Erro'); });
      expect(() => service.getBalance({ player_id: 'p1', partner_id: 'pt1' })).toThrow('Error making GET request: Erro');
    });
  });

  describe('getContentFromToken', () => {
    it('deve decodificar o token com sucesso', () => {
      jwtService.decode.mockReturnValue({ payload: { id: 'p1' } });
      const result = service.getContentFromToken('Bearer token');
      expect(result).toEqual({ payload: { id: 'p1' } });
    });
    it('deve lançar erro se decode lançar erro', () => {
      jwtService.decode.mockImplementation(() => { throw new Error('decode error'); });
      expect(() => service.getContentFromToken('Bearer token')).toThrow('Error decoding AUTH TOKEN: decode error');
    });
  });

  describe('getURLFromAggregator', () => {
    it('deve retornar resposta do aggregator com sucesso', async () => {
      (firstValueFrom as jest.Mock).mockResolvedValue({ data: { launchUrl: 'url', launchToken: 'token' } });
      const aggregatorMock = {
        requestId: 'req-1',
        partnerCloseUrl: '',
        gameMode: GameModeEnum.REAL_PLAY,
        playerId: 'p1',
        playerCurrency: 'BRL',
        playerLanguage: 'pt',
        playerCountry: 'BR',
        playerBalance: '100',
      };
      const result = await service.getURLFromAggregator({ aggregator: aggregatorMock, hmac: 'hmac' });
      expect(result).toEqual({ data: { launchUrl: 'url', launchToken: 'token' } });
      expect(httpService.post).toHaveBeenCalled();
    });
    it('deve lançar erro se o aggregator lançar erro', async () => {
      (firstValueFrom as jest.Mock).mockRejectedValue(new Error('Aggregator error'));
      const aggregatorMock2 = {
        requestId: 'req-2',
        partnerCloseUrl: '',
        gameMode: GameModeEnum.REAL_PLAY,
        playerId: 'p2',
        playerCurrency: 'BRL',
        playerLanguage: 'pt',
        playerCountry: 'BR',
        playerBalance: '200',
      };
      await expect(service.getURLFromAggregator({ aggregator: aggregatorMock2, hmac: 'hmac' })).rejects.toThrow('Error making GET request: Aggregator error');
    });
  });

  describe('getURL', () => {
    const mockGameLaunchDTO = {
      gameId: 'test-game-id',
      gameMode: GameModeEnum.REAL_PLAY,
    };

    const mockJwtPayload = {
      payload: {
        id: 'player-123',
        partnerId: 'partner-456',
        language: 'pt',
        currency: 'BRL',
        country: 'BR',
      },
    };

    const mockCasinoGame = {
      id: 'casino-game-uuid',
      gameId: 'test-game-id',
      name: 'Test Game',
      gameProvider: 'Test Provider',
    };

    const mockAggregatorResponse = {
      data: {
        errorCode: 0,
        launchUrl: 'https://test-launch-url.com',
        launchToken: 'test-launch-token',
      },
    };

    beforeEach(() => {
      const mockSubAccount = {
        id: 'sub-account-id',
        accountType: 'regular',
        balance: 1000,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        deletedAt: null,
      };

      jwtService.decode.mockReturnValue(mockJwtPayload);
      walletService.getBalanceByPlayer.mockResolvedValue(mockSubAccount);
      walletService.limit.mockResolvedValue(undefined);
      entityManager.findOneBy.mockResolvedValue(mockCasinoGame);
      hmacService.generateHMAC.mockReturnValue('test-hmac');
      (firstValueFrom as jest.Mock).mockResolvedValue(mockAggregatorResponse);
    });

    it('should successfully launch a real-play game', async () => {
      // Act
      const result = await service.getURL(mockGameLaunchDTO, 'Bearer test-token');

      // Assert
      expect(result).toEqual({
        errorCode: 0,
        launchToken: 'test-launch-token',
        launchUrl: 'https://test-launch-url.com',
      });

      expect(jwtService.decode).toHaveBeenCalledWith('test-token');
      expect(walletService.getBalanceByPlayer).toHaveBeenCalledWith('partner-456', 'player-123');
      expect(walletService.limit).toHaveBeenCalledWith('player-123', 'partner-456');
      expect(entityManager.findOneBy).toHaveBeenCalledWith(expect.anything(), { gameId: 'test-game-id' });
      expect(hmacService.generateHMAC).toHaveBeenCalled();
      expect(entityManager.insert).toHaveBeenCalled();
    });

    it('should handle free-play mode with partnerCloseUrl', async () => {
      // Arrange
      const freePlayDTO = { ...mockGameLaunchDTO, gameMode: GameModeEnum.FREE_PLAY };

      // Act
      await service.getURL(freePlayDTO, 'Bearer test-token');

      // Assert
      expect(hmacService.generateHMAC).toHaveBeenCalledWith(
        expect.stringContaining('"partnerCloseUrl":"https://example.com/casino"')
      );
    });

    it('should handle aggregator error response', async () => {
      // Arrange
      const errorResponse = {
        data: {
          errorCode: 1001,
          errorMsg: 'Game not available',
        },
      };
      (firstValueFrom as jest.Mock).mockResolvedValue(errorResponse);

      // Act
      const result = await service.getURL(mockGameLaunchDTO, 'Bearer test-token');

      // Assert
      expect(result).toEqual({
        errorCode: 1001,
        errorMsg: 'Game not available',
      });
    });

    it('should handle JWT decode error', async () => {
      // Arrange
      jwtService.decode.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // Act & Assert
      await expect(
        service.getURL(mockGameLaunchDTO, 'Bearer invalid-token')
      ).rejects.toThrow('Error decoding AUTH TOKEN: Invalid token');
    });

    it('should handle wallet balance error', async () => {
      // Arrange
      walletService.getBalanceByPlayer.mockImplementation(() => {
        throw new Error('Wallet service error');
      });

      // Act & Assert
      await expect(
        service.getURL(mockGameLaunchDTO, 'Bearer test-token')
      ).rejects.toThrow('Error making GET request: Wallet service error');
    });

    it('should handle aggregator HTTP error', async () => {
      // Arrange
      (firstValueFrom as jest.Mock).mockRejectedValue(new Error('Network error'));

      // Act & Assert
      await expect(
        service.getURL(mockGameLaunchDTO, 'Bearer test-token')
      ).rejects.toThrow('Error making GET request: Network error');
    });

    it('should handle database insert error gracefully', async () => {
      // Arrange
      const insertError = new Error('Database error');
      mockEntityManager.insert.mockImplementation(() => ({
        catch: jest.fn().mockImplementation((callback) => callback(insertError)),
      }));

      // Act
      const result = await service.getURL(mockGameLaunchDTO, 'Bearer test-token');

      // Assert
      expect(result).toEqual({
        errorCode: 0,
        launchToken: 'test-launch-token',
        launchUrl: 'https://test-launch-url.com',
      });
    });

    it('should return undefined when aggregator response has no data', async () => {
      // Arrange
      (firstValueFrom as jest.Mock).mockResolvedValue({ data: null });

      // Act
      const result = await service.getURL(mockGameLaunchDTO, 'Bearer test-token');

      // Assert
      expect(result).toBeUndefined();
    });
  });

  describe('getFreeUrl', () => {
    const mockFreePlayDTO = {
      gameId: 'test-game-id',
      gameMode: GameModeEnum.FREE_PLAY,
    };

    const mockCasinoGame = {
      id: 'casino-game-uuid',
      gameId: 'test-game-id',
      name: 'Test Game',
      gameProvider: 'Test Provider',
    };

    const mockAggregatorResponse = {
      data: {
        errorCode: 0,
        launchUrl: 'https://test-free-launch-url.com',
        launchToken: 'test-free-launch-token',
      },
    };

    beforeEach(() => {
      entityManager.findOneBy.mockResolvedValue(mockCasinoGame);
      hmacService.generateHMAC.mockReturnValue('test-hmac');
      (firstValueFrom as jest.Mock).mockResolvedValue(mockAggregatorResponse);
    });

    it('should successfully launch a free-play game', async () => {
      // Act
      const result = await service.getFreeUrl(mockFreePlayDTO);

      // Assert
      expect(result).toEqual({
        errorCode: 0,
        launchToken: 'test-free-launch-token',
        launchUrl: 'https://test-free-launch-url.com',
      });

      expect(entityManager.findOneBy).toHaveBeenCalledWith(expect.anything(), { gameId: 'test-game-id' });
      expect(hmacService.generateHMAC).toHaveBeenCalled();
      expect(entityManager.insert).toHaveBeenCalled();
    });

    it('should use default values for free-play mode', async () => {
      // Act
      await service.getFreeUrl(mockFreePlayDTO);

      // Assert
      expect(hmacService.generateHMAC).toHaveBeenCalledWith(
        expect.stringContaining('"playerCurrency":"BRL"')
      );
      expect(hmacService.generateHMAC).toHaveBeenCalledWith(
        expect.stringContaining('"playerLanguage":"pt"')
      );
      expect(hmacService.generateHMAC).toHaveBeenCalledWith(
        expect.stringContaining('"playerCountry":"BR"')
      );
      expect(hmacService.generateHMAC).toHaveBeenCalledWith(
        expect.stringContaining('"playerBalance":"1000.00"')
      );
    });

    it('should remove playerId for free-play mode', async () => {
      // Act
      await service.getFreeUrl(mockFreePlayDTO);

      // Assert
      expect(hmacService.generateHMAC).toHaveBeenCalledWith(
        expect.not.stringContaining('"playerId"')
      );
    });

    it('should handle aggregator error response in free-play', async () => {
      // Arrange
      const errorResponse = {
        data: {
          errorCode: 2001,
          errorMsg: 'Free-play not available',
        },
      };
      (firstValueFrom as jest.Mock).mockResolvedValue(errorResponse);

      // Act
      const result = await service.getFreeUrl(mockFreePlayDTO);

      // Assert
      expect(result).toEqual({
        errorCode: 2001,
        errorMsg: 'Free-play not available',
      });
    });

    it('should handle aggregator HTTP error in free-play', async () => {
      // Arrange
      (firstValueFrom as jest.Mock).mockRejectedValue(new Error('Network error'));

      // Act & Assert
      await expect(
        service.getFreeUrl(mockFreePlayDTO)
      ).rejects.toThrow('Error making GET request: Network error');
    });

    it('should save session with empty player and partner IDs for free-play', async () => {
      // Act
      await service.getFreeUrl(mockFreePlayDTO);

      // Assert
      expect(entityManager.insert).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          playerId: '',
          partnerId: '',
          gameMode: GameModeEnum.FREE_PLAY,
        })
      );
    });

    it('should handle database insert error gracefully in free-play', async () => {
      // Arrange
      const insertError = new Error('Database error');
      mockEntityManager.insert.mockImplementation(() => ({
        catch: jest.fn().mockImplementation((callback) => callback(insertError)),
      }));

      // Act
      const result = await service.getFreeUrl(mockFreePlayDTO);

      // Assert
      expect(result).toEqual({
        errorCode: 0,
        launchToken: 'test-free-launch-token',
        launchUrl: 'https://test-free-launch-url.com',
      });
    });

    it('should return undefined when aggregator response has no data in free-play', async () => {
      // Arrange
      (firstValueFrom as jest.Mock).mockResolvedValue({ data: null });

      // Act
      const result = await service.getFreeUrl(mockFreePlayDTO);

      // Assert
      expect(result).toBeUndefined();
    });
  });

  describe('Integration tests', () => {
    it('should handle complete flow for real-play game launch', async () => {
      // Arrange
      const mockGameLaunchDTO = {
        gameId: 'integration-game-id',
        gameMode: GameModeEnum.REAL_PLAY,
      };

      const mockJwtPayload = {
        payload: {
          id: 'integration-player-123',
          partnerId: 'integration-partner-456',
          language: 'en',
          currency: 'USD',
          country: 'US',
        },
      };

      const mockCasinoGame = {
        id: 'integration-casino-game-uuid',
        gameId: 'integration-game-id',
        name: 'Integration Test Game',
        gameProvider: 'Integration Provider',
      };

      const mockAggregatorResponse = {
        data: {
          errorCode: 0,
          launchUrl: 'https://integration-launch-url.com',
          launchToken: 'integration-launch-token',
        },
      };

      const mockSubAccount = {
        id: 'integration-sub-account-id',
        accountType: 'regular',
        balance: 5000,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        deletedAt: null,
      };

      jwtService.decode.mockReturnValue(mockJwtPayload);
      walletService.getBalanceByPlayer.mockResolvedValue(mockSubAccount);
      walletService.limit.mockResolvedValue(undefined);
      entityManager.findOneBy.mockResolvedValue(mockCasinoGame);
      hmacService.generateHMAC.mockReturnValue('integration-hmac');
      (firstValueFrom as jest.Mock).mockResolvedValue(mockAggregatorResponse);

      // Act
      const result = await service.getURL(mockGameLaunchDTO, 'Bearer integration-token');

      // Assert
      expect(result).toEqual({
        errorCode: 0,
        launchToken: 'integration-launch-token',
        launchUrl: 'https://integration-launch-url.com',
      });

      // Verify all services were called in correct order
      expect(jwtService.decode).toHaveBeenCalledWith('integration-token');
      expect(walletService.getBalanceByPlayer).toHaveBeenCalledWith('integration-partner-456', 'integration-player-123');
      expect(walletService.limit).toHaveBeenCalledWith('integration-player-123', 'integration-partner-456');
      expect(entityManager.findOneBy).toHaveBeenCalledWith(expect.anything(), { gameId: 'integration-game-id' });
      expect(hmacService.generateHMAC).toHaveBeenCalled();
      expect(entityManager.insert).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          gameId: 'integration-game-id',
          playerId: 'integration-player-123',
          partnerId: 'integration-partner-456',
          launchToken: 'integration-launch-token',
          launchUrl: 'https://integration-launch-url.com',
          gameMode: GameModeEnum.REAL_PLAY,
        })
      );
    });

    it('should handle complete flow for free-play game launch', async () => {
      // Arrange
      const mockFreePlayDTO = {
        gameId: 'free-integration-game-id',
        gameMode: GameModeEnum.FREE_PLAY,
      };

      const mockCasinoGame = {
        id: 'free-integration-casino-game-uuid',
        gameId: 'free-integration-game-id',
        name: 'Free Integration Test Game',
        gameProvider: 'Free Integration Provider',
      };

      const mockAggregatorResponse = {
        data: {
          errorCode: 0,
          launchUrl: 'https://free-integration-launch-url.com',
          launchToken: 'free-integration-launch-token',
        },
      };

      entityManager.findOneBy.mockResolvedValue(mockCasinoGame);
      hmacService.generateHMAC.mockReturnValue('free-integration-hmac');
      (firstValueFrom as jest.Mock).mockResolvedValue(mockAggregatorResponse);

      // Act
      const result = await service.getFreeUrl(mockFreePlayDTO);

      // Assert
      expect(result).toEqual({
        errorCode: 0,
        launchToken: 'free-integration-launch-token',
        launchUrl: 'https://free-integration-launch-url.com',
      });

      // Verify services were called correctly
      expect(entityManager.findOneBy).toHaveBeenCalledWith(expect.anything(), { gameId: 'free-integration-game-id' });
      expect(hmacService.generateHMAC).toHaveBeenCalled();
      expect(entityManager.insert).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          gameId: 'free-integration-game-id',
          playerId: '',
          partnerId: '',
          launchToken: 'free-integration-launch-token',
          launchUrl: 'https://free-integration-launch-url.com',
          gameMode: GameModeEnum.FREE_PLAY,
        })
      );
    });
  });

  describe('Edge cases and error handling', () => {
    it('should handle missing environment variables gracefully', () => {
      // This test verifies that the service can be instantiated even if env vars are undefined
      expect(service).toBeDefined();
      // Environment variables might be undefined in test environment, which is expected
      expect(service).toHaveProperty('urlBaseWallwet');
      expect(service).toHaveProperty('urlBaseAggregator');
      expect(service).toHaveProperty('urlCashier');
    });

    it('should handle Bearer token with extra spaces', () => {
      // Arrange
      jwtService.decode.mockReturnValue({ payload: { id: 'test' } });

      // Act
      const result = service.getContentFromToken('Bearer   test-token-with-spaces   ');

      // Assert
      expect(jwtService.decode).toHaveBeenCalledWith('  test-token-with-spaces   ');
      expect(result).toEqual({ payload: { id: 'test' } });
    });

    it('should handle token without Bearer prefix', () => {
      // Arrange
      jwtService.decode.mockReturnValue({ payload: { id: 'test' } });

      // Act
      const result = service.getContentFromToken('raw-token');

      // Assert
      expect(jwtService.decode).toHaveBeenCalledWith('raw-token');
      expect(result).toEqual({ payload: { id: 'test' } });
    });
  });
});