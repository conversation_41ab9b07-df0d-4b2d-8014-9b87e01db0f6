import { Test, TestingModule } from '@nestjs/testing';
import { GameLaunchService } from './gamelaunch.service';
import { EntityManager } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { HMACService } from '../hmac/service/hmac.service';
import { WalletService } from '../wallet/wallet.service';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { GameModeEnum } from './enum/game-mode.enum';

jest.mock('rxjs', () => ({
  ...jest.requireActual('rxjs'),
  firstValueFrom: jest.fn(),
}));

describe('GameLaunchService', () => {
  let service: GameLaunchService;
  let entityManager: any;
  let jwtService: any;
  let hmacService: any;
  let walletService: any;
  let httpService: any;

  const mockEntityManager = {
    findOneBy: jest.fn(),
    findOne: jest.fn(),
    insert: jest.fn(),
  };
  const mockJwtService = { decode: jest.fn() };
  const mockHMACService = { generateHMAC: jest.fn() };
  const mockWalletService = { getBalanceByPlayer: jest.fn(), limit: jest.fn() };
  const mockHttpService = { post: jest.fn() };

  beforeEach(async () => {
    (firstValueFrom as jest.Mock).mockReset();
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GameLaunchService,
        { provide: EntityManager, useValue: mockEntityManager },
        { provide: JwtService, useValue: mockJwtService },
        { provide: HMACService, useValue: mockHMACService },
        { provide: WalletService, useValue: mockWalletService },
        { provide: HttpService, useValue: mockHttpService },
      ],
    }).compile();

    service = module.get<GameLaunchService>(GameLaunchService);
    entityManager = module.get<EntityManager>(EntityManager);
    jwtService = module.get<JwtService>(JwtService);
    hmacService = module.get<HMACService>(HMACService);
    walletService = module.get<WalletService>(WalletService);
    httpService = module.get<HttpService>(HttpService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getBalance', () => {
    it('deve retornar o balance do wallet com sucesso', async () => {
      walletService.getBalanceByPlayer.mockResolvedValue({ balance: 100 });
      await expect(service.getBalance({ player_id: 'p1', partner_id: 'pt1' })).resolves.toEqual({ balance: 100 });
    });
    it('deve lançar erro se o walletService lançar erro', async () => {
      walletService.getBalanceByPlayer.mockImplementation(() => { throw new Error('Erro'); });
      await expect(() => service.getBalance({ player_id: 'p1', partner_id: 'pt1' })).toThrow('Error making GET request: Erro');
    });
  });

  describe('getContentFromToken', () => {
    it('deve decodificar o token com sucesso', () => {
      jwtService.decode.mockReturnValue({ payload: { id: 'p1' } });
      const result = service.getContentFromToken('Bearer token');
      expect(result).toEqual({ payload: { id: 'p1' } });
    });
    it('deve lançar erro se decode lançar erro', () => {
      jwtService.decode.mockImplementation(() => { throw new Error('decode error'); });
      expect(() => service.getContentFromToken('Bearer token')).toThrow('Error decoding AUTH TOKEN: decode error');
    });
  });

  describe('getURLFromAggregator', () => {
    it('deve retornar resposta do aggregator com sucesso', async () => {
      (firstValueFrom as jest.Mock).mockResolvedValue({ data: { launchUrl: 'url', launchToken: 'token' } });
      const aggregatorMock = {
        requestId: 'req-1',
        partnerCloseUrl: '',
        gameMode: GameModeEnum.REAL_PLAY,
        playerId: 'p1',
        playerCurrency: 'BRL',
        playerLanguage: 'pt',
        playerCountry: 'BR',
        playerBalance: '100',
      };
      const result = await service.getURLFromAggregator({ aggregator: aggregatorMock, hmac: 'hmac' });
      expect(result).toEqual({ data: { launchUrl: 'url', launchToken: 'token' } });
      expect(httpService.post).toHaveBeenCalled();
    });
    it('deve lançar erro se o aggregator lançar erro', async () => {
      (firstValueFrom as jest.Mock).mockRejectedValue(new Error('Aggregator error'));
      const aggregatorMock2 = {
        requestId: 'req-2',
        partnerCloseUrl: '',
        gameMode: GameModeEnum.REAL_PLAY,
        playerId: 'p2',
        playerCurrency: 'BRL',
        playerLanguage: 'pt',
        playerCountry: 'BR',
        playerBalance: '200',
      };
      await expect(service.getURLFromAggregator({ aggregator: aggregatorMock2, hmac: 'hmac' })).rejects.toThrow('Error making GET request: Aggregator error');
    });
  });

  // Testes para getURL e getFreeUrl podem ser incrementados conforme necessidade, pois envolvem muitos mocks e fluxo complexo.
}); 