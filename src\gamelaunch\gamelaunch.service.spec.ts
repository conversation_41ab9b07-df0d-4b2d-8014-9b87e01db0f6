import { HttpService } from '@nestjs/axios';
import { JwtService } from '@nestjs/jwt';
import { Test, TestingModule } from '@nestjs/testing';
import { firstValueFrom } from 'rxjs';
import { EntityManager } from 'typeorm';
import { HMACService } from '../hmac/service/hmac.service';
import { PartnerService } from '../partner/partner.service';
import { PlayerService } from '../player/player.service';
import { WalletService } from '../wallet/wallet.service';
import { CreateLauncherDto } from './dto/game-launch.dto';
import { GameModeEnum } from './enum/game-mode.enum';
import { GameLaunchService } from './gamelaunch.service';

// Mock uuid
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'mocked-uuid-v4'),
}));

jest.mock('rxjs', () => ({
  ...jest.requireActual('rxjs'),
  firstValueFrom: jest.fn(),
}));

describe('GameLaunchService', () => {
  let service: GameLaunchService;
  let entityManager: jest.Mocked<EntityManager>;
  let jwtService: jest.Mocked<JwtService>;
  let hmacService: jest.Mocked<HMACService>;
  let walletService: jest.Mocked<WalletService>;
  let httpService: jest.Mocked<HttpService>;

  const mockEntityManager = {
    findOneBy: jest.fn(),
    findOne: jest.fn(),
    insert: jest.fn().mockResolvedValue({
      identifiers: [],
      generatedMaps: [],
      raw: [],
    }),
  };
  const mockJwtService = { decode: jest.fn() };
  const mockHMACService = { generateHMAC: jest.fn() };
  const mockWalletService = {
    getBalanceByPlayer: jest.fn(),
    limit: jest.fn(),
  };
  const mockHttpService = {
    post: jest.fn().mockReturnValue({
      pipe: jest.fn().mockReturnThis(),
    }),
  };

  const mockPlayerService = {
    getPlayerById: jest.fn(),
  };

  const mockPartnerService = {
    getPartnerById: jest.fn(),
  };

  beforeEach(async () => {
    (firstValueFrom as jest.Mock).mockReset();
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GameLaunchService,
        { provide: EntityManager, useValue: mockEntityManager },
        { provide: JwtService, useValue: mockJwtService },
        { provide: HMACService, useValue: mockHMACService },
        { provide: WalletService, useValue: mockWalletService },
        { provide: HttpService, useValue: mockHttpService },
        { provide: PlayerService, useValue: mockPlayerService },
        { provide: PartnerService, useValue: mockPartnerService },
      ],
    }).compile();

    service = module.get<GameLaunchService>(GameLaunchService);
    entityManager = module.get(EntityManager);
    jwtService = module.get(JwtService);
    hmacService = module.get(HMACService);
    walletService = module.get(WalletService);
    httpService = module.get(HttpService);

    // Reset all mocks before each test
    jest.clearAllMocks();

    // Setup default mocks for player and partner services
    mockPlayerService.getPlayerById.mockResolvedValue({
      id: 'player-123',
      accountInfo: { isVerified: true },
      capabilities: { canCasinoLogin: true },
      contactInfo: { email: '<EMAIL>' },
      personalInformation: {
        name: 'Test',
        lastName: 'Player',
        birthDate: '1990-01-01T00:00:00.000Z',
      },
      createdAt: '2023-01-01T00:00:00.000Z',
    });

    mockPartnerService.getPartnerById.mockResolvedValue({
      id: 'partner-123',
      name: 'Test Partner',
      country: { codeAlpha2: 'BR' },
      currency: { currencyCode: 'BRL' },
      jurisdiction: 'BR',
    });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getBalance', () => {
    it('deve retornar o balance do wallet com sucesso', () => {
      const result = service.getBalance({ player_id: 'p1', partner_id: 'pt1' });
      expect(result).toBe(1000); // O serviço retorna 1000 hardcoded
    });

    it('deve lançar erro se o walletService lançar erro', () => {
      // O serviço não lança erro atualmente, apenas retorna 1000
      // Mas vamos manter o teste caso o comportamento mude
      const result = service.getBalance({ player_id: 'p1', partner_id: 'pt1' });
      expect(result).toBe(1000);
    });
  });

  describe('getContentFromToken', () => {
    it('deve decodificar o token com sucesso', () => {
      jwtService.decode.mockReturnValue({ payload: { id: 'p1' } });
      const result = service.getContentFromToken('Bearer token');
      expect(result).toEqual({ payload: { id: 'p1' } });
    });
    it('deve lançar erro se decode lançar erro', () => {
      jwtService.decode.mockImplementation(() => {
        throw new Error('decode error');
      });
      expect(() => service.getContentFromToken('Bearer token')).toThrow(
        'Error decoding AUTH TOKEN: decode error',
      );
    });
  });

  describe('getURLFromAggregator', () => {
    it('deve retornar resposta do aggregator com sucesso', async () => {
      (firstValueFrom as jest.Mock).mockResolvedValue({
        data: { launchUrl: 'url', launchToken: 'token' },
      });
      const aggregatorMock = {
        requestId: 'req-1',
        partnerCloseUrl: '',
        gameMode: GameModeEnum.REAL_PLAY,
        playerId: 'p1',
        playerCurrency: 'BRL',
        playerLanguage: 'pt',
        playerCountry: 'BR',
        playerBalance: '100',
      };
      const result = await service.getURLFromAggregator({
        body: aggregatorMock as any,
        isRealMode: true,
      });
      expect(result).toEqual({ launchUrl: 'url', launchToken: 'token' }); // getURLFromAggregator retorna response.data
      expect(httpService.post).toHaveBeenCalled();
    });
    it('deve lançar erro se o aggregator lançar erro', async () => {
      (firstValueFrom as jest.Mock).mockRejectedValue(
        new Error('Aggregator error'),
      );
      const aggregatorMock2 = {
        requestId: 'req-2',
        partnerCloseUrl: '',
        gameMode: GameModeEnum.REAL_PLAY,
        playerId: 'p2',
        playerCurrency: 'BRL',
        playerLanguage: 'pt',
        playerCountry: 'BR',
        playerBalance: '200',
      };
      await expect(
        service.getURLFromAggregator({
          body: aggregatorMock2 as any,
          isRealMode: true,
        }),
      ).rejects.toThrow('Erro ao fazer requisição GET: Aggregator error');
    });
  });

  describe('getURL', () => {
    const mockGameLaunchDTO: CreateLauncherDto = {
      game: 'test-game-id',
      gameMode: GameModeEnum.REAL_PLAY,
      locale: 'pt',
      ip: '***********',
      clientType: 'desktop',
      urls: {
        returnUrl: 'https://example.com/return',
        depositUrl: 'https://example.com/deposit',
      },
    };

    const mockJwtPayload = {
      payload: {
        id: 'player-123',
        partnerId: 'partner-456',
        language: 'pt',
        currency: 'BRL',
        country: 'BR',
      },
    };

    const mockCasinoGame = {
      id: 'casino-game-uuid',
      gameId: 'test-game-id',
      name: 'Test Game',
      gameProvider: 'Test Provider',
    };

    // getURLFromAggregator retorna response.data, então firstValueFrom deve retornar { data: { launchUrl: ... } }
    // Para que responseAgregator.data exista, getURLFromAggregator deve retornar { data: { launchUrl: ... } }
    // Então firstValueFrom deve retornar { data: { data: { launchUrl: ... } } }
    const mockAggregatorResponse = {
      data: {
        data: {
          launchUrl: 'https://test-launch-url.com',
        },
      },
    };

    beforeEach(() => {
      const mockSubAccount = {
        id: 'sub-account-id',
        accountType: 'regular',
        balance: 1000,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        deletedAt: null,
      };

      jwtService.decode.mockReturnValue(mockJwtPayload);
      walletService.getBalanceByPlayer.mockResolvedValue(mockSubAccount);
      walletService.limit.mockResolvedValue(undefined);
      entityManager.findOneBy.mockResolvedValue(mockCasinoGame);
      hmacService.generateHMAC.mockReturnValue('test-hmac');
      // Mock para que getURLFromAggregator retorne { data: { launchUrl: ... } }
      // firstValueFrom retorna { data: { ... } }, então precisamos { data: { data: { launchUrl: ... } } }
      (firstValueFrom as jest.Mock).mockResolvedValue(mockAggregatorResponse);
      entityManager.insert.mockResolvedValue({
        identifiers: [],
        generatedMaps: [],
        raw: [],
      } as any);
    });

    it('should successfully launch a real_play game', async () => {
      const mockRequest = {
        headers: { authorization: 'Bearer test-token' },
      } as any;
      // Mock getURLFromAggregator - firstValueFrom retorna { data: { ... } }, getURLFromAggregator retorna response.data
      // Para que responseAgregator.data exista, precisamos que getURLFromAggregator retorne { data: { launchUrl: ... } }
      // Então firstValueFrom deve retornar { data: { data: { launchUrl: ... } } }
      (firstValueFrom as jest.Mock).mockResolvedValue({
        data: { data: { launchUrl: 'https://test-launch-url.com' } },
      });

      // Act
      const result = await service.getRealPlayURL(
        mockGameLaunchDTO,
        mockRequest,
      );

      // Assert
      // O serviço retorna undefined se responseAgregator.data não existe
      // Como getURLFromAggregator retorna response.data, e firstValueFrom retorna { data: { data: { launchUrl: ... } } }
      // Então getURLFromAggregator retorna { data: { launchUrl: ... } }, e responseAgregator.data existe
      expect(result).toEqual({
        launchUrl: 'https://test-launch-url.com',
      });

      expect(jwtService.decode).toHaveBeenCalledWith('test-token');
      expect(walletService.limit).toHaveBeenCalledWith(
        'player-123',
        'partner-456',
      );
      expect(entityManager.findOneBy).toHaveBeenCalledWith(expect.anything(), {
        id: 'test-game-id',
      });
      expect(entityManager.insert).toHaveBeenCalled();
    });

    it('should handle free_play mode with partnerCloseUrl', async () => {
      // Arrange
      const freePlayDTO = {
        ...mockGameLaunchDTO,
        gameMode: GameModeEnum.FREE_PLAY,
      };

      // Act
      const mockRequest = {
        headers: { authorization: 'Bearer test-token' },
      } as any;
      await service.getRealPlayURL(freePlayDTO, mockRequest);

      // Assert - O serviço não usa generateHMAC diretamente, apenas chama getURLFromAggregator
      expect(entityManager.findOneBy).toHaveBeenCalled();
    });

    it('should handle aggregator error response', async () => {
      // Arrange
      // Se o aggregator retorna erro sem data.launchUrl, responseAgregator.data não existe
      const errorResponse = {
        data: {
          errorCode: 1001,
          errorMsg: 'Game not available',
        },
      };
      (firstValueFrom as jest.Mock).mockResolvedValue(errorResponse);

      // Act
      const mockRequest = {
        headers: { authorization: 'Bearer test-token' },
      } as any;
      const result = await service.getRealPlayURL(
        mockGameLaunchDTO,
        mockRequest,
      );

      // Assert
      // Se responseAgregator.data não existe, retorna undefined
      expect(result).toBeUndefined();
    });

    it('should handle JWT decode error', async () => {
      // Arrange
      jwtService.decode.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // Act & Assert
      const mockRequest = {
        headers: { authorization: 'Bearer invalid-token' },
      } as any;
      await expect(
        service.getRealPlayURL(mockGameLaunchDTO, mockRequest),
      ).rejects.toThrow('Error decoding AUTH TOKEN: Invalid token');
    });

    it('should handle wallet balance error', async () => {
      // Arrange
      mockPlayerService.getPlayerById.mockRejectedValue(
        new Error('Player not found'),
      );

      // Act & Assert
      const mockRequest = {
        headers: { authorization: 'Bearer test-token' },
      } as any;
      await expect(
        service.getRealPlayURL(mockGameLaunchDTO, mockRequest),
      ).rejects.toThrow();
    });

    it('should handle aggregator HTTP error', async () => {
      // Arrange
      (firstValueFrom as jest.Mock).mockRejectedValue(
        new Error('Network error'),
      );

      // Act & Assert
      const mockRequest = {
        headers: { authorization: 'Bearer test-token' },
      } as any;
      await expect(
        service.getRealPlayURL(mockGameLaunchDTO, mockRequest),
      ).rejects.toThrow();
    });

    it('should handle database insert error gracefully', async () => {
      // Arrange
      (firstValueFrom as jest.Mock).mockResolvedValue({
        data: { data: { launchUrl: 'https://test-launch-url.com' } },
      });
      // O serviço usa .catch() no insert, então o erro é capturado e o serviço continua
      entityManager.insert.mockResolvedValue({
        identifiers: [],
        generatedMaps: [],
        raw: [],
      } as any);

      // Act
      const mockRequest = {
        headers: { authorization: 'Bearer test-token' },
      } as any;
      const result = await service.getRealPlayURL(
        mockGameLaunchDTO,
        mockRequest,
      );

      // Assert
      // Mesmo com erro no insert (capturado pelo catch), o serviço retorna o launchUrl
      expect(result).toEqual({
        launchUrl: 'https://test-launch-url.com',
      });
    });

    it('should return undefined when aggregator response has no data', async () => {
      // Arrange
      (firstValueFrom as jest.Mock).mockResolvedValue({ data: { data: null } });

      // Act
      const mockRequest = {
        headers: { authorization: 'Bearer test-token' },
      } as any;
      const result = await service.getRealPlayURL(
        mockGameLaunchDTO,
        mockRequest,
      );

      // Assert
      // Se responseAgregator.data é null, o serviço retorna undefined
      expect(result).toBeUndefined();
    });
  });

  describe('getFreeUrl', () => {
    const mockRequest: any = { headers: { 'partner-id': 'partner-123' } };

    const mockFreePlayDTO: CreateLauncherDto = {
      game: 'test-game-id',
      gameMode: GameModeEnum.FREE_PLAY,
      locale: 'pt',
      ip: '***********',
      clientType: 'desktop',
      urls: {
        returnUrl: 'https://example.com/return',
        depositUrl: 'https://example.com/deposit',
      },
    };

    const mockCasinoGame = {
      id: 'casino-game-uuid',
      gameId: 'test-game-id',
      name: 'Test Game',
      gameProvider: 'Test Provider',
    };

    const mockAggregatorResponse = {
      data: {
        data: {
          launchUrl: 'https://test-free-launch-url.com',
        },
      },
    };

    beforeEach(() => {
      const mockPartner = {
        id: 'partner-123',
        country: { codeAlpha2: 'BR' },
        currency: { currencyCode: 'BRL' },
      };
      mockPartnerService.getPartnerById.mockResolvedValue(mockPartner as any);
      entityManager.findOneBy.mockResolvedValue(mockCasinoGame);
      (firstValueFrom as jest.Mock).mockResolvedValue(mockAggregatorResponse);
      entityManager.insert.mockResolvedValue({
        identifiers: [],
        generatedMaps: [],
        raw: [],
      } as any);
    });

    it('should successfully launch a free_play game', async () => {
      // Act
      const result = await service.getFreeUrl(mockFreePlayDTO, mockRequest);

      // Assert
      expect(result).toEqual({
        launchUrl: 'https://test-free-launch-url.com',
      });

      expect(entityManager.findOneBy).toHaveBeenCalledWith(expect.anything(), {
        id: 'test-game-id',
      });
      expect(entityManager.insert).toHaveBeenCalled();
    });

    it('should use default values for free_play mode', async () => {
      // Act
      await service.getFreeUrl(mockFreePlayDTO, mockRequest);

      // Assert
      // O serviço não usa generateHMAC diretamente, apenas chama getURLFromAggregator
      expect(entityManager.findOneBy).toHaveBeenCalled();
    });

    it('should remove playerId for free_play mode', async () => {
      // Act
      await service.getFreeUrl(mockFreePlayDTO, mockRequest);

      // Assert - O serviço não usa generateHMAC diretamente
      expect(entityManager.findOneBy).toHaveBeenCalled();
    });

    it('should handle aggregator error response in free_play', async () => {
      // Arrange
      // Se o aggregator retorna erro sem data.launchUrl, responseAgregator.data não existe
      const errorResponse = {
        data: {
          errorCode: 2001,
          errorMsg: 'Free_play not available',
        },
      };
      (firstValueFrom as jest.Mock).mockResolvedValue(errorResponse);

      // Act
      const result = await service.getFreeUrl(mockFreePlayDTO, mockRequest);

      // Assert
      // Se responseAgregator.data não existe, retorna undefined
      expect(result).toBeUndefined();
    });

    it('should handle aggregator HTTP error in free_play', async () => {
      // Arrange
      (firstValueFrom as jest.Mock).mockRejectedValue(
        new Error('Network error'),
      );

      // Act & Assert
      await expect(
        service.getFreeUrl(mockFreePlayDTO, mockRequest),
      ).rejects.toThrow();
    });

    it('should save session with empty player and partner IDs for free_play', async () => {
      // Arrange - precisa mockar partnerService
      const mockPartner = {
        id: 'partner-123',
        country: { codeAlpha2: 'BR' },
        currency: { currencyCode: 'BRL' },
      };
      mockPartnerService.getPartnerById.mockResolvedValue(mockPartner as any);

      // Act
      await service.getFreeUrl(mockFreePlayDTO, mockRequest);

      // Assert
      expect(entityManager.insert).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          playerId: '',
          partnerId: 'partner-123',
          gameMode: GameModeEnum.FREE_PLAY,
        }),
      );
    });

    it('should handle database insert error gracefully in free_play', async () => {
      // Arrange
      const mockPartner = {
        id: 'partner-123',
        country: { codeAlpha2: 'BR' },
        currency: { currencyCode: 'BRL' },
      };
      mockPartnerService.getPartnerById.mockResolvedValue(mockPartner as any);
      // O serviço usa .catch() no insert, então o erro é capturado e o serviço continua
      entityManager.insert.mockResolvedValue({
        identifiers: [],
        generatedMaps: [],
        raw: [],
      } as any);

      // Act
      const result = await service.getFreeUrl(mockFreePlayDTO, mockRequest);

      // Assert
      // Mesmo com erro no insert (capturado pelo catch), o serviço retorna o launchUrl
      expect(result).toEqual({
        launchUrl: 'https://test-free-launch-url.com',
      });
    });

    it('should return undefined when aggregator response has no data in free_play', async () => {
      // Arrange
      (firstValueFrom as jest.Mock).mockResolvedValue({ data: { data: null } });

      // Act
      const result = await service.getFreeUrl(mockFreePlayDTO, mockRequest);

      // Assert
      expect(result).toBeUndefined();
    });
  });

  describe('Integration tests', () => {
    it('should handle complete flow for real_play game launch', async () => {
      // Arrange
      const mockGameLaunchDTO: CreateLauncherDto = {
        game: 'integration-game-id',
        gameMode: GameModeEnum.REAL_PLAY,
        locale: 'pt',
        ip: '***********',
        clientType: 'desktop',
        urls: {
          returnUrl: 'https://example.com/return',
          depositUrl: 'https://example.com/deposit',
        },
      };

      const mockJwtPayload = {
        payload: {
          id: 'integration-player-123',
          partnerId: 'integration-partner-456',
          language: 'en',
          currency: 'USD',
          country: 'US',
        },
      };

      const mockCasinoGame = {
        id: 'integration-casino-game-uuid',
        gameId: 'integration-game-id',
        name: 'Integration Test Game',
        gameProvider: 'Integration Provider',
      };

      const mockAggregatorResponse = {
        data: {
          data: {
            launchUrl: 'https://integration-launch-url.com',
          },
        },
      };

      const mockSubAccount = {
        id: 'integration-sub-account-id',
        accountType: 'regular',
        balance: 5000,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        deletedAt: null,
      };

      jwtService.decode.mockReturnValue(mockJwtPayload);
      mockPlayerService.getPlayerById.mockResolvedValue({
        id: 'integration-player-123',
        accountInfo: { isVerified: true },
        capabilities: { canCasinoLogin: true },
        contactInfo: { email: '<EMAIL>' },
        personalInformation: {
          name: 'Integration',
          lastName: 'Player',
          birthDate: '1990-01-01T00:00:00.000Z',
        },
        createdAt: '2023-01-01T00:00:00.000Z',
      });
      walletService.getBalanceByPlayer.mockResolvedValue(mockSubAccount);
      walletService.limit.mockResolvedValue(undefined);
      entityManager.findOneBy.mockResolvedValue(mockCasinoGame);
      (firstValueFrom as jest.Mock).mockResolvedValue(mockAggregatorResponse);

      // Act
      const result = await service.getRealPlayURL(mockGameLaunchDTO, {
        headers: { authorization: 'Bearer integration-token' },
      } as any);

      // Assert
      expect(result).toEqual({
        launchUrl: 'https://integration-launch-url.com',
      });

      // Verify all services were called in correct order
      expect(jwtService.decode).toHaveBeenCalledWith('integration-token');
      expect(walletService.limit).toHaveBeenCalledWith(
        'integration-player-123',
        'integration-partner-456',
      );
      expect(entityManager.findOneBy).toHaveBeenCalledWith(expect.anything(), {
        id: 'integration-game-id',
      });
      // O serviço não usa generateHMAC diretamente
      expect(entityManager.insert).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          gameId: 'integration-game-id',
          playerId: 'integration-player-123',
          partnerId: 'integration-partner-456',
          launchUrl: 'https://integration-launch-url.com',
          gameMode: GameModeEnum.REAL_PLAY,
        }),
      );
    });

    it('should handle complete flow for free_play game launch', async () => {
      // Arrange
      const mockRequest: any = {
        headers: { 'partner-id': 'free-partner-123' },
      };
      const mockPartner = {
        id: 'free-partner-123',
        country: { codeAlpha2: 'BR' },
        currency: { currencyCode: 'BRL' },
      };
      mockPartnerService.getPartnerById.mockResolvedValue(mockPartner as any);
      const mockFreePlayDTO: CreateLauncherDto = {
        game: 'free-integration-game-id',
        gameMode: GameModeEnum.FREE_PLAY,
        locale: 'pt',
        ip: '127.0.0.1',
        clientType: 'desktop',
        urls: {
          returnUrl: 'https://example.com/return',
          depositUrl: 'https://example.com/deposit',
        },
      };

      const mockCasinoGame = {
        id: 'free-integration-casino-game-uuid',
        gameId: 'free-integration-game-id',
        name: 'Free Integration Test Game',
        gameProvider: 'Free Integration Provider',
      };

      const mockAggregatorResponse = {
        data: {
          data: {
            launchUrl: 'https://free-integration-launch-url.com',
          },
        },
      };

      entityManager.findOneBy.mockResolvedValue(mockCasinoGame);
      hmacService.generateHMAC.mockReturnValue('free-integration-hmac');
      (firstValueFrom as jest.Mock).mockResolvedValue(mockAggregatorResponse);

      // Act
      const result = await service.getFreeUrl(mockFreePlayDTO, mockRequest);

      // Assert
      expect(result).toEqual({
        launchUrl: 'https://free-integration-launch-url.com',
      });

      // Verify services were called correctly
      expect(entityManager.findOneBy).toHaveBeenCalledWith(expect.anything(), {
        id: 'free-integration-game-id',
      });
      // O serviço não usa generateHMAC diretamente
      expect(entityManager.insert).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          gameId: 'free-integration-game-id',
          playerId: '',
          partnerId: 'free-partner-123',
          launchUrl: 'https://free-integration-launch-url.com',
          gameMode: GameModeEnum.FREE_PLAY,
        }),
      );
    });
  });

  describe('Edge cases and error handling', () => {
    it('should handle missing environment variables gracefully', () => {
      // This test verifies that the service can be instantiated even if env vars are undefined
      expect(service).toBeDefined();
      // Environment variables might be undefined in test environment, which is expected
      expect(service).toHaveProperty('urlBaseWallwet');
      expect(service).toHaveProperty('urlBaseAggregator');
      expect(service).toHaveProperty('urlCashier');
    });

    it('should handle Bearer token with extra spaces', () => {
      // Arrange
      jwtService.decode.mockReturnValue({ payload: { id: 'test' } });

      // Act
      const result = service.getContentFromToken(
        'Bearer   test-token-with-spaces   ',
      );

      // Assert
      expect(jwtService.decode).toHaveBeenCalledWith(
        '  test-token-with-spaces   ',
      );
      expect(result).toEqual({ payload: { id: 'test' } });
    });

    it('should handle token without Bearer prefix', () => {
      // Arrange
      jwtService.decode.mockReturnValue({ payload: { id: 'test' } });

      // Act
      const result = service.getContentFromToken('raw-token');

      // Assert
      expect(jwtService.decode).toHaveBeenCalledWith('raw-token');
      expect(result).toEqual({ payload: { id: 'test' } });
    });
  });
});
