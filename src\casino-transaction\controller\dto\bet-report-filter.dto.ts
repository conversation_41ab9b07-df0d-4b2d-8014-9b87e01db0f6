import { GenericFilter } from '@/common/filters/generic-filter.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsUUID } from 'class-validator';

export class BetReportFilterDto extends GenericFilter {
  @ApiPropertyOptional({ description: 'Data inicial (YYYY-MM-DD)' })
  @IsOptional()
  fromDate: string;

  @ApiPropertyOptional({ description: 'Data final (YYYY-MM-DD)' })
  @IsOptional()
  toDate: string;

  @ApiPropertyOptional({ description: 'ID do jogador' })
  @IsOptional()
  @IsUUID()
  playerId?: string;

  @ApiPropertyOptional({ description: 'ID do jogo' })
  @IsOptional()
  @IsUUID()
  gameId?: string;

  @ApiPropertyOptional({ description: 'ID do provedor do jogo' })
  @IsOptional()
  @IsUUID()
  gameProviderId?: string;
}
