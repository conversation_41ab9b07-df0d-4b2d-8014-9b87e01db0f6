/**
 * Interface base para todos os provedores
 */
export interface IProvider {
  readonly name: string;
  readonly version: string;
}

/**
 * Interface para requisições de saldo
 */
export interface IBalanceRequest {
  playerId: string;
  gameId?: string;
  currency?: string;
  sessionId?: string;
  timestamp?: Date;
  metadata?: Record<string, any>;
}

/**
 * Interface para respostas de saldo
 */
export interface IBalanceResponse {
  playerId: string;
  balance: number;
  currency: string;
  timestamp: Date;
  success: boolean;
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * Interface para requisições de aposta
 */
export interface IBetRequest {
  playerId: string;
  gameId: string;
  amount: number;
  currency: string;
  roundId: string;
  sessionId?: string;
  timestamp?: Date;
  metadata?: Record<string, any>;
}

/**
 * Interface para respostas de aposta
 */
export interface IBetResponse {
  playerId: string;
  transactionId: string;
  balance: number;
  currency: string;
  success: boolean;
  timestamp: Date;
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * Interface para requisições de ganho
 */
export interface IWinRequest {
  playerId: string;
  gameId: string;
  amount: number;
  currency: string;
  roundId: string;
  betTransactionId: string;
  sessionId?: string;
  timestamp?: Date;
  metadata?: Record<string, any>;
}

/**
 * Interface para respostas de ganho
 */
export interface IWinResponse {
  playerId: string;
  transactionId: string;
  balance: number;
  currency: string;
  success: boolean;
  timestamp: Date;
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * Interface principal do adapter
 */
export interface IProviderAdapter extends IProvider {
  /**
   * Converte requisição do provedor para formato padrão
   */
  transformRequest<T>(rawRequest: any, operation: string): T;

  /**
   * Converte resposta padrão para formato do provedor
   */
  transformResponse<T>(standardResponse: any, operation: string): T;

  /**
   * Valida se a requisição é válida para este provedor
   */
  validateRequest(rawRequest: any, operation: string): boolean;

  /**
   * Obtém informações sobre o provedor
   */
  getProviderInfo(): IProvider;
}

/**
 * Enum para operações suportadas
 */
export enum ProviderOperation {
  BALANCE = 'balance',
  BET = 'bet',
  WIN = 'win',
  ROLLBACK = 'rollback',
  REFUND = 'refund',
}

/**
 * Interface para configuração do provedor
 */
export interface IProviderConfig {
  name: string;
  version: string;
  enabled: boolean;
  endpoints: Record<string, string>;
  authentication?: {
    type: 'bearer' | 'basic' | 'apikey' | 'custom';
    credentials: Record<string, any>;
  };
  timeout?: number;
  retries?: number;
  rateLimit?: {
    requests: number;
    window: number; // em segundos
  };
}
