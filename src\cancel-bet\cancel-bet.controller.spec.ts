import { Test, TestingModule } from '@nestjs/testing';
import { CancelBetController } from './cancel-bet.controller';
import { CancelBetService } from './cancel-bet.service';
import { CancelBetDto } from './dto/create-cancel-bet.dto';
import { HMACGuard } from '../common/guards/hmac.guard';
import { HMACService } from '../hmac/service/hmac.service';

const dto: CancelBetDto = {
  playerId: 'player-1',
  sessionId: 'session-1',
  transactionId: 'trans-1',
  requestType: 'RealMoney',
  gameId: 1,
  refTransactionId: 'ref-1',
  roundId: 'round-1',
  roundEnded: 'true',
};

describe('CancelBetController', () => {
  let controller: CancelBetController;
  let service: CancelBetService;

  const mockCancelBetService = {
    cancelBet: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CancelBetController],
      providers: [
        { provide: CancelBetService, useValue: mockCancelBetService },
        { provide: HMACService, useValue: {} },
      ],
    })
      .overrideGuard(HMACGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<CancelBetController>(CancelBetController);
    service = module.get<CancelBetService>(CancelBetService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('deve retornar sucesso ao cancelar aposta', async () => {
    const retorno = { errorCode: 0, balance: 100 };
    mockCancelBetService.cancelBet.mockResolvedValue(retorno);
    const result = await controller.cancelBet(dto);
    expect(result).toEqual(retorno);
    expect(service.cancelBet).toHaveBeenCalledWith(dto);
  });

  it('deve retornar erro de aposta não encontrada', async () => {
    const retorno = { errorCode: 1115, errorMsg: 'Bet not found' };
    mockCancelBetService.cancelBet.mockResolvedValue(retorno);
    const result = await controller.cancelBet(dto);
    expect(result).toEqual(retorno);
  });

  it('deve propagar erro genérico lançado pelo service', async () => {
    mockCancelBetService.cancelBet.mockRejectedValue(
      new Error('Erro inesperado'),
    );
    await expect(controller.cancelBet(dto)).rejects.toThrow('Erro inesperado');
  });
});
