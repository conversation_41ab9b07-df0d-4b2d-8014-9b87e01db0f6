/**
 * Standardized error codes for the wallet API
 * These codes will be used across the application and mapped by adapters
 */
export enum ErrorCode {
  // Wallet Errors
  WALLET_NOT_FOUND = 'WALLET_NOT_FOUND',
  WALLET_LOCKED = 'WALLET_LOCKED',
  WALLET_INACTIVE = 'WALLET_INACTIVE',

  // Casino Errors
  CASINO_DISABLED = 'CASINO_DISABLED',
  CASINO_NOT_FOUND = 'CASINO_NOT_FOUND',
  PROVIDER_NOT_CONFIGURED = 'PROVIDER_NOT_CONFIGURED',
  PROVIDER_DISABLED = 'PROVIDER_DISABLED',

  // Transaction Errors
  INSUFFICIENT_FUNDS = 'INSUFFICIENT_FUNDS',
  TRANSACTION_FAILED = 'TRANSACTION_FAILED',
  DUPLICATE_TRANSACTION = 'DUPLICATE_TRANSACTION',
  INVALID_AMOUNT = 'INVALID_AMOUNT',
  INVALID_TRANSACTION_TYPE = 'INVALID_TRANSACTION_TYPE',

  // Limits Errors
  DEPOSIT_LIMIT_EXCEEDED = 'DEPOSIT_LIMIT_EXCEEDED',
  WITHDRAWAL_LIMIT_EXCEEDED = 'WITHDRAWAL_LIMIT_EXCEEDED',
  BET_LIMIT_EXCEEDED = 'BET_LIMIT_EXCEEDED',
  MAX_BET_EXCEEDED = 'MAX_BET_EXCEEDED',
  BET_LIMIT_REACHED = 'BET_LIMIT_REACHED',
  LOSS_LIMIT_EXCEEDED = 'LOSS_LIMIT_EXCEEDED',

  // Player Errors
  PLAYER_NOT_FOUND = 'PLAYER_NOT_FOUND',
  PLAYER_BLOCKED = 'PLAYER_BLOCKED',
  PLAYER_SELF_EXCLUDED = 'PLAYER_SELF_EXCLUDED',
  PLAYER_INVALID = 'PLAYER_INVALID',
  PLAYER_DISABLED = 'PLAYER_DISABLED',

  // System Errors
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  TIMEOUT = 'TIMEOUT',
  REQUEST_TIMEOUT = 'REQUEST_TIMEOUT',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  ENDPOINT_DISABLED = 'ENDPOINT_DISABLED',
  INVALID_EVENT_TYPE = 'INVALID_EVENT_TYPE',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  FORBIDDEN = 'FORBIDDEN',
  BAD_REQUEST = 'BAD_REQUEST',
  NOT_FOUND = 'NOT_FOUND',

  // Game Errors
  GAME_FORBIDDEN = 'GAME_FORBIDDEN',
  GAME_NOT_FOUND = 'GAME_NOT_FOUND',
  GAME_NOT_AVAILABLE = 'GAME_NOT_AVAILABLE',
  GAME_UNAVAILABLE = 'GAME_UNAVAILABLE',

  // Bonus Errors
  BONUS_NOT_FOUND = 'BONUS_NOT_FOUND',
  BONUS_ALREADY_CLAIMED = 'BONUS_ALREADY_CLAIMED',
  BONUS_ELIGIBILITY_FAILED = 'BONUS_ELIGIBILITY_FAILED',
  BONUS_USAGE_LIMIT_EXCEEDED = 'BONUS_USAGE_LIMIT_EXCEEDED',

  // Freespins Errors
  FREESPINS_NOT_PROVIDED = 'FREESPINS_NOT_PROVIDED',
  FREESPINS_ISSUE_ERROR = 'FREESPINS_ISSUE_ERROR',
}

/**
 * Error metadata interface for type safety
 */
export interface ErrorMetadata {
  [key: string]: any;
}
