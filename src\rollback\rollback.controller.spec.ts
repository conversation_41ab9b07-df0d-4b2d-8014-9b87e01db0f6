import { Test, TestingModule } from '@nestjs/testing';
import { RollbackRequestDto } from './dto/rollback-request.dto';
import { RollbackResponseDto } from './dto/rollback-response.dto';
import { RollbackController } from './rollback.controller';
import { RollbackService } from './rollback.service';

describe('RollbackController', () => {
  let controller: RollbackController;
  let service: RollbackService;

  const mockRollbackService = {
    processRollback: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RollbackController],
      providers: [{ provide: RollbackService, useValue: mockRollbackService }],
    }).compile();

    controller = module.get<RollbackController>(RollbackController);
    service = module.get<RollbackService>(RollbackService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('processRollback', () => {
    it('deve processar rollback com sucesso', async () => {
      const dto: RollbackRequestDto = {
        playerId: 'player-123',
        sessionId: 'session-123',
        roundId: 'round-123',
        gameId: 'game-123',
        currency: 'BRL',
        aggregatorCode: 'AGG',
        roundEnded: false,
        transactions: [
          {
            id: 'tx-rollback-1',
            originalId: 'tx-original-1',
          },
        ],
      };

      const expectedResponse: RollbackResponseDto = {
        balance: '150',
        roundIdCasino: 'transaction-123',
        transactions: [
          {
            id: 'tx-rollback-1',
            idCasino: 'detail-123',
            processedAt: '2024-01-01T00:00:00.000Z',
          },
        ],
      };

      mockRollbackService.processRollback.mockResolvedValue(expectedResponse);

      const result = await controller.processRollback(dto);

      expect(result).toEqual(expectedResponse);
      expect(service.processRollback).toHaveBeenCalledWith(dto);
      expect(service.processRollback).toHaveBeenCalledTimes(1);
    });

    it('deve chamar service com DTO correto', async () => {
      const dto: RollbackRequestDto = {
        playerId: 'player-456',
        sessionId: 'session-456',
        roundId: 'round-456',
        gameId: 'game-456',
        currency: 'USD',
        aggregatorCode: 'TEST',
        roundEnded: true,
        transactions: [
          {
            id: 'tx-1',
            originalId: 'tx-orig-1',
          },
          {
            id: 'tx-2',
            originalId: 'tx-orig-2',
          },
        ],
      };

      const mockResponse: RollbackResponseDto = {
        balance: '200',
        roundIdCasino: 'tx-456',
        transactions: [],
      };

      mockRollbackService.processRollback.mockResolvedValue(mockResponse);

      await controller.processRollback(dto);

      expect(service.processRollback).toHaveBeenCalledWith(dto);
      expect(service.processRollback).toHaveBeenCalledWith(
        expect.objectContaining({
          playerId: 'player-456',
          sessionId: 'session-456',
          roundId: 'round-456',
          gameId: 'game-456',
          currency: 'USD',
          aggregatorCode: 'TEST',
          roundEnded: true,
        }),
      );
    });

    it('deve propagar erro do service', async () => {
      const dto: RollbackRequestDto = {
        playerId: 'player-error',
        sessionId: 'session-error',
        roundId: 'round-error',
        gameId: 'game-error',
        currency: 'BRL',
        aggregatorCode: 'AGG',
        roundEnded: false,
        transactions: [],
      };

      const error = new Error('Service error');
      mockRollbackService.processRollback.mockRejectedValue(error);

      await expect(controller.processRollback(dto)).rejects.toThrow(
        'Service error',
      );
    });

    it('deve retornar resposta vazia quando não há transações', async () => {
      const dto: RollbackRequestDto = {
        playerId: 'player-empty',
        sessionId: 'session-empty',
        roundId: 'round-empty',
        gameId: 'game-empty',
        currency: 'EUR',
        aggregatorCode: 'AGG',
        roundEnded: false,
        transactions: [],
      };

      const expectedResponse: RollbackResponseDto = {
        balance: '100',
        roundIdCasino: 'tx-empty',
        transactions: [],
      };

      mockRollbackService.processRollback.mockResolvedValue(expectedResponse);

      const result = await controller.processRollback(dto);

      expect(result.transactions).toHaveLength(0);
      expect(result.balance).toBe('100');
    });

    it('deve processar múltiplas transações de rollback', async () => {
      const dto: RollbackRequestDto = {
        playerId: 'player-multi',
        sessionId: 'session-multi',
        roundId: 'round-multi',
        gameId: 'game-multi',
        currency: 'BRL',
        aggregatorCode: 'AGG',
        roundEnded: false,
        transactions: [
          { id: 'tx-1', originalId: 'orig-1' },
          { id: 'tx-2', originalId: 'orig-2' },
          { id: 'tx-3', originalId: 'orig-3' },
        ],
      };

      const expectedResponse: RollbackResponseDto = {
        balance: '300',
        roundIdCasino: 'tx-multi',
        transactions: [
          {
            id: 'tx-1',
            idCasino: 'detail-1',
            processedAt: '2024-01-01T00:00:00.000Z',
          },
          {
            id: 'tx-2',
            idCasino: 'detail-2',
            processedAt: '2024-01-01T00:00:00.000Z',
          },
          {
            id: 'tx-3',
            idCasino: 'detail-3',
            processedAt: '2024-01-01T00:00:00.000Z',
          },
        ],
      };

      mockRollbackService.processRollback.mockResolvedValue(expectedResponse);

      const result = await controller.processRollback(dto);

      expect(result.transactions).toHaveLength(3);
      expect(result.balance).toBe('300');
    });

    it('deve manter estrutura correta de resposta', async () => {
      const dto: RollbackRequestDto = {
        playerId: 'player-struct',
        sessionId: 'session-struct',
        roundId: 'round-struct',
        gameId: 'game-struct',
        currency: 'BRL',
        aggregatorCode: 'AGG',
        roundEnded: true,
        transactions: [{ id: 'tx-struct', originalId: 'orig-struct' }],
      };

      const expectedResponse: RollbackResponseDto = {
        balance: '500',
        roundIdCasino: 'tx-struct-casino',
        transactions: [
          {
            id: 'tx-struct',
            idCasino: 'detail-struct',
            processedAt: '2024-01-01T10:30:00.000Z',
          },
        ],
      };

      mockRollbackService.processRollback.mockResolvedValue(expectedResponse);

      const result = await controller.processRollback(dto);

      expect(result).toHaveProperty('balance');
      expect(result).toHaveProperty('roundIdCasino');
      expect(result).toHaveProperty('transactions');
      expect(Array.isArray(result.transactions)).toBe(true);
      expect(result.transactions[0]).toHaveProperty('id');
      expect(result.transactions[0]).toHaveProperty('idCasino');
      expect(result.transactions[0]).toHaveProperty('processedAt');
    });
  });
});
