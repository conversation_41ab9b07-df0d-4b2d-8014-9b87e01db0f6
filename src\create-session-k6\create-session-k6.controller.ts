import {
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@/common/decorators/swagger.decorator';
import { LoggingInterceptor } from '@/common/interceptors/logging.interceptor';
import {
  Body,
  Controller,
  HttpCode,
  Logger,
  Post,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { CreateSessionK6Service } from './create-session-k6.service';
import { CreateSessionK6RequestDto } from './dto/create-session-k6-request.dto';
import { CreateSessionK6ResponseDTO } from './dto/create-session-k6-response.dto';

@Controller('create-session-k6')
@ApiTags('CreateSessionK6')
@ApiBearerAuth('access-token')
export class CreateSessionK6Controller {
  private readonly logger = new Logger(CreateSessionK6Controller.name);
  constructor(
    private readonly createSessionK6Service: CreateSessionK6Service,
  ) {}

  @Post()
  @UseInterceptors(LoggingInterceptor)
  @HttpCode(200)
  @ApiOperation({
    summary: 'Iniciar jogo',
    description: 'Endpoint para iniciar um jogo',
  })
  @ApiForbiddenResponse()
  @ApiInternalServerErrorResponse()
  async play(
    @Body() body: CreateSessionK6RequestDto,
  ): Promise<CreateSessionK6ResponseDTO> {
    this.logger.log(`Game createSessionLauncher: ${JSON.stringify(body)}`);
    return this.createSessionK6Service.createSessionLauncher(body);
  }
}
