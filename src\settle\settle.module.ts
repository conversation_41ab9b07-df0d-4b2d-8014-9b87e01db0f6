import { Module } from '@nestjs/common';
import { DatabaseModule } from 'src/common/database/database.module';
import { HMACModule } from 'src/hmac/hmac.module';
import { SettleService } from './settle.service';
import { SettleController } from './settle.controller';
import { WalletModule } from '@/wallet/wallet.module';

@Module({
  imports: [DatabaseModule, HMACModule, WalletModule],
  controllers: [SettleController],
  providers: [SettleService],
})
export class SettleModule {}
