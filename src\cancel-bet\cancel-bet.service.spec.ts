import { Test, TestingModule } from '@nestjs/testing';
import { CancelBetService } from './cancel-bet.service';
import { WalletService } from '../wallet/wallet.service';
import { EntityManager } from 'typeorm';
import { HttpException } from '@nestjs/common';

const mockCasinoTransaction = {
  playerId: 'player-1',
  partnerId: 'partner-1',
  roundId: 'round-1',
  transactionBetId: 'ref-1',
  transactionBetSettleId: 'ref-1',
  transactionSettleId: 'ref-1',
};

describe('CancelBetService', () => {
  let service: CancelBetService;
  let entityManager: any;
  let walletService: any;

  const mockEntityManager = {
    findOne: jest.fn(),
    save: jest.fn(),
  };
  const mockWalletService = {
    getBalanceByPlayer: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CancelBetService,
        { provide: EntityManager, useValue: mockEntityManager },
        { provide: WalletService, useValue: mockWalletService },
      ],
    }).compile();

    service = module.get<CancelBetService>(CancelBetService);
    entityManager = module.get<EntityManager>(EntityManager);
    walletService = module.get<WalletService>(WalletService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('cancelBet', () => {
    const dto = {
      playerId: 'player-1',
      sessionId: 'session-1',
      transactionId: 'trans-1',
      requestType: 'RealMoney',
      gameId: 1,
      refTransactionId: 'ref-1',
      roundId: 'round-1',
      roundEnded: 'true',
    };

    it('deve cancelar aposta com sucesso', async () => {
      entityManager.findOne.mockResolvedValue({
        ...mockCasinoTransaction,
        partnerId: 'partner-1',
      });
      walletService.getBalanceByPlayer.mockResolvedValue({ balance: 100 });
      entityManager.save = jest.fn().mockResolvedValue({});
      const result = await service.cancelBet(dto);
      expect(result).toEqual(100);
      expect(entityManager.save).toHaveBeenCalled();
    });

    it('deve retornar erro se aposta não encontrada', async () => {
      entityManager.findOne.mockResolvedValue(null);
      await expect(service.cancelBet(dto)).rejects.toThrow();
    });

    it('deve retornar balance 0 se getBalanceInWallet lançar erro', async () => {
      entityManager.findOne.mockResolvedValue({
        ...mockCasinoTransaction,
        partnerId: 'partner-1',
      });
      walletService.getBalanceByPlayer.mockImplementation(() => {
        throw new Error('Erro balance');
      });
      entityManager.save = jest.fn().mockResolvedValue({});
      const result = await service.cancelBet(dto);
      expect(result).toEqual(0);
    });

    it('deve lançar HttpException se balance não encontrado', async () => {
      entityManager.findOne.mockResolvedValue({
        ...mockCasinoTransaction,
        partnerId: 'partner-1',
      });
      walletService.getBalanceByPlayer.mockResolvedValue({});
      await expect(
        service.getBalanceInWallet('partner-1', 'player-1'),
      ).rejects.toThrow(HttpException);
    });

    it('deve lançar HttpException se walletService lançar erro com response customizada', async () => {
      entityManager.findOne.mockResolvedValue({
        ...mockCasinoTransaction,
        partnerId: 'partner-1',
      });
      walletService.getBalanceByPlayer.mockImplementation(() => {
        const err: any = new Error('Erro externo');
        err.response = { errorCode: 99, errorMsg: 'Erro externo' };
        throw err;
      });
      await expect(
        service.getBalanceInWallet('partner-1', 'player-1'),
      ).rejects.toThrow(HttpException);
    });

    it('deve tratar erro ao salvar atualização da transação', async () => {
      entityManager.findOne.mockResolvedValue({
        ...mockCasinoTransaction,
        partnerId: 'partner-1',
      });
      walletService.getBalanceByPlayer.mockResolvedValue({ balance: 100 });
      entityManager.save = jest
        .fn()
        .mockRejectedValue(new Error('Falha ao salvar'));
      // Mesmo com erro, o fluxo de cancelBet retorna balance 0
      const result = await service.cancelBet(dto);
      expect(result).toEqual(0);
    });
  });
});
