import {
  DeleteObjectCommand,
  PutObjectCommand,
  PutObjectCommandInput,
  S3Client,
} from '@aws-sdk/client-s3';
import { Injectable, Logger } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';

export interface S3UploadResult {
  url: string;
  path: string;
}

@Injectable()
export class S3Service {
  private readonly logger = new Logger(S3Service.name);
  private readonly s3Client: S3Client;
  private readonly bucketName: string;
  private readonly region: string;

  constructor() {
    this.region = process.env.AWS_REGION || 'us-east-1';
    this.bucketName = process.env.AWS_S3_BUCKET_NAME;

    this.s3Client = new S3Client({
      region: this.region,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    });

    this.logger.log(
      `S3Service initialized - Region: ${this.region}, Bucket: ${this.bucketName}`,
    );
  }

  async uploadFile(
    file: Buffer,
    fileName: string,
    folder = 'casino-games',
    customFileName?: string,
  ): Promise<S3UploadResult> {
    try {
      this.logger.log(`[Inicio] Upload file to S3: ${fileName}`);

      const fileExtension = fileName.split('.').pop();
      const uniqueFileName = customFileName
        ? `${customFileName}.${fileExtension}`
        : `${uuidv4()}.${fileExtension}`;
      const key = folder ? `${folder}/${uniqueFileName}` : uniqueFileName;

      const contentType = this.getContentType(fileExtension);

      const params: PutObjectCommandInput = {
        Bucket: this.bucketName,
        Key: key,
        Body: file,
        ContentType: contentType,
      };

      const command = new PutObjectCommand(params);
      await this.s3Client.send(command);

      const fileUrl = `https://${this.bucketName}.s3.${this.region}.amazonaws.com/${key}`;

      this.logger.log(`[Fim] File uploaded successfully: ${fileUrl}`);
      return { url: fileUrl, path: key };
    } catch (error) {
      this.logger.error(
        `Error uploading file to S3: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to upload file to S3: ${error.message}`);
    }
  }

  async uploadBase64File(
    base64String: string,
    folder = 'casino_games',
    customFileName?: string,
  ): Promise<S3UploadResult> {
    try {
      this.logger.log(`[Inicio] Upload base64 file to S3`);

      let base64Data = base64String;
      let contentType = 'image/jpeg';
      let fileExtension = 'jpg';

      if (base64String.includes('data:image')) {
        const matches = base64String.match(
          /data:image\/([a-zA-Z+]+);base64,(.+)/,
        );
        if (matches && matches.length === 3) {
          const imageType = matches[1];
          base64Data = matches[2];

          const typeMap: { [key: string]: { ext: string; mime: string } } = {
            jpeg: { ext: 'jpg', mime: 'image/jpeg' },
            jpg: { ext: 'jpg', mime: 'image/jpeg' },
            png: { ext: 'png', mime: 'image/png' },
            gif: { ext: 'gif', mime: 'image/gif' },
            webp: { ext: 'webp', mime: 'image/webp' },
            'svg+xml': { ext: 'svg', mime: 'image/svg+xml' },
          };

          const mapped = typeMap[imageType.toLowerCase()];
          if (mapped) {
            fileExtension = mapped.ext;
            contentType = mapped.mime;
          }
        }
      }

      const buffer = Buffer.from(base64Data, 'base64');

      const uniqueFileName = customFileName
        ? `${customFileName}.${fileExtension}`
        : `${uuidv4()}.${fileExtension}`;
      const key = folder ? `${folder}/${uniqueFileName}` : uniqueFileName;

      const params: PutObjectCommandInput = {
        Bucket: this.bucketName,
        Key: key,
        Body: buffer,
        ContentType: contentType,
      };

      const command = new PutObjectCommand(params);
      await this.s3Client.send(command);

      const fileUrl = `https://${this.bucketName}.s3.${this.region}.amazonaws.com/${key}`;

      this.logger.log(`[Fim] Base64 file uploaded successfully: ${fileUrl}`);
      return { url: fileUrl, path: key };
    } catch (error) {
      this.logger.error(
        `Error uploading base64 file to S3: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to upload base64 file to S3: ${error.message}`);
    }
  }

  async deleteFileByUrl(fileUrl: string): Promise<void> {
    try {
      this.logger.log(`[Inicio] Delete file from S3: ${fileUrl}`);

      const key = this.extractKeyFromUrl(fileUrl);

      if (!key) {
        throw new Error('Invalid S3 URL');
      }

      const params = {
        Bucket: this.bucketName,
        Key: key,
      };

      const command = new DeleteObjectCommand(params);
      await this.s3Client.send(command);

      this.logger.log(`[Fim] File deleted successfully: ${fileUrl}`);
    } catch (error) {
      this.logger.error(
        `Error deleting file from S3: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to delete file from S3: ${error.message}`);
    }
  }

  private extractKeyFromUrl(fileUrl: string): string | null {
    try {
      const urlParts = fileUrl.split('.amazonaws.com/');
      return urlParts.length > 1 ? urlParts[1] : null;
    } catch (error) {
      this.logger.error(`Error extracting key from URL: ${error.message}`);
      return null;
    }
  }

  private getContentType(extension: string): string {
    const contentTypes: { [key: string]: string } = {
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      webp: 'image/webp',
      svg: 'image/svg+xml',
    };

    return contentTypes[extension.toLowerCase()] || 'application/octet-stream';
  }
}
