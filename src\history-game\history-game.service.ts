import { GenericFilter } from "@/common/filters/generic-filter.dto";
import { HttpException, HttpStatus, Injectable, Logger } from "@nestjs/common";
import { HistoryGameEntity } from "./entities/history-game.entity";
import { InjectEntityManager } from "@nestjs/typeorm";
import { Between, EntityManager, IsNull, Not } from "typeorm";
import { SortOrder } from "@/common/filters/sortOrder";
import { DateUtilsService } from "@/common/utils/date";

export type AggregatedHistoryGame = {
  id: string;
  player_id: string;
  game_id: string;
  game_name: string;
  game_provider: string;
  currency: string;
  btag: string;
  region: string;
  partner_id: string;
  date: string | Date;
  total_bet: string;
  total_settle: string;
  total_round: string;
  ggr: string;
  profitability: string;
};

export type HistoryGameSummary = {
  totalBet: number;
  totalSettle: number;
  totalRound: number;
  ggr: number;
  profitability: number;
};

export type HistoryGameResponse = Promise<{
  data: AggregatedHistoryGame[];
  summary: HistoryGameSummary;
}>;

@Injectable()
export class HistoryGameService {
  private readonly logger = new Logger(HistoryGameService.name);
  constructor(
    @InjectEntityManager() private readonly casinoManager: EntityManager,
    private readonly dateService: DateUtilsService
  ) {}

  async getHistoryGame(
    playerId: string,
    filter: GenericFilter,
    startDate: string,
    endDate: string,
    req: Request
  ): Promise<HistoryGameResponse> {
    const partnerId = req.headers["partner-id"] as string;
    const { fromDate, toDate } = this.dateService.getDateRange(
      startDate,
      endDate
    );

    const whereCondition: any = {};

    if (partnerId) whereCondition.partner_id = partnerId;
    if (playerId) whereCondition.player_id = playerId;
    if (fromDate && toDate) whereCondition.date = Between(fromDate, toDate);

    const orderBy = filter.orderBy ?? "game_name";
    const sortOrder = filter.sortOrder ?? SortOrder.ASC;

    try {
      this.logger.log(
        `[Inicio] Find history game agrupado por game_id: ${JSON.stringify(filter)}`
      );

      const history: AggregatedHistoryGame[] = await this.casinoManager
        .createQueryBuilder(HistoryGameEntity, "h")
        .select([
          "h.game_id as game_id",
          "MAX(h.game_name) as game_name",
          "MAX(h.game_provider) as game_provider",
          "MAX(h.currency) as currency",
          "MAX(h.btag) as btag",
          "MAX(h.region) as region",
          "MAX(CAST(h.partner_id AS text)) as partner_id",
          "SUM(h.total_bet) as total_bet",
          "SUM(h.total_settle) as total_settle",
          "SUM(h.total_round) as total_round",
          "SUM(h.total_bet) - SUM(h.total_settle) as ggr",
          `ROUND(((SUM(h.total_settle) - SUM(h.total_bet)) / NULLIF(SUM(h.total_bet), 0)) * 100, 2) as profitability`,
        ])
        .where(whereCondition)
        .groupBy("h.game_id")
        .orderBy(orderBy, sortOrder)
        .getRawMany();

      const total = await this.casinoManager
        .createQueryBuilder(HistoryGameEntity, "h")
        .select([
          "SUM(h.total_bet) AS total_bet",
          "SUM(h.total_settle) AS total_settle",
          "SUM(h.total_round) AS total_round",
          "SUM(h.total_bet) - SUM(h.total_settle) AS ggr",
          `ROUND(((SUM(h.total_settle) - SUM(h.total_bet)) / NULLIF(SUM(h.total_bet), 0)) * 100, 2) as profitability`,
        ])
        .where(whereCondition)
        .getRawOne();

      return {
        data: history,
        summary: {
          totalBet: parseFloat(total.total_bet),
          totalSettle: parseFloat(total.total_settle),
          totalRound: parseFloat(total.total_round),
          ggr: parseFloat(total.ggr),
          profitability: parseFloat(total.profitability),
        },
      };
    } catch (error) {
      this.logger.error(`[Error] Find history game: ${error.message}`);
      throw new HttpException(
        {
          errorCode: 2,
          errorMsg: "Erro ao buscar histórico de jogos",
        },
        HttpStatus.SERVICE_UNAVAILABLE
      );
    }
  }
}
