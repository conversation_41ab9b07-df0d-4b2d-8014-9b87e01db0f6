import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  Unique,
  UpdateDateColumn,
} from 'typeorm';

@Entity({ schema: 'casino', name: 'daily_report_game_player_partner' })
@Unique('daily_report_player_game_partner_date_uk', [
  'playerId',
  'gameId',
  'partnerId',
  'date',
])
export class DailyReportGamePlayerPartner {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid', { name: 'player_id', nullable: true })
  playerId: string;

  @Column('varchar', { name: 'game_id', nullable: true })
  gameId: string;

  @Column({ name: 'game_name', type: 'varchar', nullable: true })
  gameName: string;

  @Column({ name: 'game_provider', type: 'varchar', nullable: true })
  gameProvider: string;

  @Column({ type: 'varchar', nullable: true })
  currency: string;

  @Column('numeric', { name: 'total_bet', nullable: true })
  totalBet: number;

  @Column('numeric', { name: 'total_settle', nullable: true })
  totalSettle: number;

  @Column('numeric', { nullable: true })
  ggr: number;

  @Column('numeric', { name: 'total_round', nullable: true })
  totalRound: number;

  @Column({ type: 'varchar', nullable: true })
  email: string;

  @Column('uuid', { name: 'partner_id', nullable: true })
  partnerId: string;

  @Column({ type: 'date', nullable: true })
  date: Date;

  @Column({ name: 'game_provider_id', type: 'uuid', nullable: true })
  gameProviderId: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;
}
