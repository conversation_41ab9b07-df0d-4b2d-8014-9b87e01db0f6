import { Test, TestingModule } from '@nestjs/testing';
import { BalanceController } from './balance.controller';
import { BalanceService } from './balance.service';
import { HMACGuard } from '@/common/guards/hmac.guard';

describe('BalanceController', () => {
  let controller: BalanceController;

  const mockBalanceService = {};
  const mockHMACGuard = { canActivate: jest.fn(() => true) };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BalanceController],
      providers: [{ provide: BalanceService, useValue: mockBalanceService }],
    })
      .overrideGuard(HMACGuard)
      .useValue(mockHMACGuard)
      .compile();

    controller = module.get<BalanceController>(BalanceController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
