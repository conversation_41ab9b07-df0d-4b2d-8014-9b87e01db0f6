import {
  IsBoolean,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON><PERSON>al,
  IsString,
  IsUUI<PERSON>,
} from 'class-validator';
export class OperationDto {
  @IsNumber()
  amount: number;

  @IsString()
  reason: string;

  @IsBoolean()
  isBonusOperation: boolean;

  @IsString()
  gameId: string;

  @IsString()
  @IsOptional()
  gameProvider?: string;

  @IsString()
  @IsOptional()
  adjustmentType?: string;
}

export class OperationTransctionDto {
  @IsNumber()
  amount: number;

  @IsString()
  reason: string;

  @IsBoolean()
  isBonusOperation: boolean;

  @IsString()
  gameId: string;

  @IsString()
  @IsOptional()
  gameProvider?: string;

  @IsString()
  @IsOptional()
  adjustmentType?: string;

  @IsUUID()
  playerId: string;

  @IsUUID()
  partnerId: string;

  @IsString()
  origin: string;

  @IsString()
  accountType: string;

  @IsString()
  transactionId: string;
}
