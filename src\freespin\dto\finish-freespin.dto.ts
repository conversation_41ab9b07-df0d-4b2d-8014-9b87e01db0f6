import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsUUID } from 'class-validator';

export class FreeSpinsFinishDto {
  @ApiProperty({ example: '0.01' })
  @IsString()
  amount: string;

  @ApiProperty({ example: '02df2081-7e99-459a-a195-d65e9cff04e9' })
  @IsUUID()
  issue_id: string;

  @ApiProperty({ example: 'string' })
  @IsString()
  payload: string;

  @ApiProperty({ example: 'string' })
  @IsString()
  wallet_id: string;
}
