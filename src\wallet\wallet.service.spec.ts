import { Test, TestingModule } from '@nestjs/testing';
import { WalletService } from './wallet.service';
import { HttpService } from '@nestjs/axios';
import { HttpException } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';

jest.mock('rxjs', () => ({
  ...jest.requireActual('rxjs'),
  firstValueFrom: jest.fn(),
}));

describe('WalletService', () => {
  let service: WalletService;
  let httpService: any;

  const mockHttpService = {
    get: jest.fn(),
    post: jest.fn(),
  };

  beforeEach(async () => {
    (firstValueFrom as jest.Mock).mockReset();
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WalletService,
        { provide: HttpService, useValue: mockHttpService },
      ],
    }).compile();

    service = module.get<WalletService>(WalletService);
    httpService = module.get<HttpService>(HttpService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getBalanceByPlayer', () => {
    it('deve retornar o balance do player com sucesso', async () => {
      const mockResponse = {
        data: { subAccounts: [{ accountType: 'regular', balance: 100 }] },
      };
      (firstValueFrom as jest.Mock).mockResolvedValue(mockResponse);
      const result = await service.getBalanceByPlayer('partner-1', 'player-1');
      expect(result).toEqual({ accountType: 'regular', balance: 100 });
      expect(httpService.get).toHaveBeenCalled();
    });

    it('deve lançar HttpException se não encontrar balance', async () => {
      const mockResponse = { data: undefined };
      (firstValueFrom as jest.Mock).mockResolvedValue(mockResponse);
      await expect(
        service.getBalanceByPlayer('partner-1', 'player-1'),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('debit', () => {
    it('deve debitar com sucesso', async () => {
      const mockResponse = {
        data: { transactionId: 'tx-1', subAccount: { balance: 50 } },
      };
      (firstValueFrom as jest.Mock).mockResolvedValue(mockResponse);
      const operation = {
        amount: 10,
        reason: 'Bet',
        isBonusOperation: false,
        gameId: 'game-1',
      };
      const result = await service.debit(operation, 'partner-1', 'player-1');
      expect(result).toEqual(mockResponse.data);
      expect(httpService.post).toHaveBeenCalled();
    });

    it('deve lançar HttpException se ocorrer erro', async () => {
      (firstValueFrom as jest.Mock).mockRejectedValue({
        response: { data: { errorCode: 99, errorMsg: 'Erro' } },
      });
      const operation = {
        amount: 10,
        reason: 'Bet',
        isBonusOperation: false,
        gameId: 'game-1',
      };
      await expect(
        service.debit(operation, 'partner-1', 'player-1'),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('credit', () => {
    it('deve creditar com sucesso', async () => {
      const mockResponse = {
        data: { transactionId: 'tx-2', subAccount: { balance: 80 } },
      };
      (firstValueFrom as jest.Mock).mockResolvedValue(mockResponse);
      const operation = {
        amount: 20,
        reason: 'Credit',
        isBonusOperation: false,
        gameId: 'game-1',
      };
      const result = await service.credit(operation, 'partner-1', 'player-1');
      expect(result).toEqual(mockResponse.data);
      expect(httpService.post).toHaveBeenCalled();
    });

    it('deve lançar HttpException se ocorrer erro', async () => {
      (firstValueFrom as jest.Mock).mockRejectedValue({
        response: { data: { errorCode: 88, errorMsg: 'Erro credit' } },
      });
      const operation = {
        amount: 20,
        reason: 'Credit',
        isBonusOperation: false,
        gameId: 'game-1',
      };
      await expect(
        service.credit(operation, 'partner-1', 'player-1'),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('limit', () => {
    it('deve retornar o limite com sucesso', async () => {
      const mockResponse = { data: { limit: 1000 } };
      (firstValueFrom as jest.Mock).mockResolvedValue(mockResponse);
      const result = await service.limit('player-1', 'partner-1');
      expect(result).toEqual({ limit: 1000 });
      expect(httpService.post).toHaveBeenCalled();
    });

    it('deve lançar HttpException se ocorrer erro', async () => {
      (firstValueFrom as jest.Mock).mockRejectedValue({
        response: { data: { errorCode: 77, errorMsg: 'Erro limit' } },
      });
      await expect(service.limit('player-1', 'partner-1')).rejects.toThrow(
        HttpException,
      );
    });
  });
});
