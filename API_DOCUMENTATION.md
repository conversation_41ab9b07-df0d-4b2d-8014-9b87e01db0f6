# Documentação da API - Casino PAM

## Informações Gerais

- **URL Base**: `{URL_BASE}/v1/pam/casino/`
- **Versão**: 1.0.0
- **Formato de Resposta**: JSON

---

## 1. BETSETTLE

### 1.1 Liquidar Aposta

**Endpoint**: `POST {URL_BASE}/v1/pam/casino/betsettle`

**Descrição**: Endpoint para liquidar uma aposta e processar os ganhos

**Headers Obrigatórios**:
```
Content-Type: application/json
X-HMAC-Signature: {signature} (quando autenticação HMAC estiver ativa)
```

**Exemplo de Requisição**:
```json
{
  "sessionId": "session-123",
  "playerId": "player-123",
  "transactionId": "tx-123",
  "requestType": "RealMoney",
  "amount": 100.50,
  "winAmount": 150.75,
  "gameId": 123,
  "roundId": "round-123",
  "roundEnded": true,
  "isJackpot": false,
  "jackpotInfo": {
    "type": "mega",
    "amount": 10000
  },
  "freeSpinInfo": {
    "spins": 10,
    "value": 1.00
  },
  "wagerInfo": {
    "type": "normal",
    "multiplier": 1
  }
}
```

**Resposta de Sucesso (200)**:
```json
{
  "balance": 100.5,
  "casinoTransactionId": "123e4567-e89b-12d3-a456-************",
  "reconcileAmount": 100.5,
  "reconcileWinAmount": 100.5
}
```

**Possíveis Erros**:
- `200` - Sucesso
- `200` - Já processado (ALREADY_PROCESSED)
- `200` - Erro genérico (GENERIC_ERROR)
- `200` - Jogador não encontrado (PLAYER_NOT_FOUND)
- `200` - Sessão não encontrada (SESSION_NOT_FOUND)
- `200` - Jogo não encontrado (GAME_NOT_FOUND)
- `200` - Rodada não encontrada (ROUND_NOT_FOUND)
- `200` - Fundos insuficientes (INSUFFICIENT_FUNDS)
- `200` - Jogador bloqueado (PLAYER_BLOCKED)
- `200` - Limite de perda total (TOTAL_LOSS_LIMIT)
- `200` - Limite de aposta total (TOTAL_STAKE_LIMIT)
- `200` - Limite máximo de aposta (MAX_STAKE_LIMIT)
- `200` - Limite de tempo diário (DAILY_TIME_LIMIT)
- `200` - Limite de tempo semanal (WEEKLY_TIME_LIMIT)
- `200` - Limite de tempo mensal (MONTHLY_TIME_LIMIT)
- `200` - Auto-exclusão (SELF_EXCLUDED)

---

## 2. BALANCE

### 2.1 Consultar Saldo

**Endpoint**: `POST {URL_BASE}/v1/pam/casino/balance`

**Descrição**: Endpoint para consultar o saldo de um jogador

**Headers Obrigatórios**:
```
Content-Type: application/json
X-HMAC-Signature: {signature} (quando autenticação HMAC estiver ativa)
```

**Exemplo de Requisição**:
```json
{
  "playerId": "player123",
  "sessionId": "session456"
}
```

**Resposta de Sucesso (200)**:
```json
{
  "balance": 1000.5
}
```

**Possíveis Erros**:
- `200` - Sucesso
- `200` - Erro genérico (GENERIC_ERROR)
- `200` - Jogador não encontrado (PLAYER_NOT_FOUND)
- `200` - Sessão não encontrada (SESSION_NOT_FOUND)

---

## 3. GAMELAUNCH

### 3.1 Iniciar Jogo

**Endpoint**: `POST {URL_BASE}/v1/pam/casino/game-launch`

**Descrição**: Endpoint para iniciar um jogo em modo real ou free_play

**Headers Obrigatórios**:
```
Content-Type: application/json
Authorization: Bearer {jwt_token} (quando autenticação JWT estiver ativa)
```

**Exemplo de Requisição**:
```json
{
  "casinoId": "c4b5d2c7-8f44-45e1-87c8-8f4f02dcd21f",
  "game": "e3e54764-96b9-484e-946f-fb576e43850f",
  "locale": "en",
  "ip": "***********",
  "clientType": "desktop",
  "urls": {
    "returnUrl": "https://meusite.com/retorno",
    "depositUrl": "https://meusite.com/deposit"
  },
  "jurisdiction": "US-NJ",
  "player": {
    "id": "458f80a6-c9e8-46b6-802f-83d284b23051",
    "walletId": "a5b4c2d1-6f78-4d9a-8b12-34e5f67890ab",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "nickName": "John.Doe",
    "dateOfBirth": "1999-12-31T15:30:00Z",
    "registeredAt": "2020-12-31T15:30:00Z",
    "country": "DE",
    "tags": ["vip"],
    "currency": "EUR"
  }
}
```

**Resposta de Sucesso (200)**:
```json
{
  "launchToken": "12345",
  "launchUrl": "https://www.google.com"
}
```

**Possíveis Erros**:
- `200` - Sucesso
- `200` - Já processado (ALREADY_PROCESSED)
- `200` - Erro genérico (GENERIC_ERROR)
- `200` - Jogador não encontrado (PLAYER_NOT_FOUND)
- `200` - Sessão não encontrada (SESSION_NOT_FOUND)
- `200` - Jogo não encontrado (GAME_NOT_FOUND)
- `200` - Rodada não encontrada (ROUND_NOT_FOUND)
- `403` - Acesso negado (quando capabilities não permitidas)
- `500` - Erro interno do servidor

---

## 4. JACKPOT

### 4.1 Feed de Jackpot

**Endpoint**: `POST {URL_BASE}/v1/pam/casino/jackpot/feed`

**Descrição**: Endpoint para obter informações de jackpot

**Headers Obrigatórios**:
```
Content-Type: application/json
```

**Exemplo de Requisição**:
```json
{
  "casinoId": "casino_id_example"
}
```

**Resposta de Sucesso (200)**:
```json
{
  "games": [
    {
      "game_id": "softswiss:PlatinumLightning",
      "levels": [
        {
          "level": 1,
          "name": "sample_level",
          "data": [
            {
              "amount": "0.01",
              "currency": "EUR"
            }
          ]
        }
      ]
    }
  ]
}
```

---

## 5. FREESPIN

### 5.1 Emitir Free Spins

**Endpoint**: `POST {URL_BASE}/v1/pam/casino/freespin/issue`

**Descrição**: Endpoint para emitir free spins para um jogador

**Headers Obrigatórios**:
```
Content-Type: application/json
```

**Exemplo de Requisição**:
```json
{
  "casinoId": "sample_casino",
  "issueId": "f500b45b-75e5-416d-94f9-10c2a2e804f1",
  "games": ["sample_provider:sample_game"],
  "freespinsQuantity": 5,
  "betLevel": 3,
  "validUntil": "2025-12-31T15:30:00Z",
  "player": {
    "id": "458f80a6-c9e8-46b6-802f-83d284b23051",
    "walletId": "string",
    "email": "<EMAIL>",
    "firstname": "John",
    "lastname": "Doe",
    "nickname": "John.Doe",
    "dateOfBirth": "1999-12-31T15:30:00Z",
    "registeredAt": "2020-12-31T15:30:00Z",
    "country": "DE",
    "tags": ["vip"],
    "currency": "EUR"
  },
  "payload": "string"
}
```

### 5.2 Finalizar Free Spins

**Endpoint**: `POST {URL_BASE}/v1/pam/casino/freespin/finish`

**Descrição**: Endpoint para finalizar free spins e processar ganhos

**Headers Obrigatórios**:
```
Content-Type: application/json
```

**Exemplo de Requisição**:
```json
{
  "amount": "0.01",
  "issue_id": "02df2081-7e99-459a-a195-d65e9cff04e9",
  "payload": "string",
  "wallet_id": "string"
}
```

**Resposta de Sucesso (200)**:
```json
{
  "balance": "0.01"
}
```

### 5.3 Cancelar Free Spins

**Endpoint**: `POST {URL_BASE}/v1/pam/casino/freespin/cancel`

**Descrição**: Endpoint para cancelar free spins

**Headers Obrigatórios**:
```
Content-Type: application/json
```

**Exemplo de Requisição**:
```json
{
  "casinoId": "sample_casino",
  "issueId": "033ff48f-5016-4e8a-8f63-201cb3189fb5",
  "provider": "sample_provider"
}
```

---

## 6. FREECHIP

### 6.1 Emitir Free Chips

**Endpoint**: `POST {URL_BASE}/v1/pam/casino/freechip/issue`

**Descrição**: Endpoint para emitir free chips para um jogador

**Headers Obrigatórios**:
```
Content-Type: application/json
```

**Exemplo de Requisição**:
```json
{
  "issueId": "f500b45b-75e5-416d-94f9-10c2a2e804f1",
  "casinoId": "sample_casino",
  "chipValue": "0.01",
  "chipCount": 5,
  "validUntil": "2025-12-31T15:30:00Z",
  "games": ["sample_provider:sample_game"],
  "maxWin": "1000.00",
  "player": {
    "id": "458f80a6-c9e8-46b6-802f-83d284b23051",
    "walletId": "string",
    "email": "<EMAIL>",
    "firstname": "John",
    "lastname": "Doe",
    "nickname": "John.Doe",
    "dateOfBirth": "1999-12-31T15:30:00Z",
    "registeredAt": "2020-12-31T15:30:00Z",
    "country": "DE",
    "tags": ["vip"],
    "currency": "EUR"
  },
  "payload": "string"
}
```

### 6.2 Finalizar Free Chips

**Endpoint**: `POST {URL_BASE}/v1/pam/casino/freechip/finish`

**Descrição**: Endpoint para finalizar free chips e processar ganhos

**Headers Obrigatórios**:
```
Content-Type: application/json
```

**Exemplo de Requisição**:
```json
{
  "amount": "0.01",
  "issue_id": "02df2081-7e99-459a-a195-d65e9cff04e9",
  "payload": "string",
  "wallet_id": "string"
}
```

**Resposta de Sucesso (200)**:
```json
{
  "balance": "0.01"
}
```

### 6.3 Cancelar Free Chips

**Endpoint**: `POST {URL_BASE}/v1/pam/casino/freechip/cancel`

**Descrição**: Endpoint para cancelar free chips

**Headers Obrigatórios**:
```
Content-Type: application/json
```

**Exemplo de Requisição**:
```json
{
  "casinoId": "sample_provider",
  "issueId": "02df2081-7e99-459a-a195-d65e9cff04e9",
  "provider": "sample_provider"
}
```

---

## 7. BONUS

### 7.1 Emitir Bônus

**Endpoint**: `POST {URL_BASE}/v1/pam/casino/bonus/issue`

**Descrição**: Endpoint para emitir bônus para um jogador

**Headers Obrigatórios**:
```
Content-Type: application/json
```

**Exemplo de Requisição**:
```json
{
  "issueId": "f500b45b-75e5-416d-94f9-10c2a2e804f1",
  "casinoId": "sample_casino",
  "bonusType": "BonusFeature",
  "validUntil": "2025-12-31T15:30:00Z",
  "games": ["sample_provider:sample_game"],
  "betLevel": 3,
  "player": {
    "id": "458f80a6-c9e8-46b6-802f-83d284b23051",
    "walletId": "string",
    "email": "<EMAIL>",
    "firstname": "John",
    "lastname": "Doe",
    "nickname": "John.Doe",
    "dateOfBirth": "1999-12-31T15:30:00Z",
    "registeredAt": "2020-12-31T15:30:00Z",
    "country": "DE",
    "tags": ["vip"],
    "currency": "EUR"
  },
  "payload": "string"
}
```

### 7.2 Finalizar Bônus

**Endpoint**: `POST {URL_BASE}/v1/pam/casino/bonus/finish`

**Descrição**: Endpoint para finalizar bônus e processar ganhos

**Headers Obrigatórios**:
```
Content-Type: application/json
```

**Exemplo de Requisição**:
```json
{
  "amount": "0.01",
  "issue_id": "02df2081-7e99-459a-a195-d65e9cff04e9",
  "payload": "string",
  "round_id": "xz1-3poiu1nwn-2871531922_1425374859601_0",
  "wallet_id": "string"
}
```

**Resposta de Sucesso (200)**:
```json
{
  "balance": "0.01"
}
```

### 7.3 Cancelar Bônus

**Endpoint**: `POST {URL_BASE}/v1/pam/casino/bonus/cancel`

**Descrição**: Endpoint para cancelar bônus

**Headers Obrigatórios**:
```
Content-Type: application/json
```

**Exemplo de Requisição**:
```json
{
  "casinoId": "sample_casino",
  "issueId": "02df2081-7e99-459a-a195-d65e9cff04e9",
  "provider": "sample_provider"
}
```

### 7.4 Tipos de Bônus

**Endpoint**: `POST {URL_BASE}/v1/pam/casino/bonus/types`

**Descrição**: Endpoint para obter tipos de bônus disponíveis

**Headers Obrigatórios**:
```
Content-Type: application/json
```

**Exemplo de Requisição**:
```json
{
  "casinoId": "sample_casino",
  "providers": ["softswiss", "pragmaticexternal"]
}
```

**Resposta de Sucesso (200)**:
```json
{
  "bonus_types": [
    {
      "name": "MegaBonus",
      "description": "Mega mega bonus",
      "provider": "sample_provider",
      "games": [
        {
          "game": "sample_provider:sample_game",
          "cost_multiplier": "20.5"
        }
      ]
    }
  ]
}
```

---

## Códigos de Status HTTP

- **200**: Sucesso (mesmo para erros de negócio)
- **403**: Acesso negado (quando capabilities não permitidas)
- **500**: Erro interno do servidor

## Observações Importantes

1. **Autenticação**: Alguns endpoints possuem autenticação HMAC ou JWT que podem estar desabilitadas (comentadas no código)
2. **Formato de Resposta**: Todos os erros de negócio retornam status 200 com códigos específicos no corpo da resposta
3. **Validação**: Todos os endpoints possuem validação de entrada usando class-validator
4. **Swagger**: A documentação Swagger está disponível em `{URL_BASE}/v1/pam/casino/doc`
5. **CORS**: A API está configurada para aceitar requisições de qualquer origem
6. **Body Parser**: A API utiliza body-parser para processar requisições JSON
