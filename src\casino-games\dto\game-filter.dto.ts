import { GenericFilter } from '@/common/filters/generic-filter.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';

export class GameFilterDto extends GenericFilter {
  @ApiPropertyOptional({
    description: 'Filtro por ID do jogo',
    example: 'a1b2c3d4-e5f6-4a5b-8c9d-0e1f2a3b4c5d',
  })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiPropertyOptional({
    description: 'Filtro por nome do jogo (busca parcial)',
    example: 'Fortune Tiger',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description:
      'Filtro por IDs de categorias (aceita múltiplos IDs separados por vírgula)',
    example: 'uuid1,uuid2,uuid3',
    type: String,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').map(id => id.trim());
    }
    return value;
  })
  @IsArray()
  gameCategoryId?: string[];

  @ApiPropertyOptional({
    description:
      'Filtro por IDs de provedores (aceita múltiplos IDs separados por vírgula)',
    example: 'uuid1,uuid2,uuid3',
    type: String,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').map(id => id.trim());
    }
    return value;
  })
  @IsArray()
  gameProviderId?: string[];

  @ApiPropertyOptional({
    description: 'Filtro por status do jogo (habilitado/desabilitado)',
    example: false,
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  isDisable?: boolean;
}
