import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { CasinoGames } from '../entities/casino-game.entity';
import { UpdateCasinoGameDto } from '../dto/update-casino-game.dto';
import { CreateCasinoGameDto } from '../dto/create-casino-game.dto';
import { GenericFilter } from '../../common/filters/generic-filter.dto';

@Injectable()
export class BackofficeCasinoGamesService {
  private readonly logger = new Logger(BackofficeCasinoGamesService.name);
  constructor(
    @InjectEntityManager() private readonly casinoManager: EntityManager
  ) {}

  async create(createCasinoGameDto: CreateCasinoGameDto): Promise<string> {
    try {
      this.logger.log(
        `[Inicio] Create casino game: ${JSON.stringify(createCasinoGameDto)}`
      );
      await this.casinoManager.insert(CasinoGames, createCasinoGameDto);
      this.logger.log(
        `[Fim] Create casino game: ${JSON.stringify(createCasinoGameDto)}`
      );
      return 'The record has been successfully created.';
    } catch (err) {
      this.logger.error(`Error creating casino game: ${err}`);
      if (err instanceof HttpException) throw err;
      throw new HttpException(err?.message || 'Internal error', err?.status || HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async findAll(
    req
  ): Promise<CasinoGames[]> {
    try {
      this.logger.log(
        `[Inicio] Find all casino games}`
      );
      const listGame = await this.casinoManager.find(CasinoGames, {
        select: ['id', 'name', 'description', 'gameId', 'gameProvider'],
      });
     return listGame
    } catch (err) {
      this.logger.error(`Error finding casino games: ${err}`);
      if (err instanceof HttpException) throw err;
      throw new HttpException(err?.message || 'Internal error', err?.status || HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async findOne(id: string): Promise<CasinoGames> {
    try {
      this.logger.log(`[Inicio] Find one casino game: ${id}`);
      const findOneCasinoGame = await this.casinoManager.findOne(CasinoGames, {
        where: { id },
      });
      if (!findOneCasinoGame) {
        this.logger.warn(`No records found`);
        throw new HttpException('No records found', HttpStatus.NO_CONTENT);
      }
      this.logger.log(`[Fim] Find one casino game: ${id}`);
      return findOneCasinoGame;
    } catch (err) {
      this.logger.error(`Error finding casino game: ${err}`);
      if (err instanceof HttpException) throw err;
      throw new HttpException(err?.message || 'Internal error', err?.status || HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async update(
    id: string,
    updateCasinoGameDto: UpdateCasinoGameDto
  ): Promise<string> {
    try {
      this.logger.log(
        `[Inicio] Update casino game: ${JSON.stringify(updateCasinoGameDto)}`
      );
      await this.casinoManager.transaction(async (casinoTransaction) => {
        const updateCasinoGame = await casinoTransaction.update(
          CasinoGames,
          id,
          updateCasinoGameDto
        );
        return updateCasinoGame;
      });
      this.logger.log(
        `[Fim] Update casino game: ${JSON.stringify(updateCasinoGameDto)}`
      );
      return 'The record has been successfully updated.';
    } catch (err) {
      this.logger.error(`Error updating casino game: ${err}`);
      if (err instanceof HttpException) throw err;
      throw new HttpException(err?.message || 'Internal error', err?.status || HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async remove(id: string) {
    try {
      this.logger.log(`[Inicio] Remove casino game: ${id}`);
      await this.casinoManager.transaction(async (casinoTransaction) => {
        const removeCasinoGame = await casinoTransaction.softDelete(
          CasinoGames,
          id
        );
        return removeCasinoGame;
      });
      this.logger.log(`[Fim] Remove casino game: ${id}`);
      return 'The record has been successfully deleted.';
    } catch (err) {
      this.logger.error(`Error removing casino game: ${err}`);
      if (err instanceof HttpException) throw err;
      throw new HttpException(err?.message || 'Internal error', err?.status || HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getAllProvides(): Promise<string[]> {
    this.logger.log('Get all providers');
    const prividers = await this.casinoManager.query<CasinoGames[]>(
      'SELECT DISTINCT game_provider as "gameProvider" FROM casino.casino_games'
    );
    return prividers
      .map((provider) => provider.gameProvider)
      .sort((a, b) => a.localeCompare(b));
  }
}
