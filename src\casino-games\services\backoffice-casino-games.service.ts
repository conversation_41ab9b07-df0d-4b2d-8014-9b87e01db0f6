import { S3Service } from '@/common/services/aws/s3.service';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { CreateCasinoGameDto } from '../dto/create-casino-game.dto';
import { UpdateCasinoGameDto } from '../dto/update-casino-game.dto';
import { ProviderFilterDto } from '../dto/provider-filter.dto';
import { CasinoGameProvider } from '../entities/casino-game-provider.entity';
import { CasinoGames } from '../entities/casino-game.entity';
import { CasinoGamesCategories } from '../entities/casino-games-categories.entity';
import { SortOrder } from '@/common/filters/sortOrder';

@Injectable()
export class BackofficeCasinoGamesService {
  private readonly logger = new Logger(BackofficeCasinoGamesService.name);
  constructor(
    @InjectEntityManager() private readonly casinoManager: EntityManager,
    private readonly s3Service: S3Service,
  ) {}

  async create(
    createCasinoGameDto: CreateCasinoGameDto,
    image?: Express.Multer.File,
  ): Promise<{ message: string }> {
    try {
      this.logger.log(
        `[Inicio] Create casino game: ${JSON.stringify(createCasinoGameDto.name)}`,
      );

      const customFileName = this.generateCustomFileName(
        createCasinoGameDto.gameId,
        createCasinoGameDto.gameProvider,
        createCasinoGameDto.name,
      );

      if (image) {
        this.logger.log(`Uploading image file to S3: ${image.originalname}`);
        const uploadResult = await this.s3Service.uploadFile(
          image.buffer,
          image.originalname,
          'casino_games',
          customFileName,
        );
        createCasinoGameDto.image = uploadResult.url;
        createCasinoGameDto['path'] = uploadResult.path;
        this.logger.log(`Image file uploaded successfully: ${uploadResult.url}`);
      } else if (createCasinoGameDto.imageBase64) {
        this.logger.log(`Uploading base64 image to S3`);
        const uploadResult = await this.s3Service.uploadBase64File(
          createCasinoGameDto.imageBase64,
          'casino_games',
          customFileName,
        );
        createCasinoGameDto.image = uploadResult.url;
        createCasinoGameDto['path'] = uploadResult.path;
        this.logger.log(`Base64 image uploaded successfully: ${uploadResult.url}`);
      }

      delete createCasinoGameDto.imageBase64;

      await this.casinoManager.insert(CasinoGames, createCasinoGameDto);
      this.logger.log(
        `[Fim] Create casino game: ${JSON.stringify(createCasinoGameDto)}`,
      );
      return { message: 'The record has been successfully created.' };
    } catch (err) {
      this.logger.error(`Error creating casino game: ${err}`);
      throw new HttpException(
        err.message,
        err.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findAll(req): Promise<CasinoGames[]> {
    try {
      this.logger.log(`[Inicio] Find all casino games}`);

      const listGame = await this.casinoManager
        .createQueryBuilder(CasinoGames, 'casino_games')
        .select('casino_games.id', 'id')
        .addSelect('casino_games.name', 'name')
        .addSelect('casino_games.description', 'description')
        .addSelect('casino_games.gameId', 'gameId')
        .addSelect('casino_games.gameProvider', 'gameProvider')
        .addSelect('casino_games.gameCategoryId', 'gameCategoryId')
        .addSelect('casino_games.gameProviderId', 'gameProviderId')
        .addSelect('casino_game_providers.name', 'gameProviderName')
        .addSelect('casino_game_categories.name', 'gameCategoryName')
        .leftJoin(
          CasinoGameProvider,
          'casino_game_providers',
          'casino_games.game_provider_id = casino_game_providers.id',
        )
        .leftJoin(
          CasinoGamesCategories,
          'casino_game_categories',
          'casino_games.game_category_id = casino_game_categories.id',
        )
        .getRawMany();
      return listGame;
    } catch (err) {
      this.logger.error(`Error finding casino games: ${err}`);
      throw new HttpException(err.message, err.status);
    }
  }

  async findOne(id: string): Promise<CasinoGames> {
    try {
      this.logger.log(`[Inicio] Find one casino game: ${id}`);
      const findOneCasinoGame = await this.casinoManager
        .createQueryBuilder(CasinoGames, 'casino_games')
        .select('casino_games.id', 'id')
        .addSelect('casino_games.name', 'name')
        .addSelect('casino_games.description', 'description')
        .addSelect('casino_games.gameId', 'gameId')
        .addSelect('casino_games.gameProvider', 'gameProvider')
        .addSelect('casino_games.gameCategoryId', 'gameCategoryId')
        .addSelect('casino_games.gameProviderId', 'gameProviderId')
        .addSelect('casino_game_providers.name', 'gameProviderName')
        .addSelect('casino_game_categories.name', 'gameCategoryName')
        .leftJoin(
          CasinoGameProvider,
          'casino_game_providers',
          'casino_games.game_provider_id = casino_game_providers.id',
        )
        .leftJoin(
          CasinoGamesCategories,
          'casino_game_categories',
          'casino_games.game_category_id = casino_game_categories.id',
        )
        .where('casino_games.id = :id', { id })
        .getRawOne();
      if (!findOneCasinoGame) {
        this.logger.warn(`No records found`);
        throw new HttpException('No records found', HttpStatus.NO_CONTENT);
      }
      this.logger.log(`[Fim] Find one casino game: ${id}`);
      return findOneCasinoGame;
    } catch (err) {
      this.logger.error(`Error finding casino game: ${err}`);
      throw new HttpException(err.message, err.status);
    }
  }

  async update(
    id: string,
    updateCasinoGameDto: UpdateCasinoGameDto,
    image?: Express.Multer.File,
  ): Promise<string> {
    try {
      this.logger.log(
        `[Inicio] Update casino game: ${JSON.stringify(updateCasinoGameDto)}`,
      );

      await this.casinoManager.transaction(async casinoTransaction => {
        if (image || updateCasinoGameDto.imageBase64) {
          const existingGame = await casinoTransaction.findOne(CasinoGames, {
            where: { id },
          });

          const gameId = updateCasinoGameDto.gameId || existingGame?.gameId;
          const gameProvider =
            updateCasinoGameDto.gameProvider || existingGame?.gameProvider;
          const gameName = updateCasinoGameDto.name || existingGame?.name;

          const customFileName = this.generateCustomFileName(
            gameId,
            gameProvider,
            gameName,
          );

          let uploadResult: { url: string; path: string };

          if (image) {
            this.logger.log(
              `Uploading new image file to S3: ${image.originalname}`,
            );
            uploadResult = await this.s3Service.uploadFile(
              image.buffer,
              image.originalname,
              'casino_games',
              customFileName,
            );
            this.logger.log(
              `New image file uploaded successfully: ${uploadResult.url}`,
            );
          } else if (updateCasinoGameDto.imageBase64) {
            this.logger.log(`Uploading new base64 image to S3`);
            uploadResult = await this.s3Service.uploadBase64File(
              updateCasinoGameDto.imageBase64,
              'casino_games',
              customFileName,
            );
            this.logger.log(
              `New base64 image uploaded successfully: ${uploadResult.url}`,
            );
          }

          updateCasinoGameDto.image = uploadResult.url;
          updateCasinoGameDto['path'] = uploadResult.path;

          if (existingGame?.image) {
            try {
              await this.s3Service.deleteFileByUrl(existingGame.image);
              this.logger.log(
                `Old image deleted from S3: ${existingGame.image}`,
              );
            } catch (deleteErr) {
              this.logger.warn(
                `Failed to delete old image from S3: ${deleteErr.message}`,
              );
              // Não falhar a operação se a deleção da imagem antiga falhar
            }
          }
        }

        delete updateCasinoGameDto.imageBase64;

        const updateCasinoGame = await casinoTransaction.update(
          CasinoGames,
          id,
          updateCasinoGameDto,
        );
        return updateCasinoGame;
      });

      this.logger.log(
        `[Fim] Update casino game: ${JSON.stringify(updateCasinoGameDto)}`,
      );
      return 'The record has been successfully updated.';
    } catch (err) {
      this.logger.error(`Error updating casino game: ${err}`);
      throw new HttpException(err.message, err.status);
    }
  }

  async remove(id: string) {
    try {
      this.logger.log(`[Inicio] Remove casino game: ${id}`);
      await this.casinoManager.transaction(async casinoTransaction => {
        const removeCasinoGame = await casinoTransaction.softDelete(
          CasinoGames,
          id,
        );
        return removeCasinoGame;
      });
      this.logger.log(`[Fim] Remove casino game: ${id}`);
      return 'The record has been successfully deleted.';
    } catch (err) {
      this.logger.error(`Error removing casino game: ${err}`);
      throw new HttpException(err.message, err.status);
    }
  }

  async getAllProvides(
    filter: ProviderFilterDto,
  ): Promise<{
    data: Array<{ id: string; name: string }>;
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    try {
      this.logger.log(
        `[Inicio] Get all providers: ${JSON.stringify(filter)}`,
      );

      if (!filter.sortOrder) {
        filter.sortOrder = SortOrder.ASC;
      }
      const page = !filter.page || filter.page < 1 ? 1 : filter.page;
      const pageSize =
        !filter.pageSize || filter.pageSize < 1 ? 10 : filter.pageSize;

      const countQueryBuilder = this.casinoManager
        .createQueryBuilder(CasinoGameProvider, 'providers')
        .where('providers.isActive = :isActive', { isActive: true });

      const queryBuilder = this.casinoManager
        .createQueryBuilder(CasinoGameProvider, 'providers')
        .select('providers.id', 'id')
        .addSelect('providers.name', 'name')
        .where('providers.isActive = :isActive', { isActive: true });

      if (filter.id) {
        queryBuilder.andWhere('providers.id = :id', { id: filter.id });
        countQueryBuilder.andWhere('providers.id = :id', { id: filter.id });
      }

      if (filter.name) {
        queryBuilder.andWhere('providers.name ILIKE :name', {
          name: `%${filter.name}%`,
        });
        countQueryBuilder.andWhere('providers.name ILIKE :name', {
          name: `%${filter.name}%`,
        });
      }

      const totalItems = await countQueryBuilder.getCount();

      const totalPages = Math.ceil(totalItems / pageSize);

      const providers = await queryBuilder
        .orderBy('providers.name', filter.sortOrder)
        .skip((page - 1) * pageSize)
        .take(pageSize)
        .getRawMany();

      this.logger.log(
        `[Fim] Get all providers: ${JSON.stringify(filter)}`,
      );

      return {
        data: providers,
        totalItems,
        totalPages,
        currentPage: page,
        pageSize,
      };
    } catch (err) {
      this.logger.error(`Error getting providers: ${err}`);
      throw new HttpException(
        err.message,
        err.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private generateCustomFileName(
    gameId: string,
    gameProvider: string,
    gameName: string,
  ): string {
    const cleanGameId = gameId.replace(/^-/, '');
    const cleanProvider = gameProvider.toLowerCase().replace(/\s+/g, '_');
    const cleanGameName = gameName.toLowerCase().replace(/\s+/g, '_');

    return `${cleanGameId}_${cleanProvider}_${cleanGameName}`;
  }
}
