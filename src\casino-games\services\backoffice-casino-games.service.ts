import { PaginationResponseDto } from '@/common/dto/paginated-response.dto';
import { SortOrder } from '@/common/filters/sortOrder';
import { IBackofficeUser } from '@/common/interfaces/backoffice-user.interface';
import { S3Service } from '@/common/services/aws/s3.service';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { CategoryFilterDto } from '../dto/category-filter.dto';
import { CreateCasinoGameDto } from '../dto/create-casino-game.dto';
import { GameFilterDto } from '../dto/game-filter.dto';
import { ProviderFilterDto } from '../dto/provider-filter.dto';
import { UpdateCasinoGameDto } from '../dto/update-casino-game.dto';
import { CasinoGameProvider } from '../entities/casino-game-provider.entity';
import { CasinoGames } from '../entities/casino-game.entity';
import { CasinoGamesCategories } from '../entities/casino-games-categories.entity';
import { CasinoLogHistoryService } from './casino-log-history.service';

@Injectable()
export class BackofficeCasinoGamesService {
  private readonly logger = new Logger(BackofficeCasinoGamesService.name);
  constructor(
    @InjectEntityManager() private readonly casinoManager: EntityManager,
    private readonly s3Service: S3Service,
    private readonly logHistoryService: CasinoLogHistoryService,
  ) {}

  async create(
    createCasinoGameDto: CreateCasinoGameDto,
    image?: Express.Multer.File,
    imageAggregator?: Express.Multer.File,
  ): Promise<{ message: string }> {
    try {
      this.logger.log(
        `[Inicio] Create casino game: ${JSON.stringify(createCasinoGameDto.name)}`,
      );

      // Validar unicidade da posição se foi fornecida
      if (
        createCasinoGameDto.position !== undefined &&
        createCasinoGameDto.position !== null
      ) {
        const gameWithSamePosition = await this.casinoManager.findOne(
          CasinoGames,
          {
            where: { position: createCasinoGameDto.position },
          },
        );

        if (gameWithSamePosition) {
          throw new HttpException(
            `Position ${createCasinoGameDto.position} is already in use by another game: ${gameWithSamePosition.name}`,
            HttpStatus.CONFLICT,
          );
        }
      }

      const customFileName = this.generateCustomFileName(
        createCasinoGameDto.gameId,
        createCasinoGameDto.gameProvider,
        createCasinoGameDto.name,
      );

      // Upload da imagem principal
      if (image) {
        this.logger.log(`Uploading image file to S3: ${image.originalname}`);
        const uploadResult = await this.s3Service.uploadFile(
          image.buffer,
          image.originalname,
          'casino_games',
          customFileName,
        );
        createCasinoGameDto.image = uploadResult.url;
        createCasinoGameDto['path'] = uploadResult.path;
        this.logger.log(
          `Image file uploaded successfully: ${uploadResult.url}`,
        );
      } else if (createCasinoGameDto.imageBase64) {
        this.logger.log(`Uploading base64 image to S3`);
        const uploadResult = await this.s3Service.uploadBase64File(
          createCasinoGameDto.imageBase64,
          'casino_games',
          customFileName,
        );
        createCasinoGameDto.image = uploadResult.url;
        createCasinoGameDto['path'] = uploadResult.path;
        this.logger.log(
          `Base64 image uploaded successfully: ${uploadResult.url}`,
        );
      }

      // Upload da imagem do aggregator
      if (imageAggregator) {
        this.logger.log(
          `Uploading aggregator image file to S3: ${imageAggregator.originalname}`,
        );
        const uploadResult = await this.s3Service.uploadFile(
          imageAggregator.buffer,
          imageAggregator.originalname,
          'casino_games',
          `${customFileName}_aggregator`,
        );
        createCasinoGameDto.imageAggregator = uploadResult.url;
        createCasinoGameDto['pathAggregator'] = uploadResult.path;
        this.logger.log(
          `Aggregator image file uploaded successfully: ${uploadResult.url}`,
        );
      } else if (createCasinoGameDto.imageBase64Aggregator) {
        this.logger.log(`Uploading base64 aggregator image to S3`);
        const uploadResult = await this.s3Service.uploadBase64File(
          createCasinoGameDto.imageBase64Aggregator,
          'casino_games',
          `${customFileName}_aggregator`,
        );
        createCasinoGameDto.imageAggregator = uploadResult.url;
        createCasinoGameDto['pathAggregator'] = uploadResult.path;
        this.logger.log(
          `Base64 aggregator image uploaded successfully: ${uploadResult.url}`,
        );
      }

      delete createCasinoGameDto.imageBase64;
      delete createCasinoGameDto.imageBase64Aggregator;

      await this.casinoManager.insert(CasinoGames, createCasinoGameDto);
      this.logger.log(
        `[Fim] Create casino game: ${JSON.stringify(createCasinoGameDto)}`,
      );
      return { message: 'The record has been successfully created.' };
    } catch (err) {
      this.logger.error(`Error creating casino game: ${err}`);
      throw new HttpException(
        err.message,
        err.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findAll(_req): Promise<CasinoGames[]> {
    try {
      this.logger.log(`[Inicio] Find all casino games}`);

      const listGame = await this.casinoManager
        .createQueryBuilder(CasinoGames, 'casino_games')
        .select('casino_games.id', 'id')
        .addSelect('casino_games.name', 'name')
        .addSelect('casino_games.description', 'description')
        .addSelect('casino_games.gameId', 'gameId')
        .addSelect('casino_games.gameProvider', 'gameProvider')
        .addSelect('casino_games.gameCategoryId', 'gameCategoryId')
        .addSelect('casino_games.gameProviderId', 'gameProviderId')
        .addSelect('casino_game_providers.name', 'gameProviderName')
        .addSelect('casino_game_categories.name', 'gameCategoryName')
        .leftJoin(
          CasinoGameProvider,
          'casino_game_providers',
          'casino_games.game_provider_id = casino_game_providers.id',
        )
        .leftJoin(
          CasinoGamesCategories,
          'casino_game_categories',
          'casino_games.game_category_id = casino_game_categories.id',
        )
        .getRawMany();
      return listGame;
    } catch (err) {
      this.logger.error(`Error finding casino games: ${err}`);
      throw new HttpException(err.message, err.status);
    }
  }

  async findGamePagination(
    filters: GameFilterDto,
  ): Promise<PaginationResponseDto<CasinoGames>> {
    try {
      this.logger.log(`[Inicio] Find all casino games paginated`);

      // Configuração padrão de ordenação e paginação
      const page = !filters.page || filters.page < 1 ? 1 : filters.page;
      const pageSize =
        !filters.pageSize || filters.pageSize < 1 ? 10 : filters.pageSize;
      const offset = (page - 1) * pageSize;

      const orderBy = [
        'id',
        'name',
        'gameProviderName',
        'position',
        'createdAt',
        'isDisable',
        'gameCategoryName',
      ].includes(filters.orderBy)
        ? filters.orderBy
        : 'name';
      const sortOrder = filters.sortOrder || SortOrder.ASC;

      const orderByMap = {
        id: 'casino_games.id',
        name: 'casino_games.name',
        position: 'casino_games.position',
        isDisable: 'casino_games.isDisabled',
        createdAt: 'casino_games.createdAt',
        gameProviderName: 'casino_game_providers.name',
        gameCategoryName: 'casino_game_categories.name',
      };

      const orderByColumn = orderByMap[orderBy] || orderByMap.name;

      // Query builder para contagem total
      const countQueryBuilder = this.casinoManager
        .createQueryBuilder(CasinoGames, 'casino_games')
        .leftJoin(
          CasinoGameProvider,
          'casino_game_providers',
          'casino_games.game_provider_id = casino_game_providers.id',
        )
        .leftJoin(
          CasinoGamesCategories,
          'casino_game_categories',
          'casino_games.game_category_id = casino_game_categories.id',
        );

      // Query builder principal
      const queryBuilder = this.casinoManager
        .createQueryBuilder(CasinoGames, 'casino_games')
        .select('casino_games.id', 'id')
        .addSelect('casino_games.name', 'name')
        .addSelect('casino_games.description', 'description')
        .addSelect('casino_games.gameId', 'gameId')
        .addSelect('casino_games.gameProvider', 'gameProvider')
        .addSelect('casino_games.isDisable', 'isDisable')
        .addSelect('casino_games.image', 'image')
        .addSelect('casino_games.path', 'path')
        .addSelect('casino_games.imageAggregator', 'imageAggregator')
        .addSelect('casino_games.pathAggregator', 'pathAggregator')
        .addSelect('casino_games.createdAt', 'createdAt')
        .addSelect('casino_games.gameProviderId', 'gameProviderId')
        .addSelect('casino_games.gameCategoryId', 'gameCategoryId')
        .addSelect('casino_games.isDesktop', 'isDesktop')
        .addSelect('casino_games.isMobile', 'isMobile')
        .addSelect('casino_games.position', 'position')
        .addSelect('casino_games.lastUserModify', 'lastUserModify')
        .addSelect('casino_game_providers.name', 'gameProviderName')
        .addSelect('casino_game_categories.name', 'gameCategoryName')
        .addSelect(
          'COALESCE(casino_game_providers.is_active = false, false)',
          'providerDisabled',
        )
        .addSelect(
          'COALESCE(casino_game_categories.is_active = false, false)',
          'categoryDisabled',
        )
        .leftJoin(
          CasinoGameProvider,
          'casino_game_providers',
          'casino_games.game_provider_id = casino_game_providers.id',
        )
        .leftJoin(
          CasinoGamesCategories,
          'casino_game_categories',
          'casino_games.game_category_id = casino_game_categories.id',
        );

      // Aplicar filtros
      if (filters.id) {
        queryBuilder.andWhere('casino_games.id = :id', { id: filters.id });
        countQueryBuilder.andWhere('casino_games.id = :id', { id: filters.id });
      }

      if (filters.name) {
        queryBuilder.andWhere('casino_games.name ILIKE :name', {
          name: `%${filters.name}%`,
        });
        countQueryBuilder.andWhere('casino_games.name ILIKE :name', {
          name: `%${filters.name}%`,
        });
      }

      if (filters.gameCategoryId && filters.gameCategoryId.length > 0) {
        queryBuilder.andWhere(
          'casino_games.game_category_id IN (:...gameCategoryIds)',
          { gameCategoryIds: filters.gameCategoryId },
        );
        countQueryBuilder.andWhere(
          'casino_games.game_category_id IN (:...gameCategoryIds)',
          { gameCategoryIds: filters.gameCategoryId },
        );
      }

      if (filters.gameProviderId && filters.gameProviderId.length > 0) {
        queryBuilder.andWhere(
          'casino_games.game_provider_id IN (:...gameProviderIds)',
          { gameProviderIds: filters.gameProviderId },
        );
        countQueryBuilder.andWhere(
          'casino_games.game_provider_id IN (:...gameProviderIds)',
          { gameProviderIds: filters.gameProviderId },
        );
      }

      if (filters.isDisable !== undefined && filters.isDisable !== null) {
        queryBuilder.andWhere('casino_games.is_disabled = :isDisable', {
          isDisable: filters.isDisable,
        });
        countQueryBuilder.andWhere('casino_games.is_disabled = :isDisable', {
          isDisable: filters.isDisable,
        });
      }

      // Contagem total de itens
      const totalItems = await countQueryBuilder.getCount();
      const totalPages = Math.ceil(totalItems / pageSize);
      // Aplicar ordenação e paginação
      const rawGames = await queryBuilder
        .orderBy(orderByColumn, sortOrder)
        .limit(pageSize)
        .offset(offset)
        .getRawMany();
        
      const games = rawGames.map((row: Record<string, unknown>) => {
        const { providerdisabled, categorydisabled, ...rest } = row;
        return {
          ...rest,
          providerDisabled: Boolean(
            providerdisabled ?? row.providerDisabled ?? false,
          ),
          categoryDisabled: Boolean(
            categorydisabled ?? row.categoryDisabled ?? false,
          ),
        };
      });

      this.logger.log(`[Fim] Find all casino games paginated`);

      return new PaginationResponseDto<CasinoGames>({
        data: games as unknown as CasinoGames[],
        totalItems,
        currentPage: page,
        pageSize,
        totalPages,
      });
    } catch (err) {
      this.logger.error(`Error finding casino games: ${err}`);
      throw new HttpException(
        err.message || 'Internal server error',
        err.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findOne(id: string): Promise<CasinoGames> {
    try {
      this.logger.log(`[Inicio] Find one casino game: ${id}`);
      const findOneCasinoGame = await this.casinoManager
        .createQueryBuilder(CasinoGames, 'casino_games')
        .select('casino_games.id', 'id')
        .addSelect('casino_games.name', 'name')
        .addSelect('casino_games.description', 'description')
        .addSelect('casino_games.gameId', 'gameId')
        .addSelect('casino_games.gameProvider', 'gameProvider')
        .addSelect('casino_games.gameCategoryId', 'gameCategoryId')
        .addSelect('casino_games.gameProviderId', 'gameProviderId')
        .addSelect('casino_game_providers.name', 'gameProviderName')
        .addSelect('casino_game_categories.name', 'gameCategoryName')
        .leftJoin(
          CasinoGameProvider,
          'casino_game_providers',
          'casino_games.game_provider_id = casino_game_providers.id',
        )
        .leftJoin(
          CasinoGamesCategories,
          'casino_game_categories',
          'casino_games.game_category_id = casino_game_categories.id',
        )
        .where('casino_games.id = :id', { id })
        .getRawOne();
      if (!findOneCasinoGame) {
        this.logger.warn(`No records found`);
        throw new HttpException('No records found', HttpStatus.NO_CONTENT);
      }
      this.logger.log(`[Fim] Find one casino game: ${id}`);
      return findOneCasinoGame;
    } catch (err) {
      this.logger.error(`Error finding casino game: ${err}`);
      throw new HttpException(err.message, err.status);
    }
  }

  async update(
    id: string,
    updateCasinoGameDto: UpdateCasinoGameDto,
    backofficeUser: IBackofficeUser,
    image?: Express.Multer.File,
    imageAggregator?: Express.Multer.File,
  ): Promise<{ message: string }> {
    try {
      this.logger.log(
        `[Inicio] Update casino game: ${JSON.stringify(updateCasinoGameDto)}`,
      );

      const existingGame = await this.casinoManager.findOne(CasinoGames, {
        where: { id },
      });

      if (!existingGame) {
        throw new HttpException('Game not found', HttpStatus.NOT_FOUND);
      }

      let gameToSwap: CasinoGames | null = null;
      if (
        updateCasinoGameDto.position !== undefined &&
        updateCasinoGameDto.position !== null
      ) {
        const gameWithSamePosition = await this.casinoManager.findOne(
          CasinoGames,
          {
            where: { position: updateCasinoGameDto.position },
          },
        );

        if (gameWithSamePosition && gameWithSamePosition.id !== id) {
          gameToSwap = gameWithSamePosition;
          this.logger.log(
            `Drag and drop: swapping positions between game ${id} and ${gameWithSamePosition.id} (${gameWithSamePosition.name})`,
          );
        }
      }

      await this.casinoManager.transaction(async casinoTransaction => {
        const gameId = updateCasinoGameDto.gameId || existingGame?.gameId;
        const gameProvider =
          updateCasinoGameDto.gameProvider || existingGame?.gameProvider;
        const gameName = updateCasinoGameDto.name || existingGame?.name;

        const customFileName = this.generateCustomFileName(
          gameId,
          gameProvider,
          gameName,
        );

        // Upload da imagem principal
        if (image || updateCasinoGameDto.imageBase64) {
          let uploadResult: { url: string; path: string };

          if (image) {
            this.logger.log(
              `Uploading new image file to S3: ${image.originalname}`,
            );
            uploadResult = await this.s3Service.uploadFile(
              image.buffer,
              image.originalname,
              'casino_games',
              customFileName,
            );
            this.logger.log(
              `New image file uploaded successfully: ${uploadResult.url}`,
            );
          } else if (updateCasinoGameDto.imageBase64) {
            this.logger.log(`Uploading new base64 image to S3`);
            uploadResult = await this.s3Service.uploadBase64File(
              updateCasinoGameDto.imageBase64,
              'casino_games',
              customFileName,
            );
            this.logger.log(
              `New base64 image uploaded successfully: ${uploadResult.url}`,
            );
          }

          updateCasinoGameDto.image = uploadResult.url;
          updateCasinoGameDto['path'] = uploadResult.path;

          if (existingGame?.image) {
            try {
              await this.s3Service.deleteFileByUrl(existingGame.image);
              this.logger.log(
                `Old image deleted from S3: ${existingGame.image}`,
              );
            } catch (deleteErr) {
              this.logger.warn(
                `Failed to delete old image from S3: ${deleteErr.message}`,
              );
            }
          }
        }

        // Upload da imagem do aggregator
        if (imageAggregator || updateCasinoGameDto.imageBase64Aggregator) {
          let uploadResultAggregator: { url: string; path: string };

          if (imageAggregator) {
            this.logger.log(
              `Uploading new aggregator image file to S3: ${imageAggregator.originalname}`,
            );
            uploadResultAggregator = await this.s3Service.uploadFile(
              imageAggregator.buffer,
              imageAggregator.originalname,
              'casino_games',
              `${customFileName}_aggregator`,
            );
            this.logger.log(
              `New aggregator image file uploaded successfully: ${uploadResultAggregator.url}`,
            );
          } else if (updateCasinoGameDto.imageBase64Aggregator) {
            this.logger.log(`Uploading new base64 aggregator image to S3`);
            uploadResultAggregator = await this.s3Service.uploadBase64File(
              updateCasinoGameDto.imageBase64Aggregator,
              'casino_games',
              `${customFileName}_aggregator`,
            );
            this.logger.log(
              `New base64 aggregator image uploaded successfully: ${uploadResultAggregator.url}`,
            );
          }

          updateCasinoGameDto.imageAggregator = uploadResultAggregator.url;
          updateCasinoGameDto['pathAggregator'] = uploadResultAggregator.path;

          if (existingGame?.imageAggregator) {
            try {
              await this.s3Service.deleteFileByUrl(
                existingGame.imageAggregator,
              );
              this.logger.log(
                `Old aggregator image deleted from S3: ${existingGame.imageAggregator}`,
              );
            } catch (deleteErr) {
              this.logger.warn(
                `Failed to delete old aggregator image from S3: ${deleteErr.message}`,
              );
            }
          }
        }

        delete updateCasinoGameDto.imageBase64;
        delete updateCasinoGameDto.imageBase64Aggregator;

        // Adicionar lastUserModify
        updateCasinoGameDto['lastUserModify'] = backofficeUser.email;

        if (gameToSwap && existingGame?.position != null) {
          await casinoTransaction.update(CasinoGames, gameToSwap.id, {
            position: existingGame.position,
            lastUserModify: backofficeUser.email,
          });
        }

        await casinoTransaction.update(CasinoGames, id, updateCasinoGameDto);
      });

      // Criar logs de histórico após o update
      await this.logHistoryService.createLogEntries(
        'CasinoGames',
        existingGame,
        updateCasinoGameDto,
        backofficeUser.email,
      );

      this.logger.log(
        `[Fim] Update casino game: ${JSON.stringify(updateCasinoGameDto)}`,
      );
      return {
        message: 'The record has been successfully updated.',
      };
    } catch (err) {
      this.logger.error(`Error updating casino game: ${err}`);
      throw new HttpException(err.message, err.status);
    }
  }

  async remove(id: string) {
    try {
      this.logger.log(`[Inicio] Remove casino game: ${id}`);
      await this.casinoManager.transaction(async casinoTransaction => {
        const removeCasinoGame = await casinoTransaction.softDelete(
          CasinoGames,
          id,
        );
        return removeCasinoGame;
      });
      this.logger.log(`[Fim] Remove casino game: ${id}`);
      return 'The record has been successfully deleted.';
    } catch (err) {
      this.logger.error(`Error removing casino game: ${err}`);
      throw new HttpException(err.message, err.status);
    }
  }

  async getAllProvides(filters: ProviderFilterDto): Promise<{
    data: Array<{ id: string; name: string }>;
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    try {
      this.logger.log(`[Inicio] Get all providers: ${JSON.stringify(filters)}`);

      const hasPagination =
        filters.page !== undefined && filters.pageSize !== undefined;
      const page = !filters.page || filters.page < 1 ? 1 : filters.page;
      const pageSize =
        !filters.pageSize || filters.pageSize < 1 ? 10 : filters.pageSize;
      const offset = (page - 1) * pageSize;
      const pagination = hasPagination ? { limit: pageSize, offset } : {};

      const orderBy = ['id', 'name'].includes(filters.orderBy)
        ? filters.orderBy
        : 'name';
      const sortOrder = filters.sortOrder || SortOrder.ASC;
      // Query builder para contagem total
      const countQueryBuilder = this.casinoManager.createQueryBuilder(
        CasinoGameProvider,
        'providers',
      );

      const queryBuilder = this.casinoManager
        .createQueryBuilder(CasinoGameProvider, 'providers')
        .select('providers.id', 'id')
        .addSelect('providers.name', 'name')
        .addSelect('providers.isActive', 'isActive');

      if (filters.id) {
        queryBuilder.andWhere('providers.id = :id', { id: filters.id });
        countQueryBuilder.andWhere('providers.id = :id', { id: filters.id });
      }

      if (filters.name) {
        queryBuilder.andWhere('providers.name ILIKE :name', {
          name: `${filters.name}%`,
        });
        countQueryBuilder.andWhere('providers.name ILIKE :name', {
          name: `${filters.name}%`,
        });
      }

      // Contagem total de itens
      const totalItems = await countQueryBuilder.getCount();
      const totalPages = Math.ceil(totalItems / pageSize);

      // Aplicar ordenação
      queryBuilder.orderBy(`"${orderBy}"`, sortOrder);

      // Aplicar paginação se existir
      if (pagination.limit) {
        queryBuilder.limit(pagination.limit).offset(pagination.offset);
      }

      const providers = await queryBuilder.getRawMany();

      this.logger.log(`[Fim] Get all providers: ${JSON.stringify(filters)}`);

      return new PaginationResponseDto<CasinoGameProvider>({
        data: providers,
        totalItems,
        currentPage: page,
        pageSize,
        totalPages,
      });
    } catch (err) {
      this.logger.error(`Error getting providers: ${err}`);
      throw new HttpException(
        err.message,
        err.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAllCategories(filters: CategoryFilterDto): Promise<{
    data: Array<{ id: string; name: string; isActive: boolean }>;
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    try {
      this.logger.log(
        `[Inicio] Get all categories: ${JSON.stringify(filters)}`,
      );

      // Configuração padrão de ordenação e paginação
      const page = !filters.page || filters.page < 1 ? 1 : filters.page;
      const pageSize =
        !filters.pageSize || filters.pageSize < 1 ? 10 : filters.pageSize;
      const offset = (page - 1) * pageSize;

      const orderBy = ['id', 'name'].includes(filters.orderBy)
        ? filters.orderBy
        : 'name';
      const sortOrder = filters.sortOrder || SortOrder.ASC;

      // Query builder para contagem total
      const countQueryBuilder = this.casinoManager.createQueryBuilder(
        CasinoGamesCategories,
        'categories',
      );

      const queryBuilder = this.casinoManager
        .createQueryBuilder(CasinoGamesCategories, 'categories')
        .select('categories.id', 'id')
        .addSelect('categories.name', 'name')
        .addSelect('categories.isActive', 'isActive');

      if (filters.id) {
        queryBuilder.andWhere('categories.id = :id', { id: filters.id });
        countQueryBuilder.andWhere('categories.id = :id', { id: filters.id });
      }

      if (filters.name) {
        queryBuilder.andWhere('categories.name ILIKE :name', {
          name: `${filters.name}%`,
        });
        countQueryBuilder.andWhere('categories.name ILIKE :name', {
          name: `${filters.name}%`,
        });
      }

      // Contagem total de itens
      const totalItems = await countQueryBuilder.getCount();
      const totalPages = Math.ceil(totalItems / pageSize);
      // Aplicar ordenação e paginação
      const categories = await queryBuilder
        .orderBy(`"${orderBy}"`, sortOrder)
        .limit(pageSize)
        .offset(offset)
        .getRawMany();

      this.logger.log(`[Fim] Get all categories: ${JSON.stringify(filters)}`);

      return new PaginationResponseDto<CasinoGamesCategories>({
        data: categories,
        totalItems,
        currentPage: page,
        pageSize,
        totalPages,
      });
    } catch (err) {
      this.logger.error(`Error getting categories: ${err}`);
      throw new HttpException(
        err.message,
        err.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async toggleProviderStatus(
    id: string,
    backofficeUser: IBackofficeUser,
  ): Promise<{
    message: string;
    isActive: boolean;
  }> {
    try {
      this.logger.log(`[Inicio] Toggle provider status: ${id}`);

      const provider = await this.casinoManager.findOne(CasinoGameProvider, {
        where: { id },
      });

      if (!provider) {
        throw new HttpException('Provider not found', HttpStatus.NOT_FOUND);
      }

      const oldStatus = provider.isActive;
      const newStatus = !provider.isActive;

      await this.casinoManager.update(CasinoGameProvider, id, {
        isActive: newStatus,
        lastUserModify: backofficeUser.email,
      });

      await this.logHistoryService.createSingleLog(
        'CasinoGameProvider',
        'isActive',
        oldStatus?.toString() || null,
        newStatus.toString(),
        backofficeUser.email,
      );

      this.logger.log(`[Fim] Toggle provider status: ${id}`);

      return {
        message: `Provider ${newStatus ? 'activated' : 'deactivated'} successfully`,
        isActive: newStatus,
      };
    } catch (err) {
      this.logger.error(`Error toggling provider status: ${err}`);
      throw new HttpException(
        err.message,
        err.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async toggleCategoryStatus(
    id: string,
    backofficeUser: IBackofficeUser,
  ): Promise<{
    message: string;
    isActive: boolean;
  }> {
    try {
      this.logger.log(`[Inicio] Toggle category status: ${id}`);

      const category = await this.casinoManager.findOne(CasinoGamesCategories, {
        where: { id },
      });

      if (!category) {
        throw new HttpException('Category not found', HttpStatus.NOT_FOUND);
      }

      const oldStatus = category.isActive;
      const newStatus = !category.isActive;

      await this.casinoManager.update(CasinoGamesCategories, id, {
        isActive: newStatus,
        lastUserModify: backofficeUser.email,
      });

      await this.logHistoryService.createSingleLog(
        'CasinoGamesCategories',
        'isActive',
        oldStatus?.toString() || null,
        newStatus.toString(),
        backofficeUser.email,
      );

      this.logger.log(`[Fim] Toggle category status: ${id}`);

      return {
        message: `Category ${newStatus ? 'activated' : 'deactivated'} successfully`,
        isActive: newStatus,
      };
    } catch (err) {
      this.logger.error(`Error toggling category status: ${err}`);
      throw new HttpException(
        err.message,
        err.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private generateCustomFileName(
    gameId: string,
    gameProvider: string,
    gameName: string,
  ): string {
    const cleanGameId = gameId.replace(/^-/, '');
    const cleanProvider = gameProvider.toLowerCase().replace(/\s+/g, '_');
    const cleanGameName = gameName.toLowerCase().replace(/\s+/g, '_');

    return `${cleanGameId}_${cleanProvider}_${cleanGameName}`;
  }
}
