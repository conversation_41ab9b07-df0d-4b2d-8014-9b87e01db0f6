import {
  <PERSON><PERSON><PERSON>,
  <PERSON>um<PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
  JoinColumn,
  ManyToOne,
} from "typeorm";
import { v4 as uuidV4 } from "uuid";
import { CasinoTransactionEntity } from "../../casino-transaction/controller/entities/casino-transactions.entity";
import { CasinoGames } from "../../casino-games/entities/casino-game.entity";

@Entity("casino_session")
export class CasinoSessionEntity {
  @PrimaryGeneratedColumn("uuid", { name: "id" })
  id: string;

  @OneToMany(
    () => CasinoTransactionEntity,
    (transaction) => transaction.session,
    {
      cascade: true,
    }
  )
  transactions: CasinoTransactionEntity[];

  @ManyToOne(() => CasinoGames, (games) => games.sessions, {
    onDelete: "CASCADE",
    onUpdate: "CASCADE",
  })
  @JoinColumn({ name: "games_id" })
  games: CasinoGames;

  @Column({
    name: "session_player_id",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  sessionAuthId: string;

  @Column({ name: "status_id", type: "int", nullable: true })
  statusId: number;

  @Column({
    name: "game",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  gameId: string;

  @Column({
    name: "player_id",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  playerId: string;

  @Column({
    name: "partner_id",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  partnerId: string;

  @Column({
    name: "request_id",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  requestId: string;

  @Column({
    name: "launch_token",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  launchToken: string;

  @Column({
    name: "launch_url",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  launchUrl: string;

  @Column({
    name: "game_mode",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  gameMode: string;

  @Column({
    name: "token",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  token: string;

  @CreateDateColumn({
    name: "created_at",
    type: "date",
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: "updated_at",
    type: "date",
  })
  updatedAt: Date;

  @DeleteDateColumn({
    name: "deleted_at",
    type: "date",
  })
  deletedAt: Date;

  @Column({
    name: "last_user_updated",
    type: "date",
    nullable: true,
  })
  lastUserUpdated: Date;

  @Column({
    name: "is_deleted",
    type: "varchar",
    length: 1,
  })
  isDeleted: string;

  @Column({
    name: "error_code",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  errorCode: string;

  @Column({
    name: "error_msg",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  errorMsg: string;

  @Column({
    name: "session_aggregator_id",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  aggregatorId: string;

  constructor() {
    if (!this.id) {
      this.id = uuidV4();
    }
  }
}
