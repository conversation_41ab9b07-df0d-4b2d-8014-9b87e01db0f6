import { Body, Controller, HttpCode, Post, UseGuards } from '@nestjs/common';
import { BalanceService } from './balance.service';
import { GetBalanceDto } from './dto/get-balance.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HMACGuard } from '@/common/guards/hmac.guard';
import {
  ApiHmacHeader,
  ApiHmacUnauthorizedResponse,
} from '@/common/decorators/swagger.decorator';
import { SwaggerApiCodeResponses } from '@/common/decorators/swagger-api-code-responses';
import {
  SUCCESS,
  GENERIC_ERROR,
  PLAYER_NOT_FOUND,
  SESSION_NOT_FOUND,
} from '@/common/constants/message-codes';
import {
  GetBalanceResponseDto,
  GetBalanceResponseDtoExample,
} from './dto/get-balance-response.dto';

@ApiTags('Balance')
@Controller('balance')
export class BalanceController {
  constructor(private readonly balanceService: BalanceService) {}

  @UseGuards(HMACGuard)
  @Post()
  @HttpCode(200)
  @ApiOperation({
    summary: 'Consultar saldo',
    description: 'Endpoint para consultar o saldo de um jogador',
  })
  @ApiHmacHeader()
  @ApiHmacUnauthorizedResponse()
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
    ],
    GetBalanceResponseDto,
    GetBalanceResponseDtoExample
  )
  async getBalance(@Body() getBalanceDto: GetBalanceDto) {
    return this.balanceService.getBalance(getBalanceDto);
  }
}
