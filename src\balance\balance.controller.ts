import { Body, Controller, HttpCode, Post } from '@nestjs/common';
import { BalanceService } from './balance.service';
import { GetBalanceDto } from './dto/get-balance.dto';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { SwaggerApiCodeResponses } from '@/common/decorators/swagger-api-code-responses';
import {
  SUCCESS,
  GENERIC_ERROR,
  PLAYER_NOT_FOUND,
  SESSION_NOT_FOUND,
} from '@/common/constants/message-codes';
import {
  GetBalanceResponseDto,
  GetBalanceResponseDtoExample,
} from './dto/get-balance-response.dto';
import { GetBalanceDetailedResponseDto } from './dto/get-balance-detailed.response.dto';
import { UpdateBalanceResponseDto } from './dto/update-balance-response.dto';
import { RequestDepositDto } from './dto/request-deposit.dto';
import { TransactionResponse } from '@/wallet/interfaces/debit';

@ApiTags('Balance')
@Controller('balance')
export class BalanceController {
  constructor(private readonly balanceService: BalanceService) {}

  @Post()
  @HttpCode(200)
  @ApiOperation({
    summary: 'Consultar saldo',
    description: 'Endpoint para consultar o saldo de um jogador',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
    ],
    GetBalanceResponseDto,
    GetBalanceResponseDtoExample,
  )
  async getBalance(@Body() getBalanceDto: GetBalanceDto): Promise<number> {
    return this.balanceService.getBalance(getBalanceDto);
  }

  @Post('with-bonus')
  @HttpCode(200)
  @ApiOperation({
    summary: 'Consultar saldo com bônus',
    description:
      'Endpoint para consultar o saldo real e de bônus de um jogador',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
    ],
    GetBalanceDetailedResponseDto,
    {
      realBalance: 100.5,
      bonusBalance: 50.0,
    },
  )
  async getDetailedBalance(
    @Body() getBalanceDto: GetBalanceDto,
  ): Promise<GetBalanceDetailedResponseDto> {
    return this.balanceService.getDetailedBalance(getBalanceDto);
  }

  @Post('deposit')
  @HttpCode(200)
  @ApiOperation({
    summary: 'Processa vitórias ou prêmios',
    description: 'Endpoint para processar vitórias ou prêmios de um jogador',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
    ],
    UpdateBalanceResponseDto,
    {
      amount: 100.5,
      transactionId: '123e4567-e89b-12d3-a456-426614174000',
    },
  )
  async deposit(
    @Body() depositDto: RequestDepositDto,
  ): Promise<TransactionResponse> {
    return this.balanceService.deposit(depositDto);
  }
}
