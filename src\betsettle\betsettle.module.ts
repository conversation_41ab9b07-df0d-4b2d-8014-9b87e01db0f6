import { BalanceModule } from '@/balance/balance.module';
import { WalletModule } from '@/wallet/wallet.module';
import { Module } from '@nestjs/common';
import { DatabaseModule } from 'src/common/database/database.module';
import { HMACModule } from 'src/hmac/hmac.module';
import { BetSettleController } from './betsettle.controller';
import { BetSettleService } from './betsettle.service';

@Module({
  imports: [DatabaseModule, HMACModule, WalletModule, BalanceModule],
  controllers: [BetSettleController],
  providers: [BetSettleService],
})
export class BetSettleModule {}
