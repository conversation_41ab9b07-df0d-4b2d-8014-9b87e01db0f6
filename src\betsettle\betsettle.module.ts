import { Module } from '@nestjs/common';
import { DatabaseModule } from 'src/common/database/database.module';
import { HMACModule } from 'src/hmac/hmac.module';
import { BetSettleService } from './betsettle.service';
import { BetSettleController } from './betsettle.controller';
import { WalletModule } from '@/wallet/wallet.module';

@Module({
  imports: [DatabaseModule, HMACModule, WalletModule],
  controllers: [BetSettleController],
  providers: [BetSettleService],
})
export class BetSettleModule {}
