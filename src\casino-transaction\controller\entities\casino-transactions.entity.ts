import { CasinoGames } from '@/casino-games/entities/casino-game.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { v4 as uuidV4 } from 'uuid';
import { CasinoSessionEntity } from '../../../common/entities/casino-session.entity';
import { CasinoTransactionDetailsEntity } from './casino-transaction-details.entity';

@Entity({ name: 'casino_transactions', schema: 'casino' })
@Index('casino_transactions_round_id_uk', ['roundId'], { unique: true })
export class CasinoTransactionEntity {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  @Column({
    name: 'casino_session_id',
    type: 'uuid',
    nullable: true,
  })
  sessionCasinoId: string;

  @ManyToOne(() => CasinoSessionEntity, { onDelete: 'RESTRICT' })
  @JoinColumn({ name: 'casino_session_id' })
  session: CasinoSessionEntity;

  @Column({
    name: 'round_id',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  roundId: string;

  @Column({
    name: 'amount',
    type: 'decimal',
    precision: 30,
    scale: 12,
    default: 0.0,
    nullable: false,
  })
  amount: number;

  @Column({
    name: 'win_amount',
    type: 'decimal',
    precision: 30,
    scale: 12,
    default: 0.0,
    nullable: false,
  })
  winAmount: number;

  @Column({
    name: 'balance',
    type: 'decimal',
    precision: 30,
    scale: 12,
    nullable: true,
  })
  balance: number;

  @Column({
    name: 'round_ended',
    type: 'boolean',
    default: false,
    nullable: false,
  })
  roundEnded: boolean;

  @Column({
    name: 'status',
    type: 'varchar',
    length: 25,
    nullable: false,
  })
  status: string;

  @Column({
    name: 'player_id',
    type: 'uuid',
    nullable: true,
  })
  playerId: string;

  @Column({
    name: 'partner_id',
    type: 'uuid',
    nullable: true,
  })
  partnerId: string;

  @Column({
    name: 'game_id',
    type: 'uuid',
    nullable: true,
  })
  gameId: string;

  @ManyToOne(() => CasinoGames)
  @JoinColumn({ name: 'game_id' })
  casinoGame: CasinoGames;

  @Column({
    name: 'aggregator_code',
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  aggregatorCode: string;

  @Column({
    name: 'currency',
    type: 'varchar',
    length: 10,
    nullable: false,
  })
  currency: string;

  @Column({
    name: 'ip',
    type: 'inet',
    nullable: true,
  })
  ip: string;

  @CreateDateColumn({
    name: 'created_at',
    type: 'timestamptz',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updated_at',
    type: 'timestamptz',
    nullable: true,
  })
  updatedAt: Date;

  @DeleteDateColumn({
    name: 'deleted_at',
    type: 'timestamptz',
    nullable: true,
  })
  deletedAt: Date;

  @OneToMany(
    () => CasinoTransactionDetailsEntity,
    details => details.transaction,
  )
  details: CasinoTransactionDetailsEntity[];

  constructor() {
    if (!this.id) {
      this.id = uuidV4();
    }
  }
}
