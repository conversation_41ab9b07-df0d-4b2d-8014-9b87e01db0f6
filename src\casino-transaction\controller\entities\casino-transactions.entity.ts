import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinC<PERSON>um<PERSON>,
} from "typeorm";
import { v4 as uuidV4 } from "uuid";
import { CasinoSessionEntity } from "../../../common/entities/casino-session.entity";
import { CasinoGames } from "@/casino-games/entities/casino-game.entity";

@Entity("casino_transactions")
export class CasinoTransactionEntity {
  @PrimaryGeneratedColumn("uuid", { name: "id" })
  id: string;

  @ManyToOne(() => CasinoSessionEntity, (session) => session.transactions, {
    onDelete: "CASCADE",
    onUpdate: "CASCADE",
  })
  @JoinColumn({ name: "casino_session_id" })
  session: CasinoSessionEntity;

  @Column({
    name: "bet_transaction_id",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  transactionBetId: string;

  @Column({
    name: "betsettle_transaction_id",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  transactionBetSettleId: string;

  @Column({
    name: "cancel_transaction_id",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  transactionCancelId: string;

  @Column({
    name: "settle_transaction_id",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  transactionSettleId: string;

  @Column({
    name: "customer_id",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  customerId: string;

  @Column({
    name: "game_id",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  gameId: string;

  @ManyToOne(() => CasinoGames)
  @JoinColumn({ name: "game_id", referencedColumnName: "gameId" })
  casinoGame: CasinoGames;

  @Column({
    name: "player_id",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  playerId: string;

  @Column({
    name: "partner_id",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  partnerId: string;

  @Column({
    name: "session_player_id",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  sessionPlayerId: string;

  @Column({
    name: "request_id",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  requestId: string;

  @Column({
    name: "round_id",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  roundId: string;

  @Column({
    name: "session_aggregator_id",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  sessionCasinoId: string;

  @Column({
    name: "status_id",
    type: "int",
    nullable: true,
  })
  statusId: number;

  @Column({
    name: "transaction_id",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  transactionId: string;

  @Column({
    name: "request_type",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  requestType: string;

  @Column({
    name: "round_ended",
    type: "boolean",
    nullable: true,
  })
  roundEnded: boolean;

  @Column({
    name: "token",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  token: string;

  @Column({
    name: "amount",
    type: "decimal",
    nullable: true,
  })
  amount: number;

  @Column({
    name: "win_amount",
    type: "decimal",
    nullable: true,
  })
  winAmount: number;

  @Column({
    name: "game_mode",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  gameMode: string;

  @Column({
    name: "balance",
    type: "decimal",
    nullable: true,
  })
  balance: number;

  @Column({
    name: "error_code",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  errorCode: string;

  @Column({
    name: "error_msg",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  errorMsg: string;

  @CreateDateColumn({ name: "created_at", type: "date" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at", type: "date" })
  updatedAt: Date;

  @DeleteDateColumn({
    name: "deleted_at",
    type: "date",
  })
  deletedAt: Date;

  @Column({
    name: "last_user_updated",
    type: "varchar",
    length: 45,
    nullable: true,
  })
  lastUserUpdated: string;

  @Column({
    name: "is_deleted",
    type: "varchar",
    length: 1,
  })
  isDeleted: string;

  constructor() {
    if (!this.id) {
      this.id = uuidV4();
    }
  }
}
