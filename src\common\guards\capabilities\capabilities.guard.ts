import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { CAPABILITIES_KEY } from 'src/common/decorators/require-capabilities.decorator';

@Injectable()
export class CapabilitiesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredCapabilities = this.reflector.getAllAndOverride<string[]>(CAPABILITIES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    
    if (!requiredCapabilities || requiredCapabilities.length === 0) {
      return true; // Se não há capabilities definidas, permite acesso
    }
    
    const {player} = context.switchToHttp().getRequest();
    
    if (!player || !player.payload || !player.payload.capabilities) {
      throw new UnauthorizedException('Usuário não possui capabilities definidas');
    }
    
    // Verifica se o usuário tem alguma das capabilities necessárias
    return requiredCapabilities.some(capability => {
      // Verifica se a capability existe e é true
      return player.payload.capabilities[capability] === true;
    });
  }
}