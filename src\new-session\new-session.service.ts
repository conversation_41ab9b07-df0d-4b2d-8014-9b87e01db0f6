import { CasinoGames } from '@/casino-games/entities/casino-game.entity';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { CasinoSessionEntity } from '../common/entities/casino-session.entity';
import { WalletService } from '../wallet/wallet.service';
import { CreateCasinoSessionResponseDto } from './dto/create-casino-session-response.dto';
import { CreateCasinoSessionDto } from './dto/create-casino-session.dto';
import { CreateNewSessionDto } from './dto/create-new-session.dto';
import { SessionStatus } from './enums/new-session.enum';

@Injectable()
export class NewSessionService {
  private readonly logger = new Logger(NewSessionService.name);

  constructor(
    @InjectEntityManager() private readonly manager: EntityManager,
    private readonly walletService: WalletService,
  ) {}

  async create(createNewSessionDto: CreateNewSessionDto): Promise<number> {
    try {
      this.logger.log(
        `[Inicio] Create new session: ${JSON.stringify(createNewSessionDto)}`,
      );
      const { sessionId, playerId, newSessionId, gameId } = createNewSessionDto;

      const gamesId = await this.manager.findOneBy(CasinoGames, {
        gameId: gameId,
      });
      const existingSession = await this.findExistingSession(
        sessionId,
        playerId,
      );
      const balance = await this.getBalanceInWallet(
        existingSession.partnerId,
        playerId,
      );

      await this.createNewSession(existingSession, newSessionId, gamesId);

      this.logger.log(
        `[Fim] Create new session: ${JSON.stringify(createNewSessionDto)}`,
      );
      return balance;
    } catch (error) {
      this.logger.error(
        `Failed to create new session: ${error.message}`,
        // error.stack
      );
      throw this.handleError(error);
    }
  }

  async createCasinoSession(
    body: CreateCasinoSessionDto,
    ip: string,
    userAgent: string,
    userPayload: any,
  ): Promise<CreateCasinoSessionResponseDto> {
    try {
      const sessionId = userPayload.session.sessionId;
      const activitySessionId = body.playerSessionActivityId;

      this.logger.log(
        `Creating session with sessionId: ${sessionId}, activitySessionId: ${activitySessionId}`,
      );
      const existingSession = await this.manager.findOne(CasinoSessionEntity, {
        where: {
          playerLoginSessionId: sessionId,
          playerActivitySessionId: activitySessionId,
          partnerId: userPayload.partnerId,
          status: SessionStatus.ACTIVE,
        },
      });

      if (existingSession) {
        return {
          id: existingSession.id,
          playerLoginSessionId: existingSession.playerLoginSessionId,
          playerActivitySessionId: existingSession.playerActivitySessionId,
          status: existingSession.status as SessionStatus,
          expiredAt: existingSession.expiredAt,
        };
      }

      const game = await this.manager.findOne(CasinoGames, {
        where: { gameId: body.gameId },
      });

      if (!game) {
        throw new HttpException('Jogo não encontrado.', HttpStatus.NOT_FOUND);
      }

      const newSession = this.manager.create(CasinoSessionEntity, {
        playerLoginSessionId: sessionId,
        playerActivitySessionId: activitySessionId,
        partnerId: userPayload.partnerId,
        games: game,
        status: SessionStatus.ACTIVE,
        ip: ip,
        userAgent: userAgent,
      });

      const createdSession = await this.manager.save(
        CasinoSessionEntity,
        newSession,
      );

      this.logger.log(
        `Session created with ID: ${createdSession.id}, playerActivitySessionId: ${createdSession.playerActivitySessionId}`,
      );

      return {
        id: createdSession.id,
        playerLoginSessionId: createdSession.playerLoginSessionId,
        playerActivitySessionId: createdSession.playerActivitySessionId,
        status: createdSession.status as SessionStatus,
        expiredAt: createdSession.expiredAt,
      };
    } catch (error) {
      this.logger.error(`Failed to create casino session: ${error.message}`);
      throw this.handleError(error);
    }
  }

  async endCasinoSession(
    payload: any,
  ): Promise<CreateCasinoSessionResponseDto> {
    try {
      this.logger.log(
        `Ending session for sessionId: ${payload.session.sessionId}`,
      );

      const existingSession = await this.manager.findOne(CasinoSessionEntity, {
        where: {
          playerLoginSessionId: payload.session.sessionId,
          partnerId: payload.partnerId,
          status: SessionStatus.ACTIVE,
        },
      });

      if (!existingSession) {
        throw new HttpException('Sessão não encontrada.', HttpStatus.NOT_FOUND);
      }

      existingSession.status = SessionStatus.INACTIVE;
      await this.manager.save(CasinoSessionEntity, existingSession);

      return {
        id: existingSession.id,
        playerLoginSessionId: existingSession.playerLoginSessionId,
        playerActivitySessionId: existingSession.playerActivitySessionId,
        status: existingSession.status as SessionStatus,
        expiredAt: existingSession.expiredAt,
      };
    } catch (error) {
      this.logger.error(`Failed to end casino session: ${error.message}`);
      throw this.handleError(error);
    }
  }

  private async findExistingSession(
    sessionId: string,
    playerId: string,
  ): Promise<CasinoSessionEntity> {
    this.logger.log(
      `[Inicio] Find existing session: ${sessionId}, ${playerId}`,
    );
    const session = await this.manager.findOne(CasinoSessionEntity, {
      where: {
        aggregatorId: sessionId,
        playerId,
      },
    });

    if (!session) {
      this.logger.error(`Session not found`);
      throw new HttpException('Sessão não encontrada.', HttpStatus.NOT_FOUND);
    }

    this.logger.log(`[Fim] Find existing session: ${sessionId}, ${playerId}`);
    return session;
  }

  private async createNewSession(
    existingSession: CasinoSessionEntity,
    newSessionId: string,
    gameId: CasinoGames,
  ): Promise<void> {
    this.logger.log(`[Inicio] Create new session: ${newSessionId}`);
    const newSession = this.buildNewSessionData(
      existingSession,
      newSessionId,
      gameId,
    );

    try {
      await this.manager.insert(CasinoSessionEntity, newSession);
      this.logger.debug(`New casino session created: ${newSession.id}`);
    } catch (error) {
      this.logger.error(`Failed to insert new session: ${error.message}`);
      throw new HttpException(
        'Erro ao criar nova sessão.',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  private buildNewSessionData(
    existingSession: CasinoSessionEntity,
    newSessionId: string,
    gameId: CasinoGames,
  ): Partial<CasinoSessionEntity> {
    this.logger.log(
      `[Inicio] Build new session data: ${JSON.stringify(existingSession)}`,
    );
    return {
      id: uuidv4(),
      aggregatorId: newSessionId,
      statusId: existingSession.statusId,
      playerId: existingSession.playerId,
      partnerId: existingSession.partnerId,
      games: gameId,
      gameId: gameId.gameId,
      requestId: existingSession.requestId,
      launchUrl: existingSession.launchUrl,
      launchToken: existingSession.launchToken,
      gameMode: existingSession.gameMode,
      token: existingSession.token,
      lastUserUpdated: existingSession.lastUserUpdated,
      errorCode: existingSession.errorCode,
      errorMsg: existingSession.errorMsg,
    };
  }

  private async getBalanceInWallet(
    partnerId: string,
    playerId: string,
  ): Promise<number> {
    try {
      this.logger.log(`[Inicio] Fetching balance for player ${playerId}`);
      const response = await this.walletService.getBalanceByPlayer(
        partnerId,
        playerId,
      );

      if (response?.balance === undefined) {
        this.logger.error(`Balance not found for player ${playerId}`);
        throw new HttpException('Saldo não encontrado.', HttpStatus.NOT_FOUND);
      }

      this.logger.log(`[Fim] Fetching balance for player ${playerId}`);
      return response.balance;
    } catch (error) {
      this.logger.error(`Failed to fetch balance: ${error.message}`);
      throw new HttpException(
        'Erro ao buscar saldo.',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  private handleError(error: any): never {
    if (error instanceof HttpException) {
      throw error;
    }

    throw new HttpException(
      error.message || 'Erro interno do servidor',
      HttpStatus.SERVICE_UNAVAILABLE,
    );
  }
}
