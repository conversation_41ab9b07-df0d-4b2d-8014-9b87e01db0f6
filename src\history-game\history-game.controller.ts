import { GenericFilter } from '@/common/filters/generic-filter.dto';
import { Controller, Get, HttpCode, Param, Query, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import {
  HistoryGameResponse,
  HistoryGameService,
} from './history-game.service';
import { HistoryGameFilterDto } from './dto/history-filter.dto';
import {
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiPaginatedResponse,
  ApiPartnerIdHeader,
  ApiDateRangeQuery,
  ApiPaginationQuery,
} from '@/common/decorators/swagger.decorator';
import {
  HistoryGameResponseDto,
  HistoryGameResponseDtoExample,
} from './dto/history-game-response.dto';
import { SwaggerApiCodeResponses } from '@/common/decorators/swagger-api-code-responses';
import {
  SUCCESS,
  ALREADY_PROCESSED,
  GENERIC_ERROR,
  PLAYER_NOT_FOUND,
  SESSION_NOT_FOUND,
  GAME_NOT_FOUND,
  ROUND_NOT_FOUND,
} from '@/common/constants/message-codes';
import {
  PaginationResponseDto,
  PaginatedResponseDtoExample,
} from '@/common/dto/paginated-response.dto';
import { BackofficeGuard } from '@/common/guards/backoffice/backoffice.guard';
@ApiTags('HistoryGame')
@ApiPartnerIdHeader()
@Controller('backoffice/history-game')
@UseGuards(BackofficeGuard)
@ApiBearerAuth('access-token')
export class HistoryGameController {
  constructor(private readonly historyGameService: HistoryGameService) {}

  @Get(':playerId')
  @HttpCode(200)
  @ApiOperation({
    summary: 'Buscar histórico de jogos',
    description:
      'Endpoint para buscar o histórico de jogos de um jogador específico',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    PaginationResponseDto<HistoryGameResponseDto>,
    PaginatedResponseDtoExample(HistoryGameResponseDtoExample)
  )
  @ApiForbiddenResponse()
  @ApiInternalServerErrorResponse()
  @ApiPaginationQuery()
  @ApiDateRangeQuery()
  async findAll(
    @Param('playerId') playerId: string,
    @Query() genericFilter: GenericFilter,
    @Req() req,
    @Query() historyFilterDto: HistoryGameFilterDto
  ): Promise<HistoryGameResponse> {
    const { fromDate, toDate } = historyFilterDto;
    return this.historyGameService.getHistoryGame(
      playerId,
      genericFilter,
      fromDate,
      toDate,
      req
    );
  }
}
