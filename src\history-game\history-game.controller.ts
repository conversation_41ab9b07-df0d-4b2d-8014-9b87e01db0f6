import {
  ALREADY_PROCESSED,
  GAME_NOT_FOUND,
  GENERIC_ERROR,
  PLAYER_NOT_FOUND,
  ROUND_NOT_FOUND,
  SESSION_NOT_FOUND,
  SUCCESS,
} from '@/common/constants/message-codes';
import { SwaggerApiCodeResponses } from '@/common/decorators/swagger-api-code-responses';
import {
  ApiDateRangeQuery,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiPaginationQuery,
  ApiPartnerIdHeader,
} from '@/common/decorators/swagger.decorator';
import {
  PaginatedResponseDtoExample,
  PaginationResponseDto,
} from '@/common/dto/paginated-response.dto';
import { GenericFilter } from '@/common/filters/generic-filter.dto';
import { KeycloakBackofficeGuard } from '@/common/guards/keycloak/keycloak-backoffice.guard';
import {
  Controller,
  Get,
  HttpCode,
  Param,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { HistoryGameFilterDto } from './dto/history-filter.dto';
import {
  HistoryGameResponseDto,
  HistoryGameResponseDtoExample,
} from './dto/history-game-response.dto';
import {
  HistoryGameResponse,
  HistoryGameService,
} from './history-game.service';

@ApiTags('HistoryGame')
@ApiPartnerIdHeader()
@Controller('backoffice/history-game')
@UseGuards(KeycloakBackofficeGuard)
@ApiBearerAuth('access-token')
export class HistoryGameController {
  constructor(private readonly historyGameService: HistoryGameService) {}

  @Get(':playerId')
  @HttpCode(200)
  @ApiOperation({
    summary: 'Buscar histórico de jogos',
    description:
      'Endpoint para buscar o histórico de jogos de um jogador específico',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    PaginationResponseDto<HistoryGameResponseDto>,
    PaginatedResponseDtoExample(HistoryGameResponseDtoExample),
  )
  @ApiForbiddenResponse()
  @ApiInternalServerErrorResponse()
  @ApiPaginationQuery()
  @ApiDateRangeQuery()
  async findAll(
    @Param('playerId') playerId: string,
    @Query() genericFilter: GenericFilter,
    @Req() req,
    @Query() historyFilterDto: HistoryGameFilterDto,
  ): Promise<HistoryGameResponse> {
    const { fromDate, toDate } = historyFilterDto;
    return this.historyGameService.getHistoryGame(
      playerId,
      genericFilter,
      fromDate,
      toDate,
      req,
    );
  }
}
