import { Column, <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn } from "typeorm";

@Entity("daily_report_game_player_partner")
export class HistoryGameEntity {
  @PrimaryGeneratedColumn("uuid", { name: "id" })
  id: string;

  @Column({ name: "player_id" })
  player_id: string;

  @Column({ name: "game_id" })
  game_id: string;

  @Column({ name: "game_name" })
  game_name: string;

  @Column({ name: "game_provider" })
  game_provider: string;

  @Column({ name: "currency" }) 
  currency: string;

  @Column({ name: "total_bet" })
  total_bet: number;

  @Column({name: "total_settle"})
  total_settle: number;

  @Column({name: "ggr"})
  ggr: number;

  @Column({name: "total_round"})
  total_round: number;

  @Column({name: "btag"})
  btag: string;

  @Column({name: "region"})
  region: string;

  @Column({name: "partner_id"})
  partner_id: string;

  @Column({ name: "date" })
  date: Date;
}