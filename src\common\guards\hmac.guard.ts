import {
  Injectable,
  CanActivate,
  ExecutionContext,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { HMACService } from '../../hmac/service/hmac.service';
import { CreateBetSettleDto } from '@/betsettle/dto/create-betsettle.dto';

@Injectable()
export class HMACGuard implements CanActivate {
  constructor(private readonly hmacService: HMACService) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const body = request.rawBody;
    console.log(body)
    const checksum = request.headers['x-checksum'];

    if (!this.hmacService.validateHMAC(body, checksum)) {
      throw new HttpException(
        {
          errorCode: 1102,
          errorMsg: 'HMAC Unauthorized.',
        },
        HttpStatus.UNAUTHORIZED
      );
    }
    return true;
  }
}
