import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { CreateBetDto } from './dto/create-bet.dto';
import { EntityManager } from 'typeorm';
import 'dotenv/config';
import { CasinoTransactionEntity } from '@/casino-transaction/controller/entities/casino-transactions.entity';
import { v4 as uuidv4 } from 'uuid';
import { WalletService } from '@/wallet/wallet.service';
import { InjectEntityManager } from '@nestjs/typeorm';
import { Jwt } from '@/common/interfaces/JWT';
import { CasinoGames } from '@/casino-games/entities/casino-game.entity';

@Injectable()
export class BetService {
  private readonly logger = new Logger(BetService.name);
  constructor(
    @InjectEntityManager() private readonly manager: EntityManager,
    private readonly walletService: WalletService
  ) {}
  async create(
    createBetDto: CreateBetDto,
    auth: Jwt
  ): Promise<{
    balance?: number;
    casinoTransactionId?: string;
    reconcileAmount?: number;
  }> {
    try {
      this.logger.log(`[Inicio] Create bet: ${JSON.stringify(createBetDto)}`);
      const { partnerId } = auth.payload;
      const {
        playerId,
        amount,
        transactionId,
        gameId,
        requestType,
        roundId,
        sessionId,
      } = createBetDto;
      const { gameProvider } = await this.manager.findOne(CasinoGames, {
        where: {
          gameId: gameId,
        },
      });
      const walletResult = await this.walletService.debit(
        {
          amount,
          reason: 'Bet',
          isBonusOperation: false,
          gameId: gameId,
          gameProvider,
        },
        partnerId,
        playerId
      );
      const transactionEntity = this.manager.create(CasinoTransactionEntity, {
        id: uuidv4(),
        transactionBetId: transactionId,
        playerId,
        createdAt: new Date(),
        partnerId,
        gameId,
        requestType: requestType || 'RealMoney',
        roundId,
        amount,
        balance: walletResult.subAccount.balance,
        sessionCasinoId: sessionId,
        transactionId: walletResult.transactionId,
      });

      await this.manager.insert(CasinoTransactionEntity, transactionEntity);
      this.logger.log(`[Fim] Create bet: ${JSON.stringify(createBetDto)}`);
      return {
        balance: walletResult.subAccount.balance,
        casinoTransactionId: transactionEntity.id,
        reconcileAmount: 0,
      };
    } catch (error) {
      this.logger.error(`Error creating bet: ${error}`);
      throw new HttpException('Erro ao criar aposta.', HttpStatus.BAD_REQUEST);
    }
  }
}
