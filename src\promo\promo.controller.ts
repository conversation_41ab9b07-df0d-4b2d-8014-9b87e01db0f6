import { Controller, Post, Body, HttpStatus, HttpCode } from '@nestjs/common';
import { PromoService } from './promo.service';
import { PromoBetDto } from './dto/promo-bet.dto';
import { PromoPrizeDto } from './dto/promo-prize.dto';
import { PromoRollbackDto } from './dto/promo-rollback.dto';
import { PromoWinDto } from './dto/promo-win.dto';
import { ApiResponse } from '@nestjs/swagger';
import { ResponsePromoBetDto } from './dto/response-promo-bet.dto';
import { ResponsePromoPrizeDto } from './dto/response-promo-prize.dto';
import { ResponsePromoRollbackDto } from './dto/response-promo-rollback.dto';
import { ResponsePromoWinDto } from './dto/response-promo-win.dto';

@Controller('promo')
export class PromoController {
  constructor(private readonly promoService: PromoService) {}

  @Post('bet')
  @HttpCode(HttpStatus.OK)
  @ApiResponse({ status: 200, type: ResponsePromoBetDto })
  bet(@Body() createPromoDto: PromoBetDto): Promise<ResponsePromoBetDto> {
    return this.promoService.bet(createPromoDto);
  }

  @Post('prize')
  @HttpCode(HttpStatus.OK)
  @ApiResponse({ status: 200, type: ResponsePromoPrizeDto })
  prize(@Body() createPromoDto: PromoPrizeDto) {
    return this.promoService.prize(createPromoDto);
  }

  @Post('rollback')
  @HttpCode(HttpStatus.OK)
  @ApiResponse({ status: 200, type: ResponsePromoRollbackDto })
  rollback(@Body() createPromoDto: PromoRollbackDto) {
    return this.promoService.rollback(createPromoDto);
  }

  @Post('win')
  @HttpCode(HttpStatus.OK)
  @ApiResponse({ status: 200, type: ResponsePromoWinDto })
  win(@Body() createPromoDto: PromoWinDto) {
    return this.promoService.win(createPromoDto);
  }
}
