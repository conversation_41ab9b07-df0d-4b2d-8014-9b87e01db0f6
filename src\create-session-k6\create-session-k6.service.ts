import { CasinoGames } from '@/casino-games/entities/casino-game.entity';
import { GameModeEnum } from '@/gamelaunch/enum/game-mode.enum';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { CasinoSessionEntity } from '../common/entities/casino-session.entity';
import { CreateSessionK6RequestDto } from './dto/create-session-k6-request.dto';
import { CreateSessionK6ResponseDTO } from './dto/create-session-k6-response.dto';

@Injectable()
export class CreateSessionK6Service {
  private readonly logger = new Logger(CreateSessionK6Service.name);
  balance = 0;

  constructor(@InjectEntityManager() private readonly manager: EntityManager) {}

  async createSessionLauncher(
    body: CreateSessionK6RequestDto,
  ): Promise<CreateSessionK6ResponseDTO> {
    this.logger.log(`[Inicio] getRealPlayURL ${JSON.stringify(body)}`);

    const gameAggregator = await this.manager.findOneBy(CasinoGames, {
      id: '95de729b-0cbe-42c8-8989-15c0e7fa31f8',
    });
    if (!gameAggregator) {
      throw new HttpException('Game not found', HttpStatus.BAD_REQUEST);
    }

    const sessionId = uuidv4();
    const request_id = uuidv4();
    const id = uuidv4();
    try {
      const result = await this.manager
        .insert(CasinoSessionEntity, {
          id,
          requestId: request_id,
          aggregatorId: sessionId,
          games: gameAggregator,
          playerId: body.playerId,
          partnerId: body.partnerId,
          launchUrl: 'k6.url',
          gameMode: GameModeEnum.REAL_PLAY,
        })
        .catch(err => console.log(err));
      this.logger.log(`[Fim] Getting url ${sessionId}`);
      return {
        sessionId,
      };
    } catch (error) {
      this.logger.error(`Error getting url: ${error.message}`);
      console.error(error);
    }
  }
}
