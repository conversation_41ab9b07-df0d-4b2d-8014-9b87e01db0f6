import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
} from "typeorm";
import { Exclude } from "class-transformer";
import { ContactInfoEntity } from "./player-contact-info.entity";

@Entity("region", { schema: "utils" })
export class RegionEntity {
  @Exclude()
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @OneToMany(() => ContactInfoEntity, (contactInfo) => contactInfo.region)
  contactInfos: ContactInfoEntity[];

  @Column({ type: "varchar", name: "name", nullable: true })
  country: string;

  @Column({ type: "varchar", nullable: true })
  language: string;

  @Exclude()
  @Column({ type: "boolean", name: "is_deleted", nullable: true })
  isDeleted: boolean;

  @Exclude()
  @CreateDateColumn({ name: "created_at", type: "timestamp with time zone" })
  createdAt: Date;

  @Exclude()
  @UpdateDateColumn({
    name: "updated_at",
    type: "timestamp with time zone",
    nullable: true,
  })
  updatedAt?: Date;

  @Exclude()
  @DeleteDateColumn({
    name: "deleted_at",
    type: "timestamp with time zone",
    nullable: true,
  })
  deletedAt?: Date;
}
