import { Test, TestingModule } from '@nestjs/testing';
import { SettleController } from './settle.controller';
import { SettleService } from './settle.service';
import { CreateSettleDto } from './dto/create-settle.dto';
import { HMACGuard } from '../common/guards/hmac.guard';
import { HMACService } from '../hmac/service/hmac.service';

const dto: CreateSettleDto = {
  playerId: 'player-1',
  sessionId: 'session-1',
  transactionId: 'trans-1',
  requestType: 'RealMoney',
  gameId: 1,
  roundId: 'round-1',
  roundEnded: true,
  amount: 100,
};

describe('SettleController', () => {
  let controller: SettleController;
  let service: SettleService;

  const mockSettleService = {
    set: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SettleController],
      providers: [
        { provide: SettleService, useValue: mockSettleService },
        { provide: HMACService, useValue: {} },
      ],
    })
      .overrideGuard(HMACGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<SettleController>(SettleController);
    service = module.get<SettleService>(SettleService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('deve retornar sucesso ao liquidar aposta', async () => {
    const retorno = { 
      errorCode: 0, 
      balance: 200, 
      casinoTransactionId: 'casino-1',
      reconcileAmount: 100 
    };
    mockSettleService.set.mockResolvedValue(retorno);
    const result = await controller.create(dto);
    expect(result).toEqual(retorno);
    expect(service.set).toHaveBeenCalledWith(dto);
  });

  it('deve retornar erro de aposta não encontrada', async () => {
    const retorno = { errorCode: 1115, errorMsg: 'Bet not found' };
    mockSettleService.set.mockResolvedValue(retorno);
    const result = await controller.create(dto);
    expect(result).toEqual(retorno);
  });

  it('deve retornar erro de sessão não encontrada', async () => {
    const retorno = { errorCode: 1112, errorMsg: 'Session not found' };
    mockSettleService.set.mockResolvedValue(retorno);
    const result = await controller.create(dto);
    expect(result).toEqual(retorno);
  });

  it('deve retornar erro de jogador não encontrado', async () => {
    const retorno = { errorCode: 1101, errorMsg: 'Player not found' };
    mockSettleService.set.mockResolvedValue(retorno);
    const result = await controller.create(dto);
    expect(result).toEqual(retorno);
  });

  it('deve propagar erro genérico lançado pelo service', async () => {
    mockSettleService.set.mockRejectedValue(new Error('Erro inesperado'));
    await expect(controller.create(dto)).rejects.toThrow('Erro inesperado');
  });
}); 