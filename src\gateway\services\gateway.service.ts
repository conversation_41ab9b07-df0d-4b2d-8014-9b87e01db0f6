import { Injectable, Logger } from '@nestjs/common';
import { AdapterFactoryService } from './adapter-factory.service';
import { CasinoIntegrationService } from './casino-integration.service';
import {
  IBalanceRequest,
  IBalanceResponse,
  IBetRequest,
  IBetResponse,
  IWinRequest,
  IWinResponse,
  ProviderOperation,
} from '../interfaces/provider.interface';

/**
 * Serviço principal do Gateway que orquestra as chamadas entre provedores e o sistema de cassino
 */
@Injectable()
export class GatewayService {
  private readonly logger = new Logger(GatewayService.name);

  constructor(
    private readonly adapterFactory: AdapterFactoryService,
    private readonly casinoIntegration: CasinoIntegrationService,
  ) {}

  /**
   * Processa requisição de saldo
   */
  async processBalance(
    rawRequest: any,
    headers: Record<string, string>,
    providerName?: string,
  ): Promise<any> {
    try {
      // Detecta o provedor se não foi especificado
      const provider = providerName || this.adapterFactory.detectProvider(rawRequest, headers);
      
      this.logger.log(`Processing balance request for provider: ${provider}`);

      // Obtém o adapter
      const adapter = this.adapterFactory.getAdapter(provider);

      // Valida a requisição
      if (!adapter.validateRequest(rawRequest, ProviderOperation.BALANCE)) {
        throw new Error('Invalid balance request');
      }

      // Transforma a requisição para formato padrão
      const standardRequest: IBalanceRequest = adapter.transformRequest(
        rawRequest,
        ProviderOperation.BALANCE,
      );

      // Chama o serviço de cassino (aqui você integraria com o BalanceController existente)
      const standardResponse: IBalanceResponse = await this.callCasinoBalanceService(standardRequest);

      // Transforma a resposta para o formato do provedor
      const providerResponse = adapter.transformResponse(
        standardResponse,
        ProviderOperation.BALANCE,
      );

      this.logger.log(`Balance request processed successfully for provider: ${provider}`);
      return providerResponse;

    } catch (error) {
      this.logger.error('Error processing balance request:', error);
      throw error;
    }
  }

  /**
   * Processa requisição de aposta
   */
  async processBet(
    rawRequest: any,
    headers: Record<string, string>,
    providerName?: string,
  ): Promise<any> {
    try {
      const provider = providerName || this.adapterFactory.detectProvider(rawRequest, headers);
      
      this.logger.log(`Processing bet request for provider: ${provider}`);

      const adapter = this.adapterFactory.getAdapter(provider);

      if (!adapter.validateRequest(rawRequest, ProviderOperation.BET)) {
        throw new Error('Invalid bet request');
      }

      const standardRequest: IBetRequest = adapter.transformRequest(
        rawRequest,
        ProviderOperation.BET,
      );

      const standardResponse: IBetResponse = await this.callCasinoBetService(standardRequest);

      const providerResponse = adapter.transformResponse(
        standardResponse,
        ProviderOperation.BET,
      );

      this.logger.log(`Bet request processed successfully for provider: ${provider}`);
      return providerResponse;

    } catch (error) {
      this.logger.error('Error processing bet request:', error);
      throw error;
    }
  }

  /**
   * Processa requisição de ganho
   */
  async processWin(
    rawRequest: any,
    headers: Record<string, string>,
    providerName?: string,
  ): Promise<any> {
    try {
      const provider = providerName || this.adapterFactory.detectProvider(rawRequest, headers);
      
      this.logger.log(`Processing win request for provider: ${provider}`);

      const adapter = this.adapterFactory.getAdapter(provider);

      if (!adapter.validateRequest(rawRequest, ProviderOperation.WIN)) {
        throw new Error('Invalid win request');
      }

      const standardRequest: IWinRequest = adapter.transformRequest(
        rawRequest,
        ProviderOperation.WIN,
      );

      const standardResponse: IWinResponse = await this.callCasinoWinService(standardRequest);

      const providerResponse = adapter.transformResponse(
        standardResponse,
        ProviderOperation.WIN,
      );

      this.logger.log(`Win request processed successfully for provider: ${provider}`);
      return providerResponse;

    } catch (error) {
      this.logger.error('Error processing win request:', error);
      throw error;
    }
  }

  /**
   * Obtém informações dos provedores disponíveis
   */
  getProvidersInfo(): Array<{ name: string; version: string; enabled: boolean }> {
    return this.adapterFactory.getProvidersInfo();
  }

  /**
   * Verifica se um provedor está disponível
   */
  isProviderAvailable(providerName: string): boolean {
    return this.adapterFactory.hasAdapter(providerName);
  }

  /**
   * Chama o serviço de saldo do cassino (integração com sistema existente)
   */
  private async callCasinoBalanceService(request: IBalanceRequest): Promise<IBalanceResponse> {
    return await this.casinoIntegration.getPlayerBalance(request);
  }

  /**
   * Chama o serviço de aposta do cassino
   */
  private async callCasinoBetService(request: IBetRequest): Promise<IBetResponse> {
    return await this.casinoIntegration.processBet(request);
  }

  /**
   * Chama o serviço de ganho do cassino
   */
  private async callCasinoWinService(request: IWinRequest): Promise<IWinResponse> {
    return await this.casinoIntegration.processWin(request);
  }
}
