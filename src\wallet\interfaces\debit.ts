
export interface TransactionResponse {
    partnerId: string,
    transactionId: string,
    amount: number,
    notes: string,
    origin: string,
    initialBalance: number,
    endBalance: number,
    subAccount: {
      id: string,
      accountType: string,
      balance: number,
      createdAt: string,
      updatedAt: string,
      deletedAt: null,
      wallet: {
        id: string,
        partnerId: string,
        playerId: string,
        walletNumber: string,
        currency: string,
        createdAt: string,
        updatedAt: string,
        deletedAt: string | null
      }
    },
    id: number,
    createdAt: string,
    updatedAt: string,
    deletedAt: string | null
}