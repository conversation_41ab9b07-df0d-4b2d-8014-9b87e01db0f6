import { Test, TestingModule } from '@nestjs/testing';
import { CasinoTransactionController } from './casino-transaction.controller';
import { CasinoTransactionService } from '../services/casino-transaction.service';
import { BackofficeGuard } from '@/common/guards/backoffice/backoffice.guard';

describe('CasinoTransactionController', () => {
  let controller: CasinoTransactionController;

  const mockCasinoTransactionService = {};
  const mockBackofficeGuard = { canActivate: jest.fn(() => true) };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CasinoTransactionController],
      providers: [
        { provide: CasinoTransactionService, useValue: mockCasinoTransactionService },
      ],
    })
      .overrideGuard(BackofficeGuard)
      .useValue(mockBackofficeGuard)
      .compile();

    controller = module.get<CasinoTransactionController>(CasinoTransactionController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
}); 