import { Test, TestingModule } from '@nestjs/testing';
import { CasinoTransactionController } from './casino-transaction.controller';
import { CasinoTransactionService } from '../services/casino-transaction.service';
import { BackofficeGuard } from '@/common/guards/backoffice/backoffice.guard';
import { GenericFilter } from '@/common/filters/generic-filter.dto';
import { CasinoTransactionFilterDto } from './dto/casino-transaction-filter.dto';
import { SortOrder } from '@/common/filters/sortOrder';

describe('CasinoTransactionController', () => {
  let controller: CasinoTransactionController;
  let casinoTransactionService: jest.Mocked<CasinoTransactionService>;

  const mockCasinoTransactionService = {
    findAll: jest.fn(),
    findKpisPlayer: jest.fn(),
    findAvgBet: jest.fn(),
    findBetCount: jest.fn(),
    findBetProfitable: jest.fn(),
    findBetTransaction: jest.fn(),
  };

  const mockBackofficeGuard = { canActivate: jest.fn(() => true) };

  const mockRequest = {
    headers: {
      'partner-id': 'test-partner-id',
    },
  } as any;

  const mockGenericFilter: GenericFilter = {
    page: 1,
    pageSize: 10,
    orderBy: 'createdAt',
    sortOrder: SortOrder.DESC,
    partners: 'test-partner',
  };

  const mockCasinoTransactionFilter: Partial<CasinoTransactionFilterDto> = {
    cancelTransaction: 'false',
    sessionId: 'test-session-id',
    playerId: 'test-player-id',
    fromDate: '2024-01-01',
    toDate: '2024-01-31',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CasinoTransactionController],
      providers: [
        { provide: CasinoTransactionService, useValue: mockCasinoTransactionService },
      ],
    })
      .overrideGuard(BackofficeGuard)
      .useValue(mockBackofficeGuard)
      .compile();

    controller = module.get<CasinoTransactionController>(CasinoTransactionController);
    casinoTransactionService = module.get(CasinoTransactionService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    const mockPaginatedResponse = {
      data: [
        {
          id: 'transaction-1',
          session: null,
          transactionBetId: 'bet-1',
          transactionBetSettleId: 'settle-1',
          transactionCancelId: null,
          transactionSettleId: null,
          customerId: 'customer-1',
          gameId: 'game-1',
          casinoGame: null,
          playerId: 'player-1',
          partnerId: 'partner-1',
          sessionPlayerId: 'session-player-1',
          requestId: 'request-1',
          roundId: 'round-1',
          sessionCasinoId: 'casino-1',
          statusId: 1,
          transactionId: 'transaction-1',
          requestType: 'bet',
          roundEnded: false,
          token: 'token-1',
          amount: 100,
          winAmount: 50,
          gameMode: 'real',
          balance: 1000,
          errorCode: null,
          errorMsg: null,
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-01'),
          deletedAt: null,
          lastUserUpdated: null,
          isDeleted: '0',
        } as any,
      ],
      totalItems: 2,
      totalPages: 1,
      currentPage: 1,
      pageSize: 10,
    };

    beforeEach(() => {
      casinoTransactionService.findAll.mockResolvedValue(mockPaginatedResponse);
    });

    it('should return paginated casino transactions', async () => {
      // Arrange
      const filter = { ...mockGenericFilter, ...mockCasinoTransactionFilter };

      // Act
      const result = await controller.findAll(filter, mockRequest);

      // Assert
      expect(result).toEqual(mockPaginatedResponse);
      expect(casinoTransactionService.findAll).toHaveBeenCalledWith(filter, mockRequest);
    });

    it('should handle filter with only generic parameters', async () => {
      // Arrange
      const filter = mockGenericFilter;

      // Act
      const result = await controller.findAll(filter, mockRequest);

      // Assert
      expect(result).toEqual(mockPaginatedResponse);
      expect(casinoTransactionService.findAll).toHaveBeenCalledWith(filter, mockRequest);
    });

    it('should handle filter with all parameters', async () => {
      // Arrange
      const completeFilter = {
        ...mockGenericFilter,
        ...mockCasinoTransactionFilter,
        gameId: 'test-game-id',
      };

      // Act
      const result = await controller.findAll(completeFilter, mockRequest);

      // Assert
      expect(result).toEqual(mockPaginatedResponse);
      expect(casinoTransactionService.findAll).toHaveBeenCalledWith(completeFilter, mockRequest);
    });

    it('should handle empty filter', async () => {
      // Arrange
      const emptyFilter = {} as GenericFilter & CasinoTransactionFilterDto;

      // Act
      const result = await controller.findAll(emptyFilter, mockRequest);

      // Assert
      expect(result).toEqual(mockPaginatedResponse);
      expect(casinoTransactionService.findAll).toHaveBeenCalledWith(emptyFilter, mockRequest);
    });

    it('should propagate service errors', async () => {
      // Arrange
      const error = new Error('Service error');
      casinoTransactionService.findAll.mockRejectedValue(error);
      const filter = mockGenericFilter;

      // Act & Assert
      await expect(controller.findAll(filter, mockRequest)).rejects.toThrow('Service error');
      expect(casinoTransactionService.findAll).toHaveBeenCalledWith(filter, mockRequest);
    });
  });

  describe('findOne', () => {
    const playerId = 'test-player-id';
    const mockPlayerTransactions = {
      data: [
        {
          id: 'transaction-1',
          playerId: playerId,
          amount: 100,
          winAmount: 50,
        } as any,
      ],
      totalItems: 1,
      totalPages: 1,
      currentPage: 1,
      pageSize: 10,
    };

    beforeEach(() => {
      casinoTransactionService.findAll.mockResolvedValue(mockPlayerTransactions);
    });

    it('should return transactions for specific player', async () => {
      // Arrange
      const filter = { ...mockGenericFilter, ...mockCasinoTransactionFilter };

      // Act
      const result = await controller.findOne(playerId, filter, mockRequest);

      // Assert
      expect(result).toEqual(mockPlayerTransactions);
      expect(casinoTransactionService.findAll).toHaveBeenCalledWith(
        { ...filter, playerId },
        mockRequest
      );
    });

    it('should override playerId in filter with param value', async () => {
      // Arrange
      const filter = { ...mockGenericFilter, playerId: 'different-player-id' };
      const paramPlayerId = 'param-player-id';

      // Act
      const result = await controller.findOne(paramPlayerId, filter, mockRequest);

      // Assert
      expect(result).toEqual(mockPlayerTransactions);
      expect(casinoTransactionService.findAll).toHaveBeenCalledWith(
        { ...filter, playerId: paramPlayerId },
        mockRequest
      );
    });

    it('should handle empty filter with player ID', async () => {
      // Arrange
      const emptyFilter = {} as GenericFilter & CasinoTransactionFilterDto;

      // Act
      const result = await controller.findOne(playerId, emptyFilter, mockRequest);

      // Assert
      expect(result).toEqual(mockPlayerTransactions);
      expect(casinoTransactionService.findAll).toHaveBeenCalledWith(
        { ...emptyFilter, playerId },
        mockRequest
      );
    });

    it('should propagate service errors', async () => {
      // Arrange
      const error = new Error('Player not found');
      casinoTransactionService.findAll.mockRejectedValue(error);
      const filter = mockGenericFilter;

      // Act & Assert
      await expect(controller.findOne(playerId, filter, mockRequest)).rejects.toThrow('Player not found');
      expect(casinoTransactionService.findAll).toHaveBeenCalledWith(
        { ...filter, playerId },
        mockRequest
      );
    });
  });

  describe('findKpis', () => {
    const playerId = 'test-player-id';
    const mockKpisResponse = {
      lastCasinoBet: {
        date: new Date('2024-01-01'),
        amount: 100,
      },
      totalStake: 1000,
      totalWin: 800,
      performance: {
        profitLoss: -200,
        roi: -20,
      },
      bonus: {
        stake: 100,
        win: 80,
      },
    };

    beforeEach(() => {
      casinoTransactionService.findKpisPlayer.mockResolvedValue(mockKpisResponse);
    });

    it('should return KPIs for specific player', async () => {
      // Arrange
      const filter = { ...mockGenericFilter, ...mockCasinoTransactionFilter };

      // Act
      const result = await controller.findKpis(playerId, filter, mockRequest);

      // Assert
      expect(result).toEqual(mockKpisResponse);
      expect(casinoTransactionService.findKpisPlayer).toHaveBeenCalledWith(
        { ...filter, playerId },
        mockRequest
      );
    });

    it('should override playerId in filter with param value', async () => {
      // Arrange
      const filter = { ...mockGenericFilter, playerId: 'different-player-id' };
      const paramPlayerId = 'param-player-id';

      // Act
      const result = await controller.findKpis(paramPlayerId, filter, mockRequest);

      // Assert
      expect(result).toEqual(mockKpisResponse);
      expect(casinoTransactionService.findKpisPlayer).toHaveBeenCalledWith(
        { ...filter, playerId: paramPlayerId },
        mockRequest
      );
    });

    it('should handle empty filter with player ID', async () => {
      // Arrange
      const emptyFilter = {} as GenericFilter & CasinoTransactionFilterDto;

      // Act
      const result = await controller.findKpis(playerId, emptyFilter, mockRequest);

      // Assert
      expect(result).toEqual(mockKpisResponse);
      expect(casinoTransactionService.findKpisPlayer).toHaveBeenCalledWith(
        { ...emptyFilter, playerId },
        mockRequest
      );
    });

    it('should propagate service errors', async () => {
      // Arrange
      const error = new Error('KPIs calculation failed');
      casinoTransactionService.findKpisPlayer.mockRejectedValue(error);
      const filter = mockGenericFilter;

      // Act & Assert
      await expect(controller.findKpis(playerId, filter, mockRequest)).rejects.toThrow('KPIs calculation failed');
      expect(casinoTransactionService.findKpisPlayer).toHaveBeenCalledWith(
        { ...filter, playerId },
        mockRequest
      );
    });
  });

  describe('findAvgBet', () => {
    const mockAvgBetResponse = {
      averageBet: 125.50,
      totalBets: 100,
      totalAmount: 12550,
    };

    beforeEach(() => {
      casinoTransactionService.findAvgBet.mockResolvedValue(mockAvgBetResponse);
    });

    it('should return average bet statistics', async () => {
      // Arrange
      const filter = { ...mockGenericFilter, ...mockCasinoTransactionFilter, fromDate: '2024-01-01', toDate: '2024-01-31' };

      // Act
      const result = await controller.findAvgBet(filter as any, mockRequest);

      // Assert
      expect(result).toEqual(mockAvgBetResponse);
      expect(casinoTransactionService.findAvgBet).toHaveBeenCalledWith(filter, mockRequest);
    });

    it('should handle empty filter', async () => {
      // Arrange
      const emptyFilter = { fromDate: '2024-01-01', toDate: '2024-01-31' } as any;

      // Act
      const result = await controller.findAvgBet(emptyFilter, mockRequest);

      // Assert
      expect(result).toEqual(mockAvgBetResponse);
      expect(casinoTransactionService.findAvgBet).toHaveBeenCalledWith(emptyFilter, mockRequest);
    });

    it('should propagate service errors', async () => {
      // Arrange
      const error = new Error('Average bet calculation failed');
      casinoTransactionService.findAvgBet.mockRejectedValue(error);
      const filter = { ...mockGenericFilter, fromDate: '2024-01-01', toDate: '2024-01-31' } as any;

      // Act & Assert
      await expect(controller.findAvgBet(filter, mockRequest)).rejects.toThrow('Average bet calculation failed');
      expect(casinoTransactionService.findAvgBet).toHaveBeenCalledWith(filter, mockRequest);
    });
  });

  describe('findBetCount', () => {
    const mockBetCountResponse = {
      totalPlayer: 500,
    };

    beforeEach(() => {
      casinoTransactionService.findBetCount.mockResolvedValue(mockBetCountResponse);
    });

    it('should return bet count statistics', async () => {
      // Arrange
      const filter = { ...mockGenericFilter, ...mockCasinoTransactionFilter, fromDate: '2024-01-01', toDate: '2024-01-31' };

      // Act
      const result = await controller.findBetCount(filter as any, mockRequest);

      // Assert
      expect(result).toEqual(mockBetCountResponse);
      expect(casinoTransactionService.findBetCount).toHaveBeenCalledWith(filter, mockRequest);
    });

    it('should handle empty filter', async () => {
      // Arrange
      const emptyFilter = { fromDate: '2024-01-01', toDate: '2024-01-31' } as any;

      // Act
      const result = await controller.findBetCount(emptyFilter, mockRequest);

      // Assert
      expect(result).toEqual(mockBetCountResponse);
      expect(casinoTransactionService.findBetCount).toHaveBeenCalledWith(emptyFilter, mockRequest);
    });

    it('should propagate service errors', async () => {
      // Arrange
      const error = new Error('Bet count calculation failed');
      casinoTransactionService.findBetCount.mockRejectedValue(error);
      const filter = { ...mockGenericFilter, fromDate: '2024-01-01', toDate: '2024-01-31' } as any;

      // Act & Assert
      await expect(controller.findBetCount(filter, mockRequest)).rejects.toThrow('Bet count calculation failed');
      expect(casinoTransactionService.findBetCount).toHaveBeenCalledWith(filter, mockRequest);
    });
  });

  describe('findBetProfitable', () => {
    const mockBetProfitableResponse = {
      top3Games: [
        {
          casino: { name: 'Game 1' },
          totalBets: 100,
          earnings: 1000,
          profit: 200,
          percentageContribution: 20,
        },
      ],
      bottom3Games: [
        {
          casino: { name: 'Game 2' },
          totalBets: 50,
          earnings: 500,
          profit: -100,
          percentageContribution: 10,
        },
      ],
    };

    beforeEach(() => {
      casinoTransactionService.findBetProfitable.mockResolvedValue(mockBetProfitableResponse);
    });

    it('should return bet profitability statistics', async () => {
      // Arrange
      const filter = { ...mockGenericFilter, ...mockCasinoTransactionFilter, fromDate: '2024-01-01', toDate: '2024-01-31' };

      // Act
      const result = await controller.findBetProfitable(filter as any, mockRequest);

      // Assert
      expect(result).toEqual(mockBetProfitableResponse);
      expect(casinoTransactionService.findBetProfitable).toHaveBeenCalledWith(filter, mockRequest);
    });

    it('should handle empty filter', async () => {
      // Arrange
      const emptyFilter = { fromDate: '2024-01-01', toDate: '2024-01-31' } as any;

      // Act
      const result = await controller.findBetProfitable(emptyFilter, mockRequest);

      // Assert
      expect(result).toEqual(mockBetProfitableResponse);
      expect(casinoTransactionService.findBetProfitable).toHaveBeenCalledWith(emptyFilter, mockRequest);
    });

    it('should propagate service errors', async () => {
      // Arrange
      const error = new Error('Bet profitability calculation failed');
      casinoTransactionService.findBetProfitable.mockRejectedValue(error);
      const filter = { ...mockGenericFilter, fromDate: '2024-01-01', toDate: '2024-01-31' } as any;

      // Act & Assert
      await expect(controller.findBetProfitable(filter, mockRequest)).rejects.toThrow('Bet profitability calculation failed');
      expect(casinoTransactionService.findBetProfitable).toHaveBeenCalledWith(filter, mockRequest);
    });
  });

  describe('findBetTransaction', () => {
    const mockBetTransactionResponse = {
      income: 50000,
      profit: 5000,
      GGR: 10,
      profitability: 10,
      averageBet: 50,
      totalUniquePlayers: 100,
    };

    beforeEach(() => {
      casinoTransactionService.findBetTransaction.mockResolvedValue(mockBetTransactionResponse);
    });

    it('should return bet transaction statistics', async () => {
      // Arrange
      const filter = { ...mockGenericFilter, ...mockCasinoTransactionFilter, fromDate: '2024-01-01', toDate: '2024-01-31' };

      // Act
      const result = await controller.findBetTransaction(filter as any, mockRequest);

      // Assert
      expect(result).toEqual(mockBetTransactionResponse);
      expect(casinoTransactionService.findBetTransaction).toHaveBeenCalledWith(filter, mockRequest);
    });

    it('should handle empty filter', async () => {
      // Arrange
      const emptyFilter = { fromDate: '2024-01-01', toDate: '2024-01-31' } as any;

      // Act
      const result = await controller.findBetTransaction(emptyFilter, mockRequest);

      // Assert
      expect(result).toEqual(mockBetTransactionResponse);
      expect(casinoTransactionService.findBetTransaction).toHaveBeenCalledWith(emptyFilter, mockRequest);
    });

    it('should propagate service errors', async () => {
      // Arrange
      const error = new Error('Bet transaction calculation failed');
      casinoTransactionService.findBetTransaction.mockRejectedValue(error);
      const filter = { ...mockGenericFilter, fromDate: '2024-01-01', toDate: '2024-01-31' } as any;

      // Act & Assert
      await expect(controller.findBetTransaction(filter, mockRequest)).rejects.toThrow('Bet transaction calculation failed');
      expect(casinoTransactionService.findBetTransaction).toHaveBeenCalledWith(filter, mockRequest);
    });
  });

  describe('Integration tests', () => {
    it('should handle complete workflow for player transactions', async () => {
      // Arrange
      const playerId = 'integration-player-id';
      const filter = {
        ...mockGenericFilter,
        ...mockCasinoTransactionFilter,
        playerId: 'should-be-overridden',
      };

      const mockTransactions = {
        data: [
          {
            id: 'integration-transaction-1',
            playerId: playerId,
            amount: 100,
            winAmount: 150,
          } as any,
        ],
        totalItems: 1,
        totalPages: 1,
        currentPage: 1,
        pageSize: 10,
      };

      const mockKpis = {
        lastCasinoBet: {
          date: new Date('2024-01-01'),
          amount: 100,
        },
        totalStake: 100,
        totalWin: 150,
        performance: { profitLoss: 50, roi: 50 },
        bonus: { stake: 0, win: 0 },
      };

      casinoTransactionService.findAll.mockResolvedValue(mockTransactions);
      casinoTransactionService.findKpisPlayer.mockResolvedValue(mockKpis);

      // Act
      const transactionsResult = await controller.findOne(playerId, filter, mockRequest);
      const kpisResult = await controller.findKpis(playerId, filter, mockRequest);

      // Assert
      expect(transactionsResult).toEqual(mockTransactions);
      expect(kpisResult).toEqual(mockKpis);

      // Verify that playerId was correctly overridden in both calls
      expect(casinoTransactionService.findAll).toHaveBeenCalledWith(
        { ...filter, playerId },
        mockRequest
      );
      expect(casinoTransactionService.findKpisPlayer).toHaveBeenCalledWith(
        { ...filter, playerId },
        mockRequest
      );
    });

    it('should handle statistics workflow with consistent filters', async () => {
      // Arrange
      const filter = { ...mockGenericFilter, ...mockCasinoTransactionFilter };

      const mockAvgBet = { averageBet: 100, totalBets: 10, totalAmount: 1000 };
      const mockBetCount = { totalPlayer: 10 };
      const mockBetProfitable = { top3Games: [], bottom3Games: [] };
      const mockBetTransaction = { income: 1000, profit: 200, GGR: 20, profitability: 20, averageBet: 100, totalUniquePlayers: 10 };

      casinoTransactionService.findAvgBet.mockResolvedValue(mockAvgBet);
      casinoTransactionService.findBetCount.mockResolvedValue(mockBetCount);
      casinoTransactionService.findBetProfitable.mockResolvedValue(mockBetProfitable);
      casinoTransactionService.findBetTransaction.mockResolvedValue(mockBetTransaction);

      // Act
      const filterWithDates = { ...filter, fromDate: '2024-01-01', toDate: '2024-01-31' } as any;
      const avgBetResult = await controller.findAvgBet(filterWithDates, mockRequest);
      const betCountResult = await controller.findBetCount(filterWithDates, mockRequest);
      const betProfitableResult = await controller.findBetProfitable(filterWithDates, mockRequest);
      const betTransactionResult = await controller.findBetTransaction(filterWithDates, mockRequest);

      // Assert
      expect(avgBetResult).toEqual(mockAvgBet);
      expect(betCountResult).toEqual(mockBetCount);
      expect(betProfitableResult).toEqual(mockBetProfitable);
      expect(betTransactionResult).toEqual(mockBetTransaction);

      // Verify all services were called with the same filter
      expect(casinoTransactionService.findAvgBet).toHaveBeenCalledWith(filterWithDates, mockRequest);
      expect(casinoTransactionService.findBetCount).toHaveBeenCalledWith(filterWithDates, mockRequest);
      expect(casinoTransactionService.findBetProfitable).toHaveBeenCalledWith(filterWithDates, mockRequest);
      expect(casinoTransactionService.findBetTransaction).toHaveBeenCalledWith(filterWithDates, mockRequest);
    });
  });

  describe('Edge cases and error handling', () => {
    it('should handle request without partner-id header', async () => {
      // Arrange
      const requestWithoutPartnerId = { headers: {} } as any;
      const filter = mockGenericFilter;
      const mockResponse = { data: [], totalItems: 0, totalPages: 0, currentPage: 1, pageSize: 10 };

      casinoTransactionService.findAll.mockResolvedValue(mockResponse);

      // Act
      const result = await controller.findAll(filter, requestWithoutPartnerId);

      // Assert
      expect(result).toEqual(mockResponse);
      expect(casinoTransactionService.findAll).toHaveBeenCalledWith(filter, requestWithoutPartnerId);
    });

    it('should handle null/undefined filter values', async () => {
      // Arrange
      const filterWithNulls = {
        ...mockGenericFilter,
        cancelTransaction: null,
        sessionId: undefined,
        playerId: '',
        fromDate: null,
        toDate: undefined,
      } as any;

      const mockResponse = { data: [], totalItems: 0, totalPages: 0, currentPage: 1, pageSize: 10 };
      casinoTransactionService.findAll.mockResolvedValue(mockResponse);

      // Act
      const result = await controller.findAll(filterWithNulls, mockRequest);

      // Assert
      expect(result).toEqual(mockResponse);
      expect(casinoTransactionService.findAll).toHaveBeenCalledWith(filterWithNulls, mockRequest);
    });

    it('should handle concurrent requests', async () => {
      // Arrange
      const filter1 = { ...mockGenericFilter, playerId: 'player-1' };
      const filter2 = { ...mockGenericFilter, playerId: 'player-2' };

      const mockResponse1 = { data: [{ id: '1', playerId: 'player-1' } as any], totalItems: 1, totalPages: 1, currentPage: 1, pageSize: 10 };
      const mockResponse2 = { data: [{ id: '2', playerId: 'player-2' } as any], totalItems: 1, totalPages: 1, currentPage: 1, pageSize: 10 };

      casinoTransactionService.findAll
        .mockResolvedValueOnce(mockResponse1)
        .mockResolvedValueOnce(mockResponse2);

      // Act
      const [result1, result2] = await Promise.all([
        controller.findAll(filter1, mockRequest),
        controller.findAll(filter2, mockRequest),
      ]);

      // Assert
      expect(result1).toEqual(mockResponse1);
      expect(result2).toEqual(mockResponse2);
      expect(casinoTransactionService.findAll).toHaveBeenCalledTimes(2);
    });

    it('should handle service timeout errors', async () => {
      // Arrange
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'TimeoutError';
      casinoTransactionService.findAll.mockRejectedValue(timeoutError);

      // Act & Assert
      await expect(controller.findAll(mockGenericFilter, mockRequest)).rejects.toThrow('Request timeout');
    });
  });
});