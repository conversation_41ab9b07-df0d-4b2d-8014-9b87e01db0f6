import { Injectable, Logger } from '@nestjs/common';
import { IProviderAdapter, IProviderConfig } from '../interfaces/provider.interface';
import { EgtAdapter } from '../adapters/egt-adapter';
import { BgamingAdapter } from '../adapters/bgaming-adapter';

/**
 * Factory para criar adapters de provedores
 */
@Injectable()
export class AdapterFactoryService {
  private readonly logger = new Logger(AdapterFactoryService.name);
  private readonly adapters = new Map<string, IProviderAdapter>();

  constructor() {
    this.initializeAdapters();
  }

  /**
   * Inicializa todos os adapters disponíveis
   */
  private initializeAdapters(): void {
    // Configurações dos provedores (normalmente viriam de um arquivo de config ou banco)
    const configs: Record<string, IProviderConfig> = {
      EGT: {
        name: 'EGT',
        version: '1.0.0',
        enabled: true,
        endpoints: {
          balance: '/api/egt/balance',
          bet: '/api/egt/bet',
          win: '/api/egt/win',
        },
        authentication: {
          type: 'bearer',
          credentials: {
            token: process.env.EGT_API_TOKEN,
          },
        },
        timeout: 5000,
        retries: 3,
        rateLimit: {
          requests: 100,
          window: 60,
        },
      },
      BGAMING: {
        name: 'BGAMING',
        version: '1.0.0',
        enabled: true,
        endpoints: {
          balance: '/api/bgaming/balance',
          bet: '/api/bgaming/bet',
          win: '/api/bgaming/win',
        },
        authentication: {
          type: 'apikey',
          credentials: {
            apiKey: process.env.BGAMING_API_KEY,
            secretKey: process.env.BGAMING_SECRET_KEY,
          },
        },
        timeout: 5000,
        retries: 3,
        rateLimit: {
          requests: 200,
          window: 60,
        },
      },
    };

    // Registra os adapters
    this.registerAdapter('EGT', new EgtAdapter(configs.EGT));
    this.registerAdapter('BGAMING', new BgamingAdapter(configs.BGAMING));

    this.logger.log(`Initialized ${this.adapters.size} provider adapters`);
  }

  /**
   * Registra um adapter
   */
  private registerAdapter(providerName: string, adapter: IProviderAdapter): void {
    this.adapters.set(providerName.toUpperCase(), adapter);
    this.logger.log(`Registered adapter for provider: ${providerName}`);
  }

  /**
   * Obtém um adapter pelo nome do provedor
   */
  getAdapter(providerName: string): IProviderAdapter {
    const adapter = this.adapters.get(providerName.toUpperCase());
    
    if (!adapter) {
      throw new Error(`Adapter not found for provider: ${providerName}`);
    }

    return adapter;
  }

  /**
   * Verifica se um provedor está disponível
   */
  hasAdapter(providerName: string): boolean {
    return this.adapters.has(providerName.toUpperCase());
  }

  /**
   * Lista todos os provedores disponíveis
   */
  getAvailableProviders(): string[] {
    return Array.from(this.adapters.keys());
  }

  /**
   * Obtém informações de todos os provedores
   */
  getProvidersInfo(): Array<{ name: string; version: string; enabled: boolean }> {
    return Array.from(this.adapters.values()).map(adapter => ({
      name: adapter.name,
      version: adapter.version,
      enabled: true, // TODO: implementar controle de habilitação
    }));
  }

  /**
   * Detecta o provedor baseado na requisição
   */
  detectProvider(request: any, headers: Record<string, string>): string {
    // Estratégias de detecção do provedor:
    
    // 1. Header específico
    if (headers['x-provider']) {
      return headers['x-provider'].toUpperCase();
    }

    // 2. User-Agent
    if (headers['user-agent']) {
      const userAgent = headers['user-agent'].toLowerCase();
      if (userAgent.includes('egt')) return 'EGT';
      if (userAgent.includes('bgaming')) return 'BGAMING';
    }

    // 3. Estrutura da requisição (campos específicos)
    if (request) {
      // EGT usa user_id
      if (request.user_id && request.game_id) {
        return 'EGT';
      }
      
      // BGaming usa playerId
      if (request.playerId && request.gameCode) {
        return 'BGAMING';
      }
    }

    // 4. URL path
    if (headers['x-original-url']) {
      const path = headers['x-original-url'].toLowerCase();
      if (path.includes('/egt/')) return 'EGT';
      if (path.includes('/bgaming/')) return 'BGAMING';
    }

    throw new Error('Unable to detect provider from request');
  }

  /**
   * Valida se um provedor suporta uma operação
   */
  validateProviderOperation(providerName: string, operation: string): boolean {
    try {
      const adapter = this.getAdapter(providerName);
      // Aqui você pode implementar lógica específica de validação
      // Por enquanto, assumimos que todos os adapters suportam as operações básicas
      return ['balance', 'bet', 'win', 'rollback', 'refund'].includes(operation);
    } catch {
      return false;
    }
  }
}
