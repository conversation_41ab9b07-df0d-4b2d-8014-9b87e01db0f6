import { Entity, PrimaryGeneratedColumn, Column } from "typeorm";

@Entity({ schema: "casino", name: "daily_report_partner" })
export class DailyReportPartner {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", nullable: true })
  currency: string;

  @Column("numeric", { name: "total_bet", nullable: true })
  totalBet: number;

  @Column("numeric", { name: "total_settle", nullable: true })
  totalSettle: number;

  @Column("numeric", { nullable: true })
  ggr: number;

  @Column("numeric", { name: "total_round", nullable: true })
  totalRound: number;

  @Column("uuid", { name: "partner_id", nullable: true })
  partnerId: string;

  @Column({ name: "partner_name", type: "varchar", nullable: true })
  partnerName: string;

  @Column({ type: "timestamptz", nullable: true })
  date: Date;
}
