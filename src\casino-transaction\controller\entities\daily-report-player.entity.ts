import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  Unique,
  UpdateDateColumn,
} from 'typeorm';

@Entity({ schema: 'casino', name: 'daily_report_player' })
@Unique('uq_daily_report_player_player_partner_date', [
  'playerId',
  'partnerId',
  'date',
])
export class DailyReportPlayer {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid', { name: 'player_id', nullable: true })
  playerId: string;

  @Column({ type: 'varchar', nullable: true })
  currency: string;

  @Column('numeric', { name: 'total_bet', nullable: true })
  totalBet: number;

  @Column('numeric', { name: 'total_settle', nullable: true })
  totalSettle: number;

  @Column('numeric', { nullable: true })
  ggr: number;

  @Column({ name: 'email', type: 'varchar', nullable: true })
  email: string;

  @Column('uuid', { name: 'partner_id', nullable: true })
  partnerId: string;

  @Column({ type: 'date', nullable: true })
  date: Date;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;
}
