import { Entity, PrimaryGeneratedColumn, Column } from "typeorm";

@Entity({ schema: "casino", name: "daily_report_player" })
export class DailyReportPlayer {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column("uuid", { name: "player_id", nullable: true })
  playerId: string;

  @Column({ type: "varchar", nullable: true })
  currency: string;

  @Column("numeric", { name: "total_bet", nullable: true })
  totalBet: number;

  @Column("numeric", { name: "total_settle", nullable: true })
  totalSettle: number;

  @Column("numeric", { nullable: true })
  ggr: number;

  @Column({ type: "varchar", nullable: true })
  email: string;

  @Column("uuid", { name: "partner_id", nullable: true })
  partnerId: string;

  @Column({ type: "timestamptz", nullable: true })
  date: Date;
}
