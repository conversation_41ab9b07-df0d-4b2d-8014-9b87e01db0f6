import { S3Service } from '@/common/services/aws/s3.service';
import { HttpException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { EntityManager } from 'typeorm';
import { CasinoGames } from '../entities/casino-game.entity';

describe('BackofficeCasinoGamesService', () => {
  let service: BackofficeCasinoGamesService;
  let casinoManager: any;

  beforeEach(async () => {
    const mockQueryBuilder = {
      select: jest.fn().mockReturnThis(),
      addSelect: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      getRawMany: jest.fn(),
      getRawOne: jest.fn(),
      getManyAndCount: jest.fn(),
      getCount: jest.fn(),
    };

    casinoManager = {
      insert: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
      softDelete: jest.fn(),
      transaction: jest.fn(),
      query: jest.fn(),
      createQueryBuilder: jest.fn(() => mockQueryBuilder),
      count: jest.fn(),
    };
    const mockS3Service = {
      uploadFile: jest.fn(),
      uploadBase64File: jest.fn(),
      deleteFile: jest.fn(),
      getKeyFromUrl: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BackofficeCasinoGamesService,
        { provide: EntityManager, useValue: casinoManager },
        { provide: S3Service, useValue: mockS3Service },
      ],
    }).compile();

    service = module.get<BackofficeCasinoGamesService>(
      BackofficeCasinoGamesService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('deve criar um novo jogo com sucesso', async () => {
      casinoManager.insert.mockResolvedValue({});
      const dto = {
        name: 'Jogo',
        description: 'desc',
        image: 'img.jpg',
        isDisable: false,
        gameId: '1',
        gameProvider: 'prov',
        gameProviderId: 'provider-uuid-123',
        gameCategoryId: 'category-uuid-123',
        userLastUpdate: 'admin',
      };
      // Mock S3Service para evitar erro no generateCustomFileName
      const mockS3Service = service['s3Service'];
      mockS3Service.uploadFile = jest.fn();
      mockS3Service.uploadBase64File = jest.fn();
      const result = await service.create(dto);
      expect(result).toEqual({
        message: 'The record has been successfully created.',
      });
      expect(casinoManager.insert).toHaveBeenCalledWith(
        CasinoGames,
        expect.any(Object),
      );
    });
    it('deve lançar exceção ao falhar', async () => {
      casinoManager.insert.mockRejectedValue(new HttpException('erro', 400));
      await expect(service.create({} as any)).rejects.toThrow(HttpException);
    });
    it('deve lançar HttpException padrão para erro genérico', async () => {
      casinoManager.insert.mockRejectedValue(new Error('erro generico'));
      await expect(service.create({} as any)).rejects.toThrow(HttpException);
    });
    it('deve chamar logger.error em erro', async () => {
      const loggerSpy = jest
        .spyOn(service['logger'], 'error')
        .mockImplementation();
      casinoManager.insert.mockRejectedValue(new Error('erro logger'));
      await expect(service.create({} as any)).rejects.toThrow(HttpException);
      expect(loggerSpy).toHaveBeenCalled();
      loggerSpy.mockRestore();
    });
  });

  describe('findAll', () => {
    it('deve retornar lista de jogos', async () => {
      const games = [
        { id: 1, name: 'A' },
        { id: 2, name: 'B' },
      ];
      const mockQueryBuilder = casinoManager.createQueryBuilder();
      mockQueryBuilder.getRawMany.mockResolvedValue(games);
      const result = await service.findAll({});
      expect(result).toEqual(games);
      expect(casinoManager.createQueryBuilder).toHaveBeenCalledWith(
        CasinoGames,
        'casino_games',
      );
      expect(mockQueryBuilder.getRawMany).toHaveBeenCalled();
    });
    it('deve lançar exceção ao falhar', async () => {
      const mockQueryBuilder = casinoManager.createQueryBuilder();
      mockQueryBuilder.getRawMany.mockRejectedValue(
        new HttpException('erro', 400),
      );
      await expect(service.findAll({})).rejects.toThrow(HttpException);
    });
    it('deve lançar HttpException padrão para erro genérico', async () => {
      const mockQueryBuilder = casinoManager.createQueryBuilder();
      mockQueryBuilder.getRawMany.mockRejectedValue(new Error('erro generico'));
      await expect(service.findAll({})).rejects.toThrow(HttpException);
    });
    it('deve chamar logger.error em erro', async () => {
      const loggerSpy = jest
        .spyOn(service['logger'], 'error')
        .mockImplementation();
      const mockQueryBuilder = casinoManager.createQueryBuilder();
      mockQueryBuilder.getRawMany.mockRejectedValue(new Error('erro logger'));
      await expect(service.findAll({})).rejects.toThrow(HttpException);
      expect(loggerSpy).toHaveBeenCalled();
      loggerSpy.mockRestore();
    });
  });

  describe('findOne', () => {
    it('deve retornar um jogo pelo id', async () => {
      const mockQueryBuilder = casinoManager.createQueryBuilder();
      mockQueryBuilder.getRawOne.mockResolvedValue({ id: '1', name: 'Jogo' });
      const result = await service.findOne('1');
      expect(result).toEqual({ id: '1', name: 'Jogo' });
      expect(casinoManager.createQueryBuilder).toHaveBeenCalledWith(
        CasinoGames,
        'casino_games',
      );
      expect(mockQueryBuilder.getRawOne).toHaveBeenCalled();
    });
    it('deve lançar exceção se não encontrar', async () => {
      const mockQueryBuilder = casinoManager.createQueryBuilder();
      mockQueryBuilder.getRawOne.mockResolvedValue(undefined);
      await expect(service.findOne('2')).rejects.toThrow(HttpException);
    });
    it('deve lançar exceção ao falhar', async () => {
      const mockQueryBuilder = casinoManager.createQueryBuilder();
      mockQueryBuilder.getRawOne.mockRejectedValue(
        new HttpException('erro', 400),
      );
      await expect(service.findOne('3')).rejects.toThrow(HttpException);
    });
    it('deve lançar HttpException padrão para erro genérico', async () => {
      const mockQueryBuilder = casinoManager.createQueryBuilder();
      mockQueryBuilder.getRawOne.mockRejectedValue(new Error('erro generico'));
      await expect(service.findOne('3')).rejects.toThrow(HttpException);
    });
    it('deve chamar logger.error em erro', async () => {
      const loggerSpy = jest
        .spyOn(service['logger'], 'error')
        .mockImplementation();
      const mockQueryBuilder = casinoManager.createQueryBuilder();
      mockQueryBuilder.getRawOne.mockRejectedValue(new Error('erro logger'));
      await expect(service.findOne('3')).rejects.toThrow(HttpException);
      expect(loggerSpy).toHaveBeenCalled();
      loggerSpy.mockRestore();
    });
    it('deve chamar logger.warn se não encontrar registro', async () => {
      const loggerSpy = jest
        .spyOn(service['logger'], 'warn')
        .mockImplementation();
      const mockQueryBuilder = casinoManager.createQueryBuilder();
      mockQueryBuilder.getRawOne.mockResolvedValue(undefined);
      await expect(service.findOne('2')).rejects.toThrow(HttpException);
      expect(loggerSpy).toHaveBeenCalled();
      loggerSpy.mockRestore();
    });
  });

  describe('update', () => {
    it('deve atualizar um jogo com sucesso', async () => {
      casinoManager.transaction.mockImplementation(async cb => {
        await cb({
          update: casinoManager.update,
          findOne: casinoManager.findOne,
        });
      });
      casinoManager.update.mockResolvedValue({});
      const result = await service.update('1', { name: 'Novo' });
      expect(result).toBe('The record has been successfully updated.');
      expect(casinoManager.transaction).toHaveBeenCalled();
    });

    it('deve atualizar com imagem quando image é fornecido', async () => {
      const mockImage = {
        buffer: Buffer.from('test'),
        originalname: 'test.jpg',
      } as Express.Multer.File;
      const mockExistingGame = {
        id: '1',
        gameId: 'game-1',
        gameProvider: 'provider-1',
        name: 'Game 1',
        image: 'https://old-image.jpg',
      };
      const mockS3Service = service['s3Service'];
      mockS3Service.uploadFile = jest.fn().mockResolvedValue({
        url: 'https://new-image.jpg',
        path: 'casino_games/new-image.jpg',
      });
      mockS3Service.deleteFileByUrl = jest.fn().mockResolvedValue(undefined);

      casinoManager.transaction.mockImplementation(async cb => {
        const mockTransactionalManager = {
          findOne: jest.fn().mockResolvedValue(mockExistingGame),
          update: jest.fn().mockResolvedValue({}),
        };
        return cb(mockTransactionalManager);
      });

      const result = await service.update('1', { name: 'Novo' }, mockImage);
      expect(result).toBe('The record has been successfully updated.');
      expect(mockS3Service.uploadFile).toHaveBeenCalled();
      expect(mockS3Service.deleteFileByUrl).toHaveBeenCalledWith(
        'https://old-image.jpg',
      );
    });

    it('deve atualizar com imageBase64 quando imageBase64 é fornecido', async () => {
      const mockExistingGame = {
        id: '1',
        gameId: 'game-1',
        gameProvider: 'provider-1',
        name: 'Game 1',
        image: 'https://old-image.jpg',
      };
      const mockS3Service = service['s3Service'];
      mockS3Service.uploadBase64File = jest.fn().mockResolvedValue({
        url: 'https://new-image.jpg',
        path: 'casino_games/new-image.jpg',
      });
      mockS3Service.deleteFileByUrl = jest.fn().mockResolvedValue(undefined);

      casinoManager.transaction.mockImplementation(async cb => {
        const mockTransactionalManager = {
          findOne: jest.fn().mockResolvedValue(mockExistingGame),
          update: jest.fn().mockResolvedValue({}),
        };
        return cb(mockTransactionalManager);
      });

      const result = await service.update('1', {
        name: 'Novo',
        imageBase64: 'data:image/png;base64,iVBORw0KGg==',
      });
      expect(result).toBe('The record has been successfully updated.');
      expect(mockS3Service.uploadBase64File).toHaveBeenCalled();
    });

    it('deve continuar mesmo se deletar imagem antiga falhar', async () => {
      const mockImage = {
        buffer: Buffer.from('test'),
        originalname: 'test.jpg',
      } as Express.Multer.File;
      const mockExistingGame = {
        id: '1',
        gameId: 'game-1',
        gameProvider: 'provider-1',
        name: 'Game 1',
        image: 'https://old-image.jpg',
      };
      const mockS3Service = service['s3Service'];
      mockS3Service.uploadFile = jest.fn().mockResolvedValue({
        url: 'https://new-image.jpg',
        path: 'casino_games/new-image.jpg',
      });
      mockS3Service.deleteFileByUrl = jest
        .fn()
        .mockRejectedValue(new Error('Delete failed'));

      casinoManager.transaction.mockImplementation(async cb => {
        const mockTransactionalManager = {
          findOne: jest.fn().mockResolvedValue(mockExistingGame),
          update: jest.fn().mockResolvedValue({}),
        };
        return cb(mockTransactionalManager);
      });

      const result = await service.update('1', { name: 'Novo' }, mockImage);
      expect(result).toBe('The record has been successfully updated.');
      expect(mockS3Service.deleteFileByUrl).toHaveBeenCalled();
    });
    it('deve lançar exceção ao falhar', async () => {
      casinoManager.transaction.mockImplementation(async _cb => {
        throw new HttpException('erro', 400);
      });
      await expect(service.update('1', { name: 'Novo' })).rejects.toThrow(
        HttpException,
      );
    });
    it('deve lançar HttpException padrão para erro genérico', async () => {
      casinoManager.transaction.mockImplementation(async _cb => {
        throw new Error('erro generico');
      });
      await expect(service.update('1', { name: 'Novo' })).rejects.toThrow(
        HttpException,
      );
    });
    it('deve chamar logger.error em erro', async () => {
      const loggerSpy = jest
        .spyOn(service['logger'], 'error')
        .mockImplementation();
      casinoManager.transaction.mockImplementation(async _cb => {
        throw new Error('erro logger');
      });
      await expect(service.update('1', { name: 'Novo' })).rejects.toThrow(
        HttpException,
      );
      expect(loggerSpy).toHaveBeenCalled();
      loggerSpy.mockRestore();
    });
  });

  describe('remove', () => {
    it('deve remover um jogo com sucesso', async () => {
      casinoManager.transaction.mockImplementation(async _cb => {
        await cb({ softDelete: casinoManager.softDelete });
      });
      casinoManager.softDelete.mockResolvedValue({});
      const result = await service.remove('1');
      expect(result).toBe('The record has been successfully deleted.');
      expect(casinoManager.transaction).toHaveBeenCalled();
    });
    it('deve lançar exceção ao falhar', async () => {
      casinoManager.transaction.mockImplementation(async _cb => {
        throw new HttpException('erro', 400);
      });
      await expect(service.remove('1')).rejects.toThrow(HttpException);
    });
    it('deve lançar HttpException padrão para erro genérico', async () => {
      casinoManager.transaction.mockImplementation(async _cb => {
        throw new Error('erro generico');
      });
      await expect(service.remove('1')).rejects.toThrow(HttpException);
    });
    it('deve chamar logger.error em erro', async () => {
      const loggerSpy = jest
        .spyOn(service['logger'], 'error')
        .mockImplementation();
      casinoManager.transaction.mockImplementation(async _cb => {
        throw new Error('erro logger');
      });
      await expect(service.remove('1')).rejects.toThrow(HttpException);
      expect(loggerSpy).toHaveBeenCalled();
      loggerSpy.mockRestore();
    });
  });

  describe('getAllProvides', () => {
    it('deve retornar lista ordenada de providers', async () => {
      const mockFilter = { page: 1, pageSize: 10 } as any;
      const countQueryBuilder = casinoManager.createQueryBuilder();
      const queryBuilder = casinoManager.createQueryBuilder();
      countQueryBuilder.getCount.mockResolvedValue(3);
      queryBuilder.getRawMany.mockResolvedValue([
        { id: '1', name: 'A' },
        { id: '2', name: 'B' },
        { id: '3', name: 'C' },
      ]);
      const result = await service.getAllProvides(mockFilter);
      expect(result).toBeDefined();
      expect(result.data).toHaveLength(3);
      expect(result.totalItems).toBe(3);
      expect(result.currentPage).toBe(1);
      expect(result.pageSize).toBe(10);
    });
    it('deve retornar lista vazia se não houver providers', async () => {
      const mockFilter = { page: 1, pageSize: 10 } as any;
      const countQueryBuilder = casinoManager.createQueryBuilder();
      const queryBuilder = casinoManager.createQueryBuilder();
      countQueryBuilder.getCount.mockResolvedValue(0);
      queryBuilder.getRawMany.mockResolvedValue([]);
      const result = await service.getAllProvides(mockFilter);
      expect(result).toBeDefined();
      expect(result.data).toHaveLength(0);
      expect(result.totalItems).toBe(0);
    });

    it('deve usar sortOrder ASC como padrão quando não fornecido', async () => {
      const mockFilter = { page: 1, pageSize: 10 } as any;
      const countQueryBuilder = casinoManager.createQueryBuilder();
      const queryBuilder = casinoManager.createQueryBuilder();
      countQueryBuilder.getCount.mockResolvedValue(2);
      queryBuilder.getRawMany.mockResolvedValue([
        { id: '1', name: 'A' },
        { id: '2', name: 'B' },
      ]);
      await service.getAllProvides(mockFilter);
      expect(queryBuilder.orderBy).toHaveBeenCalledWith(
        'providers.name',
        'ASC',
      );
    });

    it('deve usar page 1 como padrão quando page é null', async () => {
      const mockFilter = { page: null, pageSize: 10 } as any;
      const countQueryBuilder = casinoManager.createQueryBuilder();
      const queryBuilder = casinoManager.createQueryBuilder();
      countQueryBuilder.getCount.mockResolvedValue(2);
      queryBuilder.getRawMany.mockResolvedValue([{ id: '1', name: 'A' }]);
      const result = await service.getAllProvides(mockFilter);
      expect(result.currentPage).toBe(1);
    });

    it('deve usar page 1 quando page é menor que 1', async () => {
      const mockFilter = { page: 0, pageSize: 10 } as any;
      const countQueryBuilder = casinoManager.createQueryBuilder();
      const queryBuilder = casinoManager.createQueryBuilder();
      countQueryBuilder.getCount.mockResolvedValue(2);
      queryBuilder.getRawMany.mockResolvedValue([{ id: '1', name: 'A' }]);
      const result = await service.getAllProvides(mockFilter);
      expect(result.currentPage).toBe(1);
    });

    it('deve usar pageSize 10 como padrão quando pageSize é null', async () => {
      const mockFilter = { page: 1, pageSize: null } as any;
      const countQueryBuilder = casinoManager.createQueryBuilder();
      const queryBuilder = casinoManager.createQueryBuilder();
      countQueryBuilder.getCount.mockResolvedValue(2);
      queryBuilder.getRawMany.mockResolvedValue([{ id: '1', name: 'A' }]);
      const result = await service.getAllProvides(mockFilter);
      expect(result.pageSize).toBe(10);
    });

    it('deve usar pageSize 10 quando pageSize é menor que 1', async () => {
      const mockFilter = { page: 1, pageSize: 0 } as any;
      const countQueryBuilder = casinoManager.createQueryBuilder();
      const queryBuilder = casinoManager.createQueryBuilder();
      countQueryBuilder.getCount.mockResolvedValue(2);
      queryBuilder.getRawMany.mockResolvedValue([{ id: '1', name: 'A' }]);
      const result = await service.getAllProvides(mockFilter);
      expect(result.pageSize).toBe(10);
    });

    it('deve filtrar por id quando filter.id é fornecido', async () => {
      const mockFilter = { page: 1, pageSize: 10, id: 'provider-123' } as any;
      const countQueryBuilder = casinoManager.createQueryBuilder();
      const queryBuilder = casinoManager.createQueryBuilder();
      countQueryBuilder.getCount.mockResolvedValue(1);
      queryBuilder.getRawMany.mockResolvedValue([
        { id: 'provider-123', name: 'Provider' },
      ]);
      await service.getAllProvides(mockFilter);
      expect(countQueryBuilder.andWhere).toHaveBeenCalledWith(
        'providers.id = :id',
        { id: 'provider-123' },
      );
      expect(queryBuilder.andWhere).toHaveBeenCalledWith('providers.id = :id', {
        id: 'provider-123',
      });
    });

    it('deve filtrar por name quando filter.name é fornecido', async () => {
      const mockFilter = { page: 1, pageSize: 10, name: 'Test' } as any;
      const countQueryBuilder = casinoManager.createQueryBuilder();
      const queryBuilder = casinoManager.createQueryBuilder();
      countQueryBuilder.getCount.mockResolvedValue(1);
      queryBuilder.getRawMany.mockResolvedValue([
        { id: '1', name: 'Test Provider' },
      ]);
      await service.getAllProvides(mockFilter);
      expect(countQueryBuilder.andWhere).toHaveBeenCalledWith(
        'providers.name ILIKE :name',
        { name: '%Test%' },
      );
      expect(queryBuilder.andWhere).toHaveBeenCalledWith(
        'providers.name ILIKE :name',
        { name: '%Test%' },
      );
    });
  });
});
