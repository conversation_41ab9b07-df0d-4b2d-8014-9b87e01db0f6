import { Test, TestingModule } from '@nestjs/testing';
import { BackofficeCasinoGamesService } from './backoffice-casino-games.service';
import { EntityManager } from 'typeorm';
import { HttpException } from '@nestjs/common';
import { CasinoGames } from '../entities/casino-game.entity';

describe('BackofficeCasinoGamesService', () => {
  let service: BackofficeCasinoGamesService;
  let casinoManager: any;

  beforeEach(async () => {
    casinoManager = {
      insert: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
      softDelete: jest.fn(),
      transaction: jest.fn(),
      query: jest.fn(),
    };
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BackofficeCasinoGamesService,
        { provide: EntityManager, useValue: casinoManager },
      ],
    }).compile();

    service = module.get<BackofficeCasinoGamesService>(BackofficeCasinoGamesService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('deve criar um novo jogo com sucesso', async () => {
      casinoManager.insert.mockResolvedValue({});
      const dto = {
        id: 'game-1',
        name: 'Jogo',
        description: 'desc',
        image: 'img.jpg',
        isDisable: false,
        gameId: '1',
        gameProvider: 'prov',
        userLastUpdate: 'admin',
      };
      const result = await service.create(dto);
      expect(result).toBe('The record has been successfully created.');
      expect(casinoManager.insert).toHaveBeenCalledWith(CasinoGames, dto);
    });
    it('deve lançar exceção ao falhar', async () => {
      casinoManager.insert.mockRejectedValue(new HttpException('erro', 400));
      await expect(service.create({} as any)).rejects.toThrow(HttpException);
    });
    it('deve lançar HttpException padrão para erro genérico', async () => {
      casinoManager.insert.mockRejectedValue(new Error('erro generico'));
      await expect(service.create({} as any)).rejects.toThrow(HttpException);
    });
    it('deve chamar logger.error em erro', async () => {
      const loggerSpy = jest.spyOn(service['logger'], 'error').mockImplementation();
      casinoManager.insert.mockRejectedValue(new Error('erro logger'));
      await expect(service.create({} as any)).rejects.toThrow(HttpException);
      expect(loggerSpy).toHaveBeenCalled();
      loggerSpy.mockRestore();
    });
  });

  describe('findAll', () => {
    it('deve retornar lista de jogos', async () => {
      const games = [{ id: 1, name: 'A' }, { id: 2, name: 'B' }];
      casinoManager.find.mockResolvedValue(games);
      const result = await service.findAll({});
      expect(result).toEqual(games);
      expect(casinoManager.find).toHaveBeenCalledWith(CasinoGames, {
        select: ['id', 'name', 'description', 'gameId', 'gameProvider'],
      });
    });
    it('deve lançar exceção ao falhar', async () => {
      casinoManager.find.mockRejectedValue(new HttpException('erro', 400));
      await expect(service.findAll({})).rejects.toThrow(HttpException);
    });
    it('deve lançar HttpException padrão para erro genérico', async () => {
      casinoManager.find.mockRejectedValue(new Error('erro generico'));
      await expect(service.findAll({})).rejects.toThrow(HttpException);
    });
    it('deve chamar logger.error em erro', async () => {
      const loggerSpy = jest.spyOn(service['logger'], 'error').mockImplementation();
      casinoManager.find.mockRejectedValue(new Error('erro logger'));
      await expect(service.findAll({})).rejects.toThrow(HttpException);
      expect(loggerSpy).toHaveBeenCalled();
      loggerSpy.mockRestore();
    });
  });

  describe('findOne', () => {
    it('deve retornar um jogo pelo id', async () => {
      casinoManager.findOne.mockReturnValue(Promise.resolve({ id: '1', name: 'Jogo' }));
      const result = await service.findOne('1');
      expect(result).toEqual({ id: '1', name: 'Jogo' });
      expect(casinoManager.findOne).toHaveBeenCalledWith(CasinoGames, { where: { id: '1' } });
    });
    it('deve lançar exceção se não encontrar', async () => {
      casinoManager.findOne.mockReturnValue(Promise.resolve(undefined));
      await expect(service.findOne('2')).rejects.toThrow(HttpException);
    });
    it('deve lançar exceção ao falhar', async () => {
      casinoManager.findOne.mockReturnValue(Promise.reject(new HttpException('erro', 400)));
      await expect(service.findOne('3')).rejects.toThrow(HttpException);
    });
    it('deve lançar HttpException padrão para erro genérico', async () => {
      casinoManager.findOne.mockReturnValue(Promise.reject(new Error('erro generico')));
      await expect(service.findOne('3')).rejects.toThrow(HttpException);
    });
    it('deve chamar logger.error em erro', async () => {
      const loggerSpy = jest.spyOn(service['logger'], 'error').mockImplementation();
      casinoManager.findOne.mockReturnValue(Promise.reject(new Error('erro logger')));
      await expect(service.findOne('3')).rejects.toThrow(HttpException);
      expect(loggerSpy).toHaveBeenCalled();
      loggerSpy.mockRestore();
    });
    it('deve chamar logger.warn se não encontrar registro', async () => {
      const loggerSpy = jest.spyOn(service['logger'], 'warn').mockImplementation();
      casinoManager.findOne.mockReturnValue(Promise.resolve(undefined));
      await expect(service.findOne('2')).rejects.toThrow(HttpException);
      expect(loggerSpy).toHaveBeenCalled();
      loggerSpy.mockRestore();
    });
  });

  describe('update', () => {
    it('deve atualizar um jogo com sucesso', async () => {
      casinoManager.transaction.mockImplementation(async (cb) => {
        await cb({ update: casinoManager.update });
      });
      casinoManager.update.mockResolvedValue({});
      const result = await service.update('1', { name: 'Novo' });
      expect(result).toBe('The record has been successfully updated.');
      expect(casinoManager.transaction).toHaveBeenCalled();
    });
    it('deve lançar exceção ao falhar', async () => {
      casinoManager.transaction.mockImplementation(async (cb) => {
        throw new HttpException('erro', 400);
      });
      await expect(service.update('1', { name: 'Novo' })).rejects.toThrow(HttpException);
    });
    it('deve lançar HttpException padrão para erro genérico', async () => {
      casinoManager.transaction.mockImplementation(async (cb) => { throw new Error('erro generico'); });
      await expect(service.update('1', { name: 'Novo' })).rejects.toThrow(HttpException);
    });
    it('deve chamar logger.error em erro', async () => {
      const loggerSpy = jest.spyOn(service['logger'], 'error').mockImplementation();
      casinoManager.transaction.mockImplementation(async (cb) => { throw new Error('erro logger'); });
      await expect(service.update('1', { name: 'Novo' })).rejects.toThrow(HttpException);
      expect(loggerSpy).toHaveBeenCalled();
      loggerSpy.mockRestore();
    });
  });

  describe('remove', () => {
    it('deve remover um jogo com sucesso', async () => {
      casinoManager.transaction.mockImplementation(async (cb) => {
        await cb({ softDelete: casinoManager.softDelete });
      });
      casinoManager.softDelete.mockResolvedValue({});
      const result = await service.remove('1');
      expect(result).toBe('The record has been successfully deleted.');
      expect(casinoManager.transaction).toHaveBeenCalled();
    });
    it('deve lançar exceção ao falhar', async () => {
      casinoManager.transaction.mockImplementation(async (cb) => {
        throw new HttpException('erro', 400);
      });
      await expect(service.remove('1')).rejects.toThrow(HttpException);
    });
    it('deve lançar HttpException padrão para erro genérico', async () => {
      casinoManager.transaction.mockImplementation(async (cb) => { throw new Error('erro generico'); });
      await expect(service.remove('1')).rejects.toThrow(HttpException);
    });
    it('deve chamar logger.error em erro', async () => {
      const loggerSpy = jest.spyOn(service['logger'], 'error').mockImplementation();
      casinoManager.transaction.mockImplementation(async (cb) => { throw new Error('erro logger'); });
      await expect(service.remove('1')).rejects.toThrow(HttpException);
      expect(loggerSpy).toHaveBeenCalled();
      loggerSpy.mockRestore();
    });
  });

  describe('getAllProvides', () => {
    it('deve retornar lista ordenada de providers', async () => {
      casinoManager.query.mockResolvedValue([
        { gameProvider: 'B' },
        { gameProvider: 'A' },
        { gameProvider: 'C' },
      ]);
      const result = await service.getAllProvides();
      expect(result).toEqual(['A', 'B', 'C']);
    });
    it('deve retornar lista vazia se não houver providers', async () => {
      casinoManager.query.mockResolvedValue([]);
      const result = await service.getAllProvides();
      expect(result).toEqual([]);
    }); 
  });
}); 