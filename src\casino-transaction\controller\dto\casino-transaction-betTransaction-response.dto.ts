import { ApiProperty } from '@nestjs/swagger';

export class CasinoTransactionBetTransactionResponseDto {
  @ApiProperty({ description: 'Receita total' })
  income: number;

  @ApiProperty({ description: 'Lucro total' })
  profit: number;

  @ApiProperty({ description: 'GGR total' })
  GGR: number;

  @ApiProperty({ description: 'Rentabilidade' })
  profitability: number;

  @ApiProperty({ description: 'Média de apostas' })
  averageBet: number;

  @ApiProperty({ description: 'Quantidade de jogadores únicos' })
  totalUniquePlayers: number;
}

export const CasinoTransactionBetTransactionResponseDtoExample = {
  income: 10000,
  profit: 8000,
  GGR: 2000,
  profitability: 20,
  averageBet: 50.25,
  totalUniquePlayers: 150,
};
