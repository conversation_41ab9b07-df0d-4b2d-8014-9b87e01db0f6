import { Modu<PERSON> } from "@nestjs/common";
import { HMACModule } from "./hmac/hmac.module";
import { GameLaunchModule } from "./gamelaunch/gamelaunch.module";
import { AuthenticateModule } from "./authenticate/authenticate.module";
import { BetModule } from "./bet/bet.module";
import { WalletModule } from "./wallet/wallet.module";
import { BalanceModule } from "./balance/balance.module";
import { BetSettleModule } from "./betsettle/betsettle.module";
import { CancelBetModule } from "./cancel-bet/cancel-bet.module";
import { SettleModule } from "./settle/settle.module";
import { NewSessionModule } from "./new-session/new-session.module";
import { CasinoGamesModule } from "./casino-games/casino-games.module";
import { HealthModule } from "./health/health.module";
import { CasinoTransactionModule } from "./casino-transaction/casino-transaction.module";
import { DateUtilsService } from "./common/utils/date";
import { HistoryGameModule } from './history-game/history-game.module';
import { JwtModule } from "@nestjs/jwt";

const modules = [
  GameLaunchModule,
  AuthenticateModule,
  BetModule,
  HMACModule,
  WalletModule,
  BetSettleModule,
  BalanceModule,
  CancelBetModule,
  SettleModule,
  NewSessionModule,
  CasinoGamesModule,
  HealthModule,
  CasinoTransactionModule,
  HistoryGameModule
];

@Module({
  providers: [DateUtilsService],
  imports: [
    ...modules,
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: '1d' },
      global: true,
    }),
  ],
  exports: [...modules, DateUtilsService],
})
export class AppModule {}
