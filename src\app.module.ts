import { RabbitmqModule } from '@h2/rabbitmq';
import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { AuthenticateModule } from './authenticate/authenticate.module';
import { BalanceModule } from './balance/balance.module';
import { BetModule } from './bet/bet.module';
import { BetSettleModule } from './betsettle/betsettle.module';
import { BonusModule } from './bonus/bonus.module';
import { CancelBetModule } from './cancel-bet/cancel-bet.module';
import { CasinoGamesModule } from './casino-games/casino-games.module';
import { CasinoTransactionModule } from './casino-transaction/casino-transaction.module';
import { DateUtilsService } from './common/utils/date';
import { CreateSessionK6Module } from './create-session-k6/create-session-k6.module';
import { FreechipModule } from './freechip/freechip.module';
import { FreespinModule } from './freespin/freespin.module';
import { GameLaunchModule } from './gamelaunch/gamelaunch.module';
import { GatewayIntegrationModule } from './gateway-integration/gateway-integration.module';
import { HealthModule } from './health/health.module';
import { HistoryGameModule } from './history-game/history-game.module';
import { HMACModule } from './hmac/hmac.module';
import { IntegrationsModule } from './integrations/integrations.module';
import { JackpotModule } from './jackpot/jackpot.module';
import { NewSessionModule } from './new-session/new-session.module';
import { PlayerModule } from './player/player.module';
import { PromoModule } from './promo/promo.module';
import { RollbackModule } from './rollback/rollback.module';
import { SettleModule } from './settle/settle.module';
import { WalletModule } from './wallet/wallet.module';

const modules = [
  GameLaunchModule,
  AuthenticateModule,
  BetModule,
  HMACModule,
  WalletModule,
  BetSettleModule,
  BalanceModule,
  CancelBetModule,
  SettleModule,
  NewSessionModule,
  CasinoGamesModule,
  HealthModule,
  CasinoTransactionModule,
  HistoryGameModule,
  PlayerModule,
  RollbackModule,
  CreateSessionK6Module,
  GatewayIntegrationModule,
];

@Module({
  providers: [DateUtilsService],
  imports: [
    ...modules,
    RabbitmqModule.forRoot({
      uri: process.env.RABBITMQ_URI,
    }),
    FreechipModule,
    FreespinModule,
    BonusModule,
    JackpotModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: '1d' },
      global: true,
    }),
    PromoModule,
    IntegrationsModule,
  ],
  exports: [...modules, DateUtilsService],
})
export class AppModule {}
