import { Modu<PERSON> } from '@nestjs/common';
import { HMACModule } from './hmac/hmac.module';
import { GameLaunchModule } from './gamelaunch/gamelaunch.module';
import { AuthenticateModule } from './authenticate/authenticate.module';
import { BetModule } from './bet/bet.module';
import { WalletModule } from './wallet/wallet.module';
import { BalanceModule } from './balance/balance.module';
import { BetSettleModule } from './betsettle/betsettle.module';
import { CancelBetModule } from './cancel-bet/cancel-bet.module';
import { SettleModule } from './settle/settle.module';
import { NewSessionModule } from './new-session/new-session.module';
import { CasinoGamesModule } from './casino-games/casino-games.module';
import { HealthModule } from './health/health.module';
import { CasinoTransactionModule } from './casino-transaction/casino-transaction.module';
import { DateUtilsService } from './common/utils/date';
import { HistoryGameModule } from './history-game/history-game.module';
import { JwtModule } from '@nestjs/jwt';
import { FreechipModule } from './freechip/freechip.module';
import { FreespinModule } from './freespin/freespin.module';
import { BonusModule } from './bonus/bonus.module';
import { JackpotModule } from './jackpot/jackpot.module';
import { PromoModule } from './promo/promo.module';
import { RabbitmqModule } from '@h2/rabbitmq';
import { PlayerModule } from './player/player.module';
import { RollbackModule } from './rollback/rollback.module';

const modules = [
  GameLaunchModule,
  AuthenticateModule,
  BetModule,
  HMACModule,
  WalletModule,
  BetSettleModule,
  BalanceModule,
  CancelBetModule,
  SettleModule,
  NewSessionModule,
  CasinoGamesModule,
  HealthModule,
  CasinoTransactionModule,
  HistoryGameModule,
  PlayerModule,
  RollbackModule,
];

@Module({
  providers: [DateUtilsService],
  imports: [
    ...modules,
    RabbitmqModule.forRoot({
      uri: process.env.RABBITMQ_URI,
    }),
    FreechipModule,
    FreespinModule,
    BonusModule,
    JackpotModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: '1d' },
      global: true,
    }),
    PromoModule,
  ],
  exports: [...modules, DateUtilsService],
})
export class AppModule {}
