import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity({ schema: 'casino', name: 'daily_report_game' })
export class DailyReportGame {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid', { name: 'game_id', nullable: true })
  gameId: string;

  @Column({ name: 'game_name', type: 'varchar', nullable: true })
  gameName: string;

  @Column({ name: 'game_provider', type: 'varchar', nullable: true })
  gameProvider: string;

  @Column({ type: 'varchar', nullable: true })
  currency: string;

  @Column('numeric', { name: 'total_bet', nullable: true })
  totalBet: number;

  @Column('numeric', { name: 'total_settle', nullable: true })
  totalSettle: number;

  @Column('numeric', { nullable: true })
  ggr: number;

  @Column('numeric', { name: 'total_round', nullable: true })
  totalRound: number;

  @Column('numeric', { name: 'total_players', nullable: true })
  totalPlayers: number;

  @Column('uuid', { name: 'partner_id', nullable: true })
  partnerId: string;

  @Column({ type: 'date', nullable: true })
  date: Date;
}
