import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { v4 as uuidV4 } from 'uuid';
import { CasinoTransactionEntity } from './casino-transactions.entity';

@Entity({ name: 'casino_transaction_details', schema: 'casino' })
export class CasinoTransactionDetailsEntity {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  @Column({
    name: 'casino_transactions_id',
    type: 'uuid',
    nullable: false,
  })
  casinoTransactionsId: string;

  @ManyToOne(
    () => CasinoTransactionEntity,
    transaction => transaction.details,
    { onDelete: 'RESTRICT' },
  )
  @JoinColumn({ name: 'casino_transactions_id' })
  transaction: CasinoTransactionEntity;

  @Column({
    name: 'transaction_id',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  transactionId: string;

  @Column({
    name: 'round_id',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  roundId: string;

  @Column({
    name: 'type',
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  type: string;

  @Column({
    name: 'amount',
    type: 'decimal',
    precision: 30,
    scale: 12,
    nullable: false,
  })
  amount: number;

  @Column({
    name: 'currency',
    type: 'varchar',
    length: 10,
    nullable: false,
  })
  currency: string;

  @Column({
    name: 'transaction_refund_id',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  transactionRefundId: string | null;

  @CreateDateColumn({ name: 'created_at', type: 'date' })
  createdAt: Date;

  constructor() {
    if (!this.id) {
      this.id = uuidV4();
    }
  }
}
