export function detectDeviceType(req: Request): 'mobile' | 'desktop' {
  const userAgent = req.headers['user-agent'] || '';

  const mobileRegex =
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;

  return mobileRegex.test(userAgent) ? 'mobile' : 'desktop';
}
export function getUserAgent(req: any): string {
  return req?.headers?.['user-agent'];
}

export function getIpAddress(req: any): string {
  if (!req?.headers) {
    return null;
  }

  return (
    req.headers['client-ip'] ||
    req.headers['x-forwarded-for'] ||
    req.connection?.remoteAddress
  );
}

export function getOrigin(req: any): string {
  return req?.headers?.['origin'] || process.env.ORIGIN_FASTTRACK;
}
