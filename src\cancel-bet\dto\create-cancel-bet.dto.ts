import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsString } from 'class-validator';

export enum RequestTypeEnum {
  FREESPIN = 'FreeSpin',
  REALMONEY = 'RealMoney',
  WAGER = 'Wager',
}

export class CancelBetDto {
  @ApiProperty({
    description: 'ID do jogador',
    example: 'player-123',
    required: true
  })
  @IsString()
  playerId: string;

  @ApiProperty({
    description: 'ID da sessão do jogador',
    example: 'session-123',
    required: true
  })
  @IsString()
  sessionId: string;

  @ApiProperty({
    description: 'ID da transação de cancelamento',
    example: 'tx-123',
    required: true
  })
  @IsString()
  transactionId: string;

  @ApiProperty({
    description: 'Tipo da requisição',
    enum: RequestTypeEnum,
    example: RequestTypeEnum.REALMONEY,
    required: true
  })
  @IsString()
  @IsEnum(RequestTypeEnum)
  requestType: string;

  @ApiProperty({
    description: 'ID do jogo',
    example: 123,
    required: true
  })
  @IsNumber()
  gameId: number;

  @ApiProperty({
    description: 'ID da transação original a ser cancelada',
    example: 'tx-456',
    required: true
  })
  @IsString()
  refTransactionId: string;

  @ApiProperty({
    description: 'ID da rodada',
    example: 'round-123',
    required: true
  })
  @IsString()
  roundId: string;

  @ApiProperty({
    description: 'Indica se a rodada foi finalizada',
    example: 'true',
    required: true
  })
  @IsString()
  roundEnded: string;
}
