import { Test, TestingModule } from '@nestjs/testing';
import { JackpotService } from './jackpot.service';
import { HttpService } from '@nestjs/axios';
import { of, throwError } from 'rxjs';
import { HttpException } from '@nestjs/common';

describe('JackpotService', () => {
  let service: JackpotService;
  let httpService: { post: jest.Mock };

  const GATEWAY_URL = 'http://gateway.test';

  beforeAll(() => {
    process.env.GATEWAY_URL = GATEWAY_URL;
  });

  beforeEach(async () => {
    httpService = { post: jest.fn() };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JackpotService,
        { provide: HttpService, useValue: httpService },
      ],
    }).compile();

    service = module.get<JackpotService>(JackpotService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('feed should POST to gateway and return data', async () => {
    const dto: any = { gameId: 'g1', amount: 10 };
    const data = { success: true };
    httpService.post.mockReturnValue(of({ data }));

    await expect(service.feed(dto)).resolves.toEqual(data);
    expect(httpService.post).toHaveBeenCalledWith(
      `${GATEWAY_URL}/casino/jackpot/feed`,
      dto,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-aggregator': 'softswiss',
        },
      },
    );
  });

  it('feed should wrap http errors in HttpException', async () => {
    const dto: any = { gameId: 'g1', amount: 10 };
    const error: any = { message: 'upstream failed', status: 500 };
    httpService.post.mockReturnValue(throwError(() => error));

    await expect(service.feed(dto)).rejects.toBeInstanceOf(HttpException);
    await expect(service.feed(dto)).rejects.toMatchObject({
      message: error.message,
    });
  });
});
