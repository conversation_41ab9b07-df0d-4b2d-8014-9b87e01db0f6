import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { NewSessionService } from './new-session.service';
import { CreateNewSessionDto } from './dto/create-new-session.dto';
import { HMACGuard } from '../common/guards/hmac.guard';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  ApiHmacHeader,
  ApiHmacUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@/common/decorators/swagger.decorator';
import {
  CreateNewSessionResponseDto,
  CreateNewSessionResponseDtoExample,
} from './dto/create-new-session-response.dto';
import { SwaggerApiCodeResponses } from '@/common/decorators/swagger-api-code-responses';
import {
  SUCCESS,
  GENERIC_ERROR,
  PLAYER_NOT_FOUND,
  SESSION_NOT_FOUND,
  GAME_NOT_FOUND,
} from '@/common/constants/message-codes';

@Controller('new-session')
@ApiTags('NewSession')
@ApiHmacHeader()
export class NewSessionController {
  constructor(private readonly newSessionService: NewSessionService) {}

  @UseGuards(HMACGuard)
  @Post()
  @ApiOperation({
    summary: 'Criar nova sessão',
    description: 'Endpoint para criar uma nova sessão de jogo',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
    ],
    CreateNewSessionResponseDto,
    CreateNewSessionResponseDtoExample
  )
  @ApiHmacUnauthorizedResponse()
  @ApiForbiddenResponse()
  @ApiInternalServerErrorResponse()
  create(@Body() createNewSessionDto: CreateNewSessionDto): Promise<{
    balance: number;
    errorCode: number;
    errorMsg?: string;
  }> {
    return this.newSessionService.create(createNewSessionDto);
  }
}
