import {
  GAME_NOT_FOUND,
  GENERIC_ERROR,
  PLAYER_NOT_FOUND,
  SESSION_NOT_FOUND,
  SUCCESS,
} from '@/common/constants/message-codes';
import { SwaggerApiCodeResponses } from '@/common/decorators/swagger-api-code-responses';
import {
  ApiForbiddenResponse,
  ApiHmacHeader,
  ApiHmacUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@/common/decorators/swagger.decorator';
import {
  Body,
  Controller,
  Patch,
  Post,
  Req,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { JwtAuthPlayerGuard } from '../common/guards/player/jwt-auth-player.guard';
import { CreateCasinoSessionResponseDto } from './dto/create-casino-session-response.dto';
import { CreateCasinoSessionDto } from './dto/create-casino-session.dto';
import {
  CreateNewSessionResponseDto,
  CreateNewSessionResponseDtoExample,
} from './dto/create-new-session-response.dto';
import { CreateNewSessionDto } from './dto/create-new-session.dto';
import { NewSessionService } from './new-session.service';
@Controller('session')
@ApiTags('NewSession')
export class NewSessionController {
  constructor(
    private readonly newSessionService: NewSessionService,
    private readonly jwtService: JwtService,
  ) {}

  //@UseGuards(HMACGuard)
  @Post()
  @ApiHmacHeader()
  @ApiOperation({
    summary: 'Criar nova sessão',
    description: 'Endpoint para criar uma nova sessão de jogo',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
    ],
    CreateNewSessionResponseDto,
    CreateNewSessionResponseDtoExample,
  )
  @ApiHmacUnauthorizedResponse()
  @ApiForbiddenResponse()
  @ApiInternalServerErrorResponse()
  create(@Body() createNewSessionDto: CreateNewSessionDto) {
    return this.newSessionService.create(createNewSessionDto);
  }

  @Post('new')
  @UseGuards(JwtAuthPlayerGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Criar nova sessão de casino' })
  async createCasinoSession(
    @Req() req: Request & { user: any },
    @Body() body: CreateCasinoSessionDto,
  ): Promise<CreateCasinoSessionResponseDto> {
    const authHeader = req.headers['authorization'];
    if (
      !authHeader ||
      typeof authHeader !== 'string' ||
      !authHeader.startsWith('Bearer ')
    ) {
      throw new UnauthorizedException('Token de autorização não fornecido');
    }
    try {
      const token = authHeader.substring(7);
      const decoded = this.jwtService.verify(token, {
        secret: process.env.JWT_SECRET_PLAYER,
      });
      const ip = req.ip as string;
      const userAgent = req.headers['user-agent'] as string;
      const userPayload = decoded.payload;
      return this.newSessionService.createCasinoSession(
        body,
        ip,
        userAgent,
        userPayload,
      );
    } catch (error) {
      throw new UnauthorizedException(`Token inválido ou expirado - ${error}`);
    }
  }

  @Patch('end')
  @UseGuards(JwtAuthPlayerGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Finalizar sessão de casino' })
  async endCasinoSession(
    @Req() req: Request,
  ): Promise<CreateCasinoSessionResponseDto> {
    const authHeader = req.headers['authorization'];
    if (
      !authHeader ||
      typeof authHeader !== 'string' ||
      !authHeader.startsWith('Bearer ')
    ) {
      throw new UnauthorizedException('Token de autorização não fornecido');
    }
    try {
      const token = authHeader.substring(7);
      const decoded = this.jwtService.verify(token, {
        secret: process.env.JWT_SECRET_PLAYER,
      });

      const { payload } = decoded;
      return this.newSessionService.endCasinoSession(payload);
    } catch (error) {
      throw new UnauthorizedException(
        `Token inválido ou expirado - ${error.message}`,
      );
    }
  }
}
