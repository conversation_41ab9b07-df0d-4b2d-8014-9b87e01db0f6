import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
} from "typeorm";

@Entity({ schema: "partners", name: "pam_partners_opah" })
export class PartnersEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "description_partner", type: "varchar", nullable: true })
  descriptionPartner?: string;

  @CreateDateColumn({ name: "created_at", type: "timestamptz", nullable: true })
  createdAt?: Date;

  @Column({ type: "bool", nullable: true })
  active?: boolean;

  @DeleteDateColumn({ name: "deleted_at", type: "timestamptz", nullable: true })
  deletedAt?: Date;

  @Column({
    name: "partner_cashier_url",
    type: "varchar",
    length: 255,
    nullable: true,
  })
  partnerCashierUrl?: string;

  @Column({
    name: "partner_close_url",
    type: "varchar",
    length: 255,
    nullable: true,
  })
  partnerCloseUrl?: string;

  @Column({ name: "basic_auth", type: "varchar", length: 255, nullable: true })
  basicAuth?: string;

  @Column({ name: "id_partner", type: "int", nullable: false })
  idPartner: number;
}
