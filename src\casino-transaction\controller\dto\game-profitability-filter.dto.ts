import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { GenericFilter } from "@/common/filters/generic-filter.dto";
import { DateRangeDto } from "@/common/filters/date-range.dto";
import { SortOrder } from "@/common/filters/sortOrder";
import { Transform } from "class-transformer";
import { IsNumber, IsOptional, IsString } from "class-validator";

export class GameProfitabilityFilterDto extends GenericFilter {
  @ApiProperty({
    description: "Game provider filter",
    required: false,
    example: "PRAGMATIC"
  })
  @IsOptional()
  @IsString()
  gameProvider?: string;

  dateRange: DateRangeDto;

  @ApiPropertyOptional({ type: Number })
  @Transform(({ value }) => Number(value))
  @IsNumber({}, { message: ' "page" atrribute should be a number' })
  public page = 1;
  
  @ApiPropertyOptional({ type: Number })
  @Transform(({ value }) => Number(value))
  @IsNumber({}, { message: ' "pageSize" attribute should be a number ' })
  public pageSize = 10;
  
  @ApiPropertyOptional({ type: String })
  @IsOptional()
  public orderBy?: string;
  
  @ApiPropertyOptional({ enum: SortOrder })
  @IsOptional()
  public sortOrder?: SortOrder = SortOrder.DESC;
} 