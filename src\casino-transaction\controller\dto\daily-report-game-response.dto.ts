import { ApiProperty } from '@nestjs/swagger';

export class DailyReportGameResponseDto {
  @ApiProperty({ description: 'ID do jogo' })
  gameId: string;

  @ApiProperty({ description: 'Nome do jogo' })
  gameName: string;

  @ApiProperty({ description: 'Provedor do jogo' })
  gameProvider: string;

  @ApiProperty({ description: 'ID do parceiro' })
  partnerId: string;

  @ApiProperty({ description: 'Total de apostas' })
  totalBet: number;

  @ApiProperty({ description: 'Total de ganhos' })
  totalSettle: number;

  @ApiProperty({ description: 'Moeda' })
  currency: string;

  @ApiProperty({ description: 'GGR (Gross Gaming Revenue)' })
  ggr: number;

  @ApiProperty({ description: 'Total de rodadas' })
  totalRound: number;

  @ApiProperty({ description: 'Total de jogadores' })
  totalPlayers: number;

  @ApiProperty({ description: 'Data do relatório' })
  date: Date;
}
