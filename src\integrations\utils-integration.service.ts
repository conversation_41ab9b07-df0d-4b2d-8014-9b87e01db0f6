import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';

export interface Region {
  id: string;
  name: string;
  currencyCode?: string | null;
  currencySymbol?: string | null;
  currencyName?: string | null;
  createdAt?: Date;
  updatedAt?: Date;
}

@Injectable()
export class UtilsIntegrationService {
  private readonly logger = new Logger(UtilsIntegrationService.name);
  private readonly BATCH_SIZE = 100; // Limite do endpoint batch
  private readonly apiUtilsUrl: string;

  constructor(private readonly httpService: HttpService) {
    this.apiUtilsUrl = process.env.API_UTILS || 'http://localhost:3001';
    this.logger.log(
      `UtilsIntegrationService initialized with API_UTILS: ${this.apiUtilsUrl}`,
    );
  }

  /**
   * Busca múltiplas regions por seus IDs via API do api-pam-utils
   * @param regionIds Array de IDs das regions a serem buscadas
   * @returns Promise com array de regions encontradas
   */
  async getRegionsByIds(regionIds: string[]): Promise<Region[]> {
    try {
      this.logger.log(
        `[Start] Getting regions by ids: ${regionIds.length} region IDs`,
      );

      if (!regionIds || regionIds.length === 0) {
        this.logger.warn(`Empty array of region IDs provided`);
        return [];
      }

      // Remove IDs duplicados
      const uniqueRegionIds = [...new Set(regionIds)];
      this.logger.log(
        `[Processing] ${uniqueRegionIds.length} unique region IDs`,
      );

      // Dividir em chunks de 100 (limite do endpoint batch)
      const chunks: string[][] = [];
      for (let i = 0; i < uniqueRegionIds.length; i += this.BATCH_SIZE) {
        chunks.push(uniqueRegionIds.slice(i, i + this.BATCH_SIZE));
      }

      this.logger.log(`[Processing] Split into ${chunks.length} chunks`);

      // Fazer chamadas HTTP em paralelo para cada chunk
      const promises = chunks.map((chunk, index) =>
        this.fetchRegionsChunk(chunk, index),
      );

      const results = await Promise.all(promises);

      // Combinar todos os resultados
      const allRegions = results.flat();

      this.logger.log(
        `[End] Retrieved ${allRegions.length} regions out of ${uniqueRegionIds.length} requested`,
      );

      return allRegions;
    } catch (error) {
      this.logger.error(
        `[Error] Failed to get regions by ids: ${error.message}`,
      );
      // Por enquanto, retornar array vazio em caso de erro
      // Quando implementar circuit breaker, podemos usar fallback
      return [];
    }
  }

  /**
   * Busca um chunk de regions via HTTP
   * @param regionIds Chunk de IDs para buscar
   * @param chunkIndex Índice do chunk (para logging)
   * @returns Promise com array de regions do chunk
   */
  private async fetchRegionsChunk(
    regionIds: string[],
    chunkIndex: number,
  ): Promise<Region[]> {
    try {
      this.logger.log(
        `[Fetching] Chunk ${chunkIndex + 1}: ${regionIds.length} region IDs`,
      );

      const url = `${this.apiUtilsUrl}/v1/pam/utils/regions/batch`;
      const response = await firstValueFrom(
        this.httpService.post<Region[]>(
          url,
          { ids: regionIds },
          {
            timeout: 5000,
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      const regions = response.data || [];
      this.logger.log(
        `[Received] Chunk ${chunkIndex + 1}: ${regions.length} regions`,
      );

      return regions;
    } catch (error) {
      this.logger.error(
        `[Error] Failed to fetch chunk ${chunkIndex + 1}: ${error.message}`,
      );
      // Se falhar, retornar array vazio para este chunk
      // Isso permite que outros chunks ainda sejam processados
      return [];
    }
  }
}
