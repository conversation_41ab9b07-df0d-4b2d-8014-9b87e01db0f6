import { Controller, Post, Body } from '@nestjs/common';
import { FreechipService } from './freechip.service';
import { FreeChipFinishDto } from './dto/finish-freechip.dto';
import { FreechipCancelDto } from './dto/cancel-freechip.dto';
import { IssueFreeChipsDto } from './dto/issue-freechip.dto';
import { ResponseFinishFreeChipDto } from './dto/response-finish-freechip.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@Controller('freechip')
@ApiTags('Freechip')
export class FreechipController {
  constructor(private readonly freechipService: FreechipService) {}

  @Post('finish')
  @ApiOperation({
    summary: 'Finalizar Free Chips',
    description: 'Endpoint para finalizar free chips e processar ganhos',
  })
  @ApiResponse({
    status: 200,
    description: 'Free chips finalizados com sucesso',
    type: ResponseFinishFreeChipDto,
  })
  async finish(
    @Body() finishFreechip: FreeChipFinishDto,
  ): Promise<ResponseFinishFreeChipDto> {
    return this.freechipService.finish(finishFreechip);
  }

  @Post('cancel')
  @ApiOperation({
    summary: 'Cancelar Free Chips',
    description: 'Endpoint para cancelar free chips',
  })
  @ApiResponse({
    status: 200,
    description: 'Free chips cancelados com sucesso',
  })
  async cancel(@Body() cancelFreechip: FreechipCancelDto): Promise<any> {
    return this.freechipService.cancel(cancelFreechip);
  }

  @Post('issue')
  @ApiOperation({
    summary: 'Emitir Free Chips',
    description: 'Endpoint para emitir free chips para um jogador',
  })
  @ApiResponse({
    status: 200,
    description: 'Free chips emitidos com sucesso',
  })
  async issue(@Body() createFreechipDto: IssueFreeChipsDto): Promise<any> {
    return this.freechipService.issue(createFreechipDto);
  }
}
