import { WalletService } from '@/wallet/wallet.service';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { CasinoTransactionEntity } from '../casino-transaction/controller/entities/casino-transactions.entity';
import { CancelBetDto } from './dto/create-cancel-bet.dto';

@Injectable()
export class CancelBetService {
  private readonly logger = new Logger(CancelBetService.name);
  constructor(
    @InjectEntityManager() private readonly manager: EntityManager,
    private readonly walletService: WalletService,
  ) {}

  async cancelBet(cancelBet: CancelBetDto) {
    this.logger.log(`[Inicio] Cancel bet: ${JSON.stringify(cancelBet)}`);
    const { playerId, roundId, transactionId } = cancelBet;
    let balance = 0;

    const casinoTransaction = await this.manager.findOne(
      CasinoTransactionEntity,
      {
        where: [
          { playerId, roundId },
          {
            // transactionBetId: refTransactionId,
            // transactionBetSettleId: refTransactionId,
            // transactionSettleId: refTransactionId,
          },
        ],
      },
    );

    if (!casinoTransaction) {
      this.logger.error(`Bet not found`);
      throw new HttpException('Aposta não encontrada', HttpStatus.NOT_FOUND);
    }

    try {
      this.logger.debug(`Getting url ${casinoTransaction}`);
      const [fetchedBalance] = await Promise.all([
        this.getBalanceInWallet(
          casinoTransaction.partnerId,
          casinoTransaction.playerId,
        ),
        this.updateTransactionCancelId(casinoTransaction, transactionId),
      ]);
      balance = fetchedBalance;
    } catch (error) {
      this.logger.error(`Erro ao buscar balance: ${error}`);
      balance = 0;
    }
    this.logger.log(`[Fim] Cancel bet: ${JSON.stringify(cancelBet)}`);
    return balance;
  }

  async getBalanceInWallet(partnerId: string, playerId: string) {
    try {
      this.logger.log(`[Inicio] Fetching balance for player ${playerId}`);
      const response = await this.walletService.getBalanceByPlayer(
        partnerId,
        playerId,
      );

      if (response && response.balance !== undefined) {
        return response.balance;
      } else {
        this.logger.error(`Balance not found for player ${playerId}`);
        throw new HttpException('Saldo não encontrado', HttpStatus.NOT_FOUND);
      }
    } catch (error) {
      this.logger.error(`Error fetching balance: ${error}`);
      throw new HttpException('Erro ao buscar saldo', HttpStatus.BAD_REQUEST);
    }
  }

  private async updateTransactionCancelId(
    casinoTransaction: CasinoTransactionEntity,
    transactionId: string,
  ): Promise<void> {
    this.logger.log(
      `[Inicio] Updating transaction cancel id: ${JSON.stringify(casinoTransaction)}`,
    );
    await this.manager.save(CasinoTransactionEntity, {
      ...casinoTransaction,
      statusId: 4,
      transactionCancelId: transactionId,
    });
    this.logger.log(
      `[Fim] Updating transaction cancel id: ${JSON.stringify(casinoTransaction)}`,
    );
  }
}
