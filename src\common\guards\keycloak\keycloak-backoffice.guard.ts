import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

/**
 * Guard para autenticação via Keycloak com suporte a X-User-Context
 *
 * Fluxo de autenticação:
 * 1. Valida token Keycloak do header Authorization
 * 2. Valida JWT do header X-User-Context
 * 3. Decodifica X-User-Context e popula req.user
 *
 * Headers esperados:
 * - Authorization: Bearer <keycloak-token>
 * - X-User-Context: <jwt-with-user-data>
 */
@Injectable()
export class KeycloakBackofficeGuard implements CanActivate {
  private readonly logger = new Logger(KeycloakBackofficeGuard.name);

  constructor(private jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest();

    const keycloakToken = this.extractBearerToken(req);
    const userContextJwt = this.extractUserContextToken(req);

    this.logger.debug(
      `Headers recebidos: Authorization=${keycloakToken ? 'presente' : 'ausente'}, X-User-Context=${userContextJwt ? 'presente' : 'ausente'}`,
    );

    if (!keycloakToken) {
      this.logger.warn('Token Keycloak não fornecido no header Authorization');
      throw new HttpException(
        'Token de autenticação não fornecido.',
        HttpStatus.UNAUTHORIZED,
      );
    }

    if (!userContextJwt) {
      this.logger.warn('X-User-Context não fornecido no header');
      throw new HttpException(
        'Contexto de usuário não fornecido.',
        HttpStatus.UNAUTHORIZED,
      );
    }

    this.logger.debug('Iniciando validação do token Keycloak...');
    const isKeycloakValid = await this.validateKeycloakToken(keycloakToken);

    if (!isKeycloakValid) {
      this.logger.error('Token Keycloak inválido ou expirado');
      throw new HttpException(
        'Token de autenticação inválido.',
        HttpStatus.UNAUTHORIZED,
      );
    }

    this.logger.debug('Token Keycloak validado com sucesso');

    try {
      const userPayload = await this.jwtService.verify(userContextJwt, {
        secret: process.env.JWT_SECRET_BACK_OFFICE,
      });

      req.user = userPayload;

      this.logger.log(
        `Usuário autenticado: ${userPayload.email} (ID: ${userPayload.userId})`,
      );

      return true;
    } catch (error) {
      this.logger.error('Erro ao validar X-User-Context:', error.message);

      const errorStatus =
        error.name === 'TokenExpiredError'
          ? HttpStatus.UNAUTHORIZED
          : HttpStatus.INTERNAL_SERVER_ERROR;

      throw new HttpException(
        error.message || 'Erro ao validar contexto de usuário',
        errorStatus,
      );
    }
  }

  /**
   * Valida token Keycloak usando endpoint de introspection
   * @param token Token Keycloak
   * @returns true se válido, false caso contrário
   */
  private async validateKeycloakToken(token: string): Promise<boolean> {
    try {
      const keycloakUrl = process.env.KEYCLOAK_AUTH_SERVER_URL;
      const keycloakRealm = process.env.KEYCLOAK_REALM;
      const clientId = process.env.KEYCLOAK_CLIENT_ID;
      const clientSecret = process.env.KEYCLOAK_CLIENT_SECRET;

      this.logger.debug(
        `Configuração Keycloak: URL=${keycloakUrl}, Realm=${keycloakRealm}, ClientId=${clientId}`,
      );

      if (!keycloakUrl || !keycloakRealm || !clientId || !clientSecret) {
        this.logger.error(
          'Variáveis de ambiente Keycloak não configuradas: KEYCLOAK_AUTH_SERVER_URL, KEYCLOAK_REALM, KEYCLOAK_CLIENT_ID, KEYCLOAK_CLIENT_SECRET',
        );
        throw new HttpException(
          'Configuração de autenticação inválida',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      const introspectUrl = `${keycloakUrl}/realms/${keycloakRealm}/protocol/openid-connect/token/introspect`;

      this.logger.debug(`Validando token Keycloak em: ${introspectUrl}`);

      const response = await fetch(introspectUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: clientId,
          client_secret: clientSecret,
          token: token,
        }),
      });

      this.logger.debug(
        `Keycloak introspection response status: ${response.status} ${response.statusText}`,
      );

      const introspectionResult = await response.json();

      this.logger.debug(
        `Token introspection result: active=${introspectionResult.active}, username=${introspectionResult.username || introspectionResult.preferred_username}`,
      );

      if (!introspectionResult.active) {
        this.logger.warn(
          'Token Keycloak não está ativo (expirado ou revogado)',
        );
        return false;
      }

      this.logger.debug(
        `Token Keycloak válido. Subject: ${introspectionResult.sub}, Client: ${introspectionResult.client_id}`,
      );

      return true;
    } catch (error) {
      this.logger.error(
        `Erro ao validar token Keycloak: ${error.message}`,
        error.stack,
      );

      if (error instanceof TypeError) {
        this.logger.error(
          'Possível erro de rede ou URL inválida. Verifique KEYCLOAK_AUTH_SERVER_URL e KEYCLOAK_REALM',
        );
      }

      return false;
    }
  }

  /**
   * Extrai token Bearer do header Authorization
   * @param req Request object
   * @returns Token ou null
   */
  private extractBearerToken(req: Request): string | null {
    const authorizationHeader = req.headers.authorization;
    if (!authorizationHeader) {
      return null;
    }

    const [bearer, token] = authorizationHeader.split(' ');
    return bearer === 'Bearer' ? token : null;
  }

  /**
   * Extrai JWT do header X-User-Context
   * @param req Request object
   * @returns JWT ou null
   */
  private extractUserContextToken(req: Request): string | null {
    const userContext = req.headers['x-user-context'];

    if (!userContext) {
      return null;
    }

    // Pode vir como string ou array, normalizar
    if (Array.isArray(userContext)) {
      return userContext[0] || null;
    }

    return userContext;
  }
}
