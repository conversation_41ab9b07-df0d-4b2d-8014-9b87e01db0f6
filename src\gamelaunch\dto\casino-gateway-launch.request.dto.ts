class UrlsDto {
  returnUrl: string;
  depositUrl: string;
}

class PlayerDto {
  id: string;
  walletId?: string;
  email: string;
  firstname: string;
  lastname: string;
  nickname?: string;
  dateOfBirth?: string;
  registeredAt?: string;
  country: string;
  tags?: string[];
  currency: string;
}

export class CasinoGatewayLauncherRequestDto {
  sessionPayload: string;
  game: string;
  locale: string;
  ip: string;
  clientType: string;
  urls: UrlsDto;
  jurisdiction?: string;
  player?: PlayerDto;
}

export { UrlsDto, PlayerDto };
