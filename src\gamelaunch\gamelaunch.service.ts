import { Injectable, Logger } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { Aggregator } from "./interfaces/aggregator.interface";
import { HMACService } from "../hmac/service/hmac.service";
import { EntityManager } from "typeorm";
import { CasinoSessionEntity } from "../common/entities/casino-session.entity";
import { v4 as uuidv4 } from "uuid";
import { GameLaunchDTO } from "./dto/game-launch.dto";
import { WalletService } from "@/wallet/wallet.service";
import { Jwt } from "@/common/interfaces/JWT";
import { HttpService } from "@nestjs/axios";
import { firstValueFrom } from "rxjs";
import { InjectEntityManager } from "@nestjs/typeorm";
import { CasinoGames } from "@/casino-games/entities/casino-game.entity";

@Injectable()
export class GameLaunchService {
  private readonly logger = new Logger(GameLaunchService.name);
  balance = 0;

  private urlBaseWallwet = process.env.API_WALLET;
  private urlBaseAggregator = process.env.GAMELAUNCH_BASE_URL;
  private urlCashier = process.env.API_CASHIER;

  constructor(
    @InjectEntityManager() private readonly manager: EntityManager,
    private readonly jwtService: JwtService,
    private readonly hmacService: HMACService,
    private readonly walletService: WalletService,
    private readonly httpService: HttpService
  ) {}

  getBalance({
    player_id,
    partner_id,
  }: {
    player_id: string;
    partner_id: string;
  }) {
    try {
      this.logger.log(`[Inicio] Fetching balance for player ${player_id}`);
      const balance = this.walletService.getBalanceByPlayer(
        partner_id,
        player_id
      );
      this.logger.log(`[Fim] Fetching balance for player ${player_id}`);
      return balance;
    } catch (error) {
      this.logger.error(`Error fetching balance: ${error.message}`);
      throw new Error(`Error making GET request: ${error.message}`);
    }
  }

  async getURLFromAggregator({
    aggregator,
    hmac,
  }: {
    aggregator: Aggregator;
    hmac: string;
  }) {
    const headers = {
      "X-Checksum": hmac,
    };

    try {
      this.logger.log(`[Inicio] Getting url Aggregator ${aggregator}`);
      const response = await firstValueFrom(
        this.httpService.post<{
          errorCode: number;
          errorMsg: string;
          launchUrl: string;
          launchToken: string;
        }>(
          `${this.urlBaseAggregator}/gaming/aggregator/partner/gamelaunch/`,
          { ...aggregator },
          { headers }
        )
      );
      this.logger.log(`[Fim] Getting url Aggregator ${aggregator}`);
      return response;
    } catch (error) {
      this.logger.error(`Error getting url Aggregator: ${error.message}`);
      throw new Error(`Error making GET request: ${error.message}`);
    }
  }

  getContentFromToken(token: string): Jwt {
    try {
      this.logger.log(`[Inicio] Decoding AUTH TOKEN ${token}`);
      const params = this.jwtService.decode(token.replace("Bearer ", ""));
      this.logger.log(`[Fim] Decoding AUTH TOKEN ${token}`);
      return params;
    } catch (error) {
      this.logger.error(`Error decoding AUTH TOKEN: ${error.message}`);
      throw new Error(`Error decoding AUTH TOKEN: ${error.message}`);
    }
  }

  //TODO add session player id in session table
  async getURL(
    body: GameLaunchDTO,
    bearer_token: string
  ): Promise<{
    errorCode: number;
    errorMsg?: string;
    launchToken?: string;
    launchUrl?: string;
  }> {
    this.logger.log(`[Inicio] Getting url ${bearer_token}, ${body}`);
    const params = await this.getContentFromToken(bearer_token);
    this.logger.debug(`Getting url ${params}`);

    const response = await this.getBalance({
      player_id: params.payload.id,
      partner_id: params.payload.partnerId,
    });
    this.logger.debug(`Response getUrl ${response}`);
    await this.walletService.limit(params.payload.id, params.payload.partnerId);
    this.logger.debug(`Limit url ${response}`);
    const gameAggregator = await this.manager.findOneBy(CasinoGames, {
      gameId: body.gameId,
    });
    this.logger.debug(`Getting url ${gameAggregator}`);
    const request_id = uuidv4();

    const aggregator = new Aggregator({
      requestId: request_id,
      partnerCloseUrl:
        body.gameMode === "free-play" ? "https://example.com/casino" : "",
      gameId: body.gameId,
      gameMode: body.gameMode,
      playerLanguage: params.payload.language,
      playerCurrency: params.payload.currency,
      playerCountry: params.payload.country,
      playerBalance: `'${response.balance}'`,
      playerId: params.payload.id,
    });
    const rawData = JSON.stringify(aggregator);
    const hmac = this.hmacService.generateHMAC(rawData);
    const responseAgregator = await this.getURLFromAggregator({
      aggregator,
      hmac,
    });

    if (responseAgregator.data) {
      try {
        await this.manager
          .insert(CasinoSessionEntity, {
            id: uuidv4(),
            requestId: request_id,
            gameId: body.gameId,
            games: gameAggregator,
            playerId: params.payload.id,
            partnerId: params.payload.partnerId,
            launchToken: responseAgregator.data.launchToken,
            launchUrl: responseAgregator.data.launchUrl,
            gameMode: body.gameMode,
            // id_status: '',
            // id_request: '',
            // error_code: '',
            // error_msg: '',
          })
          .catch((err) => console.log(err));
        this.logger.log(`[Fim] Getting url ${responseAgregator}`);
      } catch (error) {
        this.logger.error(`Error getting url: ${error.message}`);
        console.error(error);
      }
      if (responseAgregator.data.errorCode) {
        this.logger.warn(`[Fim] Getting url ${responseAgregator}`);
        return {
          errorCode: responseAgregator.data.errorCode,
          errorMsg: responseAgregator.data.errorMsg,
        };
      }
      return {
        errorCode: 0,
        launchToken: responseAgregator.data.launchToken,
        launchUrl: responseAgregator.data.launchUrl,
      };
    }
  }

  async getFreeUrl(body: GameLaunchDTO): Promise<{
    errorCode: number;
    errorMsg?: string;
    launchToken?: string;
    launchUrl?: string;
  }> {
    this.logger.log(
      `[Inicio] Game launch request free-play: ${JSON.stringify(body)}`
    );
    const request_id = uuidv4();

    const aggregator = new Aggregator({
      requestId: request_id,
      partnerCloseUrl: "https://example.com/casino",
      gameId: body.gameId,
      gameMode: body.gameMode,
      playerCurrency: "BRL",
      playerLanguage: "pt",
      playerCountry: "BR",
      playerBalance: "1000.00",
    });
    this.logger.debug(`Getting url ${aggregator}`);
    const gameAggregator = await this.manager.findOneBy(CasinoGames, {
      gameId: body.gameId,
    });

    if (aggregator.gameMode === "free-play") delete aggregator.playerId;
    const rawData = JSON.stringify(aggregator);
    const hmac = this.hmacService.generateHMAC(rawData);
    const responseAgregator = await this.getURLFromAggregator({
      aggregator,
      hmac,
    });
    if (responseAgregator.data) {
      try {
        //TODO verificar se vai salvar no nosso banco quando for free-play
        await this.manager
          .insert(CasinoSessionEntity, {
            id: uuidv4(),
            requestId: request_id,
            gameId: body.gameId,
            games: gameAggregator,
            playerId: "",
            partnerId: "",
            launchToken: responseAgregator.data.launchToken,
            launchUrl: responseAgregator.data.launchUrl,
            gameMode: body.gameMode,
            // id_status: '',
            // id_request: '',
            // error_code: '',
            // error_msg: '',
          })
          .catch((err) => console.log(err));
      } catch (error) {
        this.logger.error(`Error getting url: ${error.message}`);
        console.error(error);
      }
      if (responseAgregator.data.errorCode) {
        return {
          errorCode: responseAgregator.data.errorCode,
          errorMsg: responseAgregator.data.errorMsg,
        };
      }
      this.logger.log(`[Fim] Getting url ${responseAgregator}`);
      return {
        errorCode: 0,
        launchToken: responseAgregator.data.launchToken,
        launchUrl: responseAgregator.data.launchUrl,
      };
    }
  }
}
