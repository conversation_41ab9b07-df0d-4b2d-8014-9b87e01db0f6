import { CasinoGames } from '@/casino-games/entities/casino-game.entity';
import { Jwt } from '@/common/interfaces/JWT';
import { PartnerService } from '@/partner/partner.service';
import { PlayerService } from '@/player/player.service';
import { WalletService } from '@/wallet/wallet.service';
import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectEntityManager } from '@nestjs/typeorm';
import { firstValueFrom } from 'rxjs';
import { EntityManager } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { CasinoSessionEntity } from '../common/entities/casino-session.entity';
import { CasinoGatewayLauncherRequestDto } from './dto/casino-gateway-launch.request.dto';
import { CreateLauncherDto } from './dto/game-launch.dto';
import { GameModeEnum } from './enum/game-mode.enum';

@Injectable()
export class GameLaunchService {
  private readonly logger = new Logger(GameLaunchService.name);
  balance = 0;

  private urlBaseWallwet = process.env.API_WALLET;
  private urlBaseAggregator = process.env.GAMELAUNCH_BASE_URL;
  private urlCashier = process.env.API_CASHIER;
  private gatewayUrl = `${process.env.GATEWAY_URL}/v1/pam/casino-gateway/casino`;

  constructor(
    @InjectEntityManager() private readonly manager: EntityManager,
    private readonly jwtService: JwtService,
    private readonly walletService: WalletService,
    private readonly httpService: HttpService,
    private readonly playerService: PlayerService,
    private readonly partnerService: PartnerService,
  ) {}

  getBalance({
    player_id,
    partner_id,
  }: {
    player_id: string;
    partner_id: string;
  }) {
    try {
      this.logger.log(`[Inicio] Fetching balance for player ${player_id}`);
      // const balance = this.walletService.getBalanceByPlayer(
      //   partner_id,
      //   player_id
      // );
      const balance = 1000;
      this.logger.log(`[Fim] Fetching balance for player ${player_id}`);
      return balance;
    } catch (error) {
      this.logger.error(`Error fetching balance: ${error.message}`);
      throw new Error(`Error making GET request: ${error.message}`);
    }
  }

  async getURLFromAggregator({
    body,
    isRealMode = true,
  }: {
    body: CasinoGatewayLauncherRequestDto;
    isRealMode?: boolean;
  }) {
    const endpoint = isRealMode ? '/launch/real' : '/launch/demo';
    const url = `${this.gatewayUrl}${endpoint}`;
    const headers = {
      'x-aggregator': 'SOFTSWISS',
    };

    try {
      this.logger.log(
        `[Inicio] Getting url Aggregator ${JSON.stringify(body)}`,
      );
      const response = await firstValueFrom(
        this.httpService.post(url, body, { headers }),
      );
      this.logger.log(
        `[Fim] Getting url Aggregator ${JSON.stringify(response.data)}`,
      );
      return response.data;
    } catch (error) {
      this.logger.error(`Error getting url Aggregator: ${error.message}`);
      throw new HttpException(
        `Erro ao fazer requisição GET: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  getContentFromToken(token: string): Jwt {
    try {
      this.logger.log(`[Inicio] Decoding AUTH TOKEN ${token.length}`);
      const params = this.jwtService.decode(token.replace('Bearer ', ''));
      this.logger.log(`[Fim] Decoding AUTH TOKEN ${token.length}`);
      return params;
    } catch (error) {
      this.logger.error(`Error decoding AUTH TOKEN: ${error.message}`);
      throw new Error(`Error decoding AUTH TOKEN: ${error.message}`);
    }
  }

  async getRealPlayURL(
    body: CreateLauncherDto,
    req,
  ): Promise<{
    launchUrl?: string;
  }> {
    this.logger.log(`[Inicio] getRealPlayURL ${JSON.stringify(body)}`);

    if (!req.headers.authorization) {
      throw new HttpException(
        'Authorization header is required',
        HttpStatus.UNAUTHORIZED,
      );
    }

    if (!body.clientType) {
      throw new HttpException(
        'Client type is required',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!body.ip) {
      throw new HttpException('IP is required', HttpStatus.BAD_REQUEST);
    }

    if (!body.locale) {
      throw new HttpException('Locale is required', HttpStatus.BAD_REQUEST);
    }

    if (!body.game) {
      throw new HttpException('Game is required', HttpStatus.BAD_REQUEST);
    }

    if (!body.urls?.returnUrl) {
      throw new HttpException('Return url is required', HttpStatus.BAD_REQUEST);
    }

    if (!body.urls?.depositUrl) {
      throw new HttpException(
        'Deposit url is required',
        HttpStatus.BAD_REQUEST,
      );
    }

    const params = await this.getContentFromToken(req.headers.authorization);

    if (!params.payload.id) {
      throw new HttpException('Player id is required', HttpStatus.BAD_REQUEST);
    }

    const player = await this.playerService.getPlayerById(
      params.payload.id,
      req,
    );

    if (!player) {
      throw new HttpException('Player not found', HttpStatus.BAD_REQUEST);
    }

    if (!player.accountInfo.isVerified) {
      throw new HttpException(
        'Player account is not verified',
        HttpStatus.FORBIDDEN,
      );
    }

    if (!player.capabilities.canCasinoLogin) {
      throw new HttpException(
        'Player is not allowed to play casino games',
        HttpStatus.FORBIDDEN,
      );
    }

    if (!player.contactInfo.email) {
      throw new HttpException(
        'Player email is required',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!player.personalInformation.name) {
      throw new HttpException(
        'Player name is required',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!player.personalInformation.lastName) {
      throw new HttpException(
        'Player last name is required',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!player.personalInformation.birthDate) {
      throw new HttpException(
        'Player birth date is required',
        HttpStatus.BAD_REQUEST,
      );
    }

    const partner = await this.partnerService.getPartnerById(
      params.payload.partnerId,
      req,
    );

    if (!partner) {
      throw new HttpException('Partner not found', HttpStatus.BAD_REQUEST);
    }

    if (!partner.country?.codeAlpha2) {
      throw new HttpException(
        'Partner country is required',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!partner.currency?.currencyCode) {
      throw new HttpException(
        'Partner currency is required',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Calcula e aplica limites de jogo
    await this.walletService.limit(params.payload.id, params.payload.partnerId);

    const gameAggregator = await this.manager.findOneBy(CasinoGames, {
      id: body.game,
    });
    this.logger.log(`Getting url ${gameAggregator}`);
    const sessionId = uuidv4();

    const responseAgregator = await this.getURLFromAggregator({
      body: {
        sessionPayload: sessionId,
        game: body.game,
        locale: body.locale,
        ip: body.ip,
        clientType: body.clientType,
        urls: body.urls,
        jurisdiction: partner.country.codeAlpha2,
        player: {
          id: player.id,
          walletId: null,
          email: player.contactInfo.email,
          firstname: player.personalInformation.name,
          lastname: player.personalInformation.lastName,
          nickname: player.contactInfo.email,
          dateOfBirth: player.personalInformation.birthDate ? new Date(player.personalInformation.birthDate).toISOString() : null,
          registeredAt: player.createdAt,
          country: partner.country.codeAlpha2,
          currency: partner.currency.currencyCode,
          tags: [],
        },
      },
      isRealMode: true,
    });

    if (responseAgregator.data) {
      const request_id = uuidv4();
      try {
        await this.manager
          .insert(CasinoSessionEntity, {
            id: uuidv4(),
            requestId: request_id,
            aggregatorId: sessionId,
            gameId: body.game,
            games: gameAggregator,
            playerId: player.id,
            partnerId: params.payload.partnerId,
            launchUrl: responseAgregator.data.launchUrl,
            gameMode: GameModeEnum.REAL_PLAY,
          })
          .catch(err => console.log(err));
        this.logger.log(
          `[Fim] Getting url ${responseAgregator.data.launchUrl}`,
        );
        return {
          launchUrl: responseAgregator.data.launchUrl,
        };
      } catch (error) {
        this.logger.error(`Error getting url: ${error.message}`);
        console.error(error);
      }
    }
  }

  async getFreeUrl(
    body: CreateLauncherDto,
    req,
  ): Promise<{
    launchUrl?: string;
  }> {
    this.logger.log(
      `[Inicio] Game launch request free_play: ${JSON.stringify(body)}`,
    );

    this.logger.debug(`Getting url ${JSON.stringify(body)}`);
    const gameAggregator = await this.manager.findOneBy(CasinoGames, {
      id: body.game,
    });

    const partnerId = req.headers['partner-id'] as string;

    const partner = await this.partnerService.getPartnerById(partnerId, req);

    if (!partner) {
      throw new HttpException('Partner not found', HttpStatus.BAD_REQUEST);
    }

    if (!partner.country?.codeAlpha2) {
      throw new HttpException(
        'Partner country is required',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!partner.currency?.currencyCode) {
      throw new HttpException(
        'Partner currency is required',
        HttpStatus.BAD_REQUEST,
      );
    }

    const sessionId = uuidv4();

    const responseAgregator = await this.getURLFromAggregator({
      body: {
        sessionPayload: sessionId,
        game: body.game,
        locale: body.locale,
        ip: body.ip,
        clientType: body.clientType,
        urls: body.urls,
        jurisdiction: partner.country.codeAlpha2,
        player: {
          id: '',
          walletId: '',
          email: '',
          firstname: '',
          lastname: '',
          country: '',
          currency: '',
        },
      },
      isRealMode: false,
    });

    if (responseAgregator.data) {
      const request_id = uuidv4();
      try {
        //TODO verificar se vai salvar no nosso banco quando for free_play
        await this.manager
          .insert(CasinoSessionEntity, {
            id: uuidv4(),
            requestId: request_id,
            aggregatorId: sessionId,
            gameId: body.game,
            games: gameAggregator,
            playerId: '',
            partnerId: partnerId,
            launchUrl: responseAgregator.data.launchUrl,
            gameMode: GameModeEnum.FREE_PLAY,
          })
          .catch(err => console.log(err));
      } catch (error) {
        this.logger.error(`Error getting FreeUrl: ${error.message}`);
        console.error(error);
      }

      this.logger.log(`[Fim] Getting FreeUrl ${responseAgregator}`);
      return {
        launchUrl: responseAgregator.data.launchUrl,
      };
    }
  }
}
