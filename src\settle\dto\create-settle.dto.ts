import { IsBoolean, IsDecimal, IsObject, IsString } from "class-validator"
import { ApiProperty } from '@nestjs/swagger';

export class CreateSettleDto {
    @ApiProperty({
        description: 'ID da sessão',
        example: '123e4567-e89b-12d3-a456-426614174000',
        required: true
    })
    @IsString()
    sessionId: string

    @ApiProperty({
        description: 'ID do jogador',
        example: '123e4567-e89b-12d3-a456-426614174001',
        required: true
    })
    @IsString()
    playerId: string
    
    @ApiProperty({
        description: 'ID da transação',
        example: '123e4567-e89b-12d3-a456-426614174002',
        required: true
    })
    @IsString()
    transactionId: string

    @ApiProperty({
        description: 'Tipo da requisição',
        example: 'SETTLE',
        required: true
    })
    @IsString()
    requestType: string

    @ApiProperty({
        description: 'Valor da aposta',
        example: 100.50,
        required: true
    })
    @IsDecimal()
    amount: number

    @ApiProperty({
        description: 'ID do jogo',
        example: '123e4567-e89b-12d3-a456-426614174003',
        required: true
    })
    @IsString()
    gameId: number

    @ApiProperty({
        description: 'ID da rodada',
        example: '123e4567-e89b-12d3-a456-426614174004',
        required: true
    })
    @IsString()
    roundId: string

    @ApiProperty({
        description: 'Informações de rodadas grátis',
        example: { spins: 10, multiplier: 2 },
        required: false
    })
    @IsObject()
    freeSpinInfo?: object

    @ApiProperty({
        description: 'Indica se a rodada foi finalizada',
        example: true,
        required: true
    })
    @IsBoolean()
    roundEnded: boolean

    @ApiProperty({
        description: 'Informações da aposta',
        example: { betType: 'NORMAL', betAmount: 100.50 },
        required: false
    })
    @IsObject()
    wagerInfo?: object   
}
