import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>Array, IsNumber, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class JackpotFeedDataDto {
  @ApiProperty({ example: '0.01', description: 'Valor do prêmio' })
  @IsString()
  amount: string;

  @ApiProperty({ example: 'EUR', description: 'Moeda utilizada' })
  @IsString()
  currency: string;
}

export class JackpotFeedLevelDto {
  @ApiProperty({ example: 1, description: 'Nível do jackpot' })
  @IsNumber()
  level: number;

  @ApiProperty({ example: 'sample_level', description: 'Nome do nível' })
  @IsString()
  name: string;

  @ApiProperty({
    type: [JackpotFeedDataDto],
    description: 'Lista de dados por nível',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => JackpotFeedDataDto)
  data: JackpotFeedDataDto[];
}

export class JackpotFeedGameDto {
  @ApiProperty({
    example: 'softswiss:PlatinumLightning',
    description: 'ID do jogo',
  })
  @IsString()
  game_id: string;

  @ApiProperty({
    type: [JackpotFeedLevelDto],
    description: 'Níveis do jackpot para o jogo',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => JackpotFeedLevelDto)
  levels: JackpotFeedLevelDto[];
}

export class ResponseJackpotFeedDto {
  @ApiProperty({
    type: [JackpotFeedGameDto],
    description: 'Lista de jogos com jackpot',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => JackpotFeedGameDto)
  games: JackpotFeedGameDto[];
}
