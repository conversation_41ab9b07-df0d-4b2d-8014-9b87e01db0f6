import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { CapabilitiesGuard } from './capabilities.guard';
import { CAPABILITIES_KEY } from '../../decorators/require-capabilities.decorator';

describe('CapabilitiesGuard', () => {
  let guard: CapabilitiesGuard;
  let reflector: Reflector;

  const mockReflector = {
    getAllAndOverride: jest.fn(),
  };

  const createMockExecutionContext = (player: any): ExecutionContext => {
    return {
      switchToHttp: () => ({
        getRequest: () => ({
          player,
        }),
      }),
      getHandler: jest.fn(),
      getClass: jest.fn(),
    } as any;
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CapabilitiesGuard,
        { provide: Reflector, useValue: mockReflector },
      ],
    }).compile();

    guard = module.get<CapabilitiesGuard>(CapabilitiesGuard);
    reflector = module.get<Reflector>(Reflector);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  describe('canActivate', () => {
    it('deve retornar true quando não há capabilities requeridas', () => {
      mockReflector.getAllAndOverride.mockReturnValue(undefined);

      const player = {
        payload: {
          capabilities: { canBet: true },
        },
      };

      const context = createMockExecutionContext(player);
      const result = guard.canActivate(context);

      expect(result).toBe(true);
    });

    it('deve retornar true quando array de capabilities está vazio', () => {
      mockReflector.getAllAndOverride.mockReturnValue([]);

      const player = {
        payload: {
          capabilities: { canBet: true },
        },
      };

      const context = createMockExecutionContext(player);
      const result = guard.canActivate(context);

      expect(result).toBe(true);
    });

    it('deve retornar true quando player tem capability requerida', () => {
      mockReflector.getAllAndOverride.mockReturnValue(['canBet']);

      const player = {
        payload: {
          capabilities: {
            canBet: true,
            canWithdraw: false,
          },
        },
      };

      const context = createMockExecutionContext(player);
      const result = guard.canActivate(context);

      expect(result).toBe(true);
    });

    it('deve retornar true quando player tem pelo menos uma das capabilities requeridas', () => {
      mockReflector.getAllAndOverride.mockReturnValue(['canBet', 'canDeposit']);

      const player = {
        payload: {
          capabilities: {
            canBet: false,
            canDeposit: true,
            canWithdraw: false,
          },
        },
      };

      const context = createMockExecutionContext(player);
      const result = guard.canActivate(context);

      expect(result).toBe(true);
    });

    it('deve retornar false quando player não tem nenhuma capability requerida', () => {
      mockReflector.getAllAndOverride.mockReturnValue(['canBet']);

      const player = {
        payload: {
          capabilities: {
            canBet: false,
            canWithdraw: true,
          },
        },
      };

      const context = createMockExecutionContext(player);
      const result = guard.canActivate(context);

      expect(result).toBe(false);
    });

    it('deve lançar UnauthorizedException quando player não está presente', () => {
      mockReflector.getAllAndOverride.mockReturnValue(['canBet']);

      const context = createMockExecutionContext(null);

      expect(() => guard.canActivate(context)).toThrow(UnauthorizedException);
      expect(() => guard.canActivate(context)).toThrow(
        'Usuário não possui capabilities definidas',
      );
    });

    it('deve lançar UnauthorizedException quando player.payload não está presente', () => {
      mockReflector.getAllAndOverride.mockReturnValue(['canBet']);

      const player = {};

      const context = createMockExecutionContext(player);

      expect(() => guard.canActivate(context)).toThrow(UnauthorizedException);
    });

    it('deve lançar UnauthorizedException quando capabilities não estão definidas', () => {
      mockReflector.getAllAndOverride.mockReturnValue(['canBet']);

      const player = {
        payload: {},
      };

      const context = createMockExecutionContext(player);

      expect(() => guard.canActivate(context)).toThrow(UnauthorizedException);
    });

    it('deve retornar false quando capability existe mas é false', () => {
      mockReflector.getAllAndOverride.mockReturnValue(['canBet']);

      const player = {
        payload: {
          capabilities: {
            canBet: false,
          },
        },
      };

      const context = createMockExecutionContext(player);
      const result = guard.canActivate(context);

      expect(result).toBe(false);
    });

    it('deve retornar false quando capability não existe no objeto', () => {
      mockReflector.getAllAndOverride.mockReturnValue(['canBet']);

      const player = {
        payload: {
          capabilities: {
            canWithdraw: true,
          },
        },
      };

      const context = createMockExecutionContext(player);
      const result = guard.canActivate(context);

      expect(result).toBe(false);
    });

    it('deve validar múltiplas capabilities corretamente', () => {
      mockReflector.getAllAndOverride.mockReturnValue([
        'canBet',
        'canDeposit',
        'canWithdraw',
      ]);

      const player = {
        payload: {
          capabilities: {
            canBet: false,
            canDeposit: false,
            canWithdraw: true,
          },
        },
      };

      const context = createMockExecutionContext(player);
      const result = guard.canActivate(context);

      expect(result).toBe(true);
    });

    it('deve chamar reflector com parâmetros corretos', () => {
      mockReflector.getAllAndOverride.mockReturnValue(['canBet']);

      const player = {
        payload: {
          capabilities: { canBet: true },
        },
      };

      const context = createMockExecutionContext(player);
      guard.canActivate(context);

      expect(reflector.getAllAndOverride).toHaveBeenCalledWith(
        CAPABILITIES_KEY,
        [context.getHandler(), context.getClass()],
      );
    });
  });
});
