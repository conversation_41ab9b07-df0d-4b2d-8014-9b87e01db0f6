import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsString, IsUUID } from 'class-validator';

export class ResponsePromoRollbackDto {
  @ApiProperty({ example: '0.01' })
  @IsString()
  balance: string;

  @ApiProperty({ example: 'e9888ed7-9e98-4463-be23-35b74295eb79' })
  @IsUUID()
  id: string;

  @ApiProperty({ example: 'ab74daba-c9d6-4fb6-a815-0fd6c42a9cdd' })
  @IsUUID()
  id_casino: string;

  @ApiProperty({ example: '2022-06-15T15:30:00.000054Z' })
  @IsDateString()
  processed_at: string;
}
