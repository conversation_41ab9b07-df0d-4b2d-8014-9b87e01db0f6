import { Injectable } from '@nestjs/common';
import { parseISO } from 'date-fns';
import { toZonedTime, fromZonedTime } from 'date-fns-tz';

@Injectable()
export class DateUtilsService {
  private readonly timezone: string;

  constructor() {
    this.timezone = process.env.TZ || 'America/Sao_Paulo';
  }

  getDateRange(from: string, to: string) {
    try {
      const fromDate = this.getStartOfDayInUTC(from);
      const toDate = this.getEndOfDayInUTC(to);

      return { fromDate, toDate };
    } catch {
      return { fromDate: null, toDate: null };
    }
  }

  getStartOfDayInUTC(date: string): Date | null {
    if (!date || typeof date !== 'string') {
      return null;
    }

    try {
      const parsedDate = parseISO(date);
      if (Number.isNaN(parsedDate.getTime())) {
        return null;
      }

      const year = parsedDate.getFullYear();
      const month = parsedDate.getMonth();
      const day = parsedDate.getDate();

      const localDate = new Date(year, month, day, 0, 0, 0, 0);
      const utcDate = fromZonedTime(localDate, this.timezone);

      return utcDate;
    } catch {
      return null;
    }
  }

  getEndOfDayInUTC(date: string): Date | null {
    if (!date || typeof date !== 'string') {
      return null;
    }

    try {
      const parsedDate = parseISO(date);

      if (Number.isNaN(parsedDate.getTime())) {
        return null;
      }

      const year = parsedDate.getFullYear();
      const month = parsedDate.getMonth();
      const day = parsedDate.getDate();

      const localDate = new Date(year, month, day, 23, 59, 59, 999);
      const utcDate = fromZonedTime(localDate, this.timezone);

      return utcDate;
    } catch {
      return null;
    }
  }

  /**
   * Converte uma data para UTC antes de salvar no banco de dados
   * @param date Data a ser convertida
   * @returns Data em UTC
   */
  toUTC(date: Date): Date {
    return new Date(date.toISOString());
  }

  /**
   * Converte uma data UTC para o timezone configurado
   * @param date Data UTC
   * @returns Data no timezone configurado
   */
  toLocal(date: Date): Date {
    return toZonedTime(date, this.timezone);
  }

  /**
   * Retorna a data atual no timezone configurado
   * @returns Data atual no timezone configurado
   */
  now(): Date {
    return toZonedTime(new Date(), this.timezone);
  }
}
