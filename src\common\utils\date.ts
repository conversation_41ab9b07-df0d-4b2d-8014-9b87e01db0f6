import { Injectable } from "@nestjs/common";
import { endOfDay, parseISO, startOfDay } from "date-fns";

@Injectable()
export class DateUtilsService {
  getDateRange(from: string, to: string) {
    if (from && to) {
      const fromDate = startOfDay(parseISO(from));
      const toDate = endOfDay(parseISO(to));

      return { fromDate, toDate };
    }

    return { fromDate: null, toDate: null };
  }
}
