import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsIP,
  IsNotEmpty,
  IsString,
  Matches,
  ValidateNested,
} from 'class-validator';
import { GameModeEnum } from '../enum/game-mode.enum';

class UrlsDto {
  @ApiProperty({
    example: 'https://meusite.com/retorno',
    description: 'URL de retorno após o término da sessão',
  })
  @IsString()
  @IsNotEmpty()
  returnUrl: string;

  @ApiProperty({
    example: 'https://meusite.com/deposit',
    description: 'URL que o jogador é redirecionado para efetuar o depósito',
  })
  @IsString()
  @IsNotEmpty()
  depositUrl: string;
}

export class CreateLauncherDto {
  @ApiProperty({
    example: 'real_play',
    description: 'Tipo de jogo (real_play ou free_play)',
  })
  @IsNotEmpty()
  @IsEnum(GameModeEnum)
  gameMode: GameModeEnum;

  @ApiProperty({
    example: 'e3e54764-96b9-484e-946f-fb576e43850f',
    description: 'identificador do jogo',
  })
  @IsString()
  @IsNotEmpty()
  game: string;

  @ApiProperty({
    example: 'en',
    description:
      'Código ISO 639-1 do idioma do jogador (duas letras minúsculas). Caso não suportado, será usado "en".',
    pattern: '^[a-z]{2}$',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^[a-z]{2}$/)
  locale: string;

  @ApiProperty({
    example: '***********',
    description: 'Endereço IP do jogador',
  })
  @IsIP()
  @IsNotEmpty()
  ip: string;

  @ApiProperty({
    example: 'desktop',
    description: 'Tipo de cliente (ex: desktop, mobile, tablet)',
  })
  @IsString()
  @IsNotEmpty()
  clientType: string;

  @ApiProperty({
    type: UrlsDto,
    description: 'Objeto contendo URLs relacionadas',
  })
  @ValidateNested()
  @Type(() => UrlsDto)
  urls: UrlsDto;
}

export { UrlsDto };
