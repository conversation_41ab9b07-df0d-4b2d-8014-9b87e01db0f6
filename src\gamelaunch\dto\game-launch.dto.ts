import { GameModeEnum } from "../enum/game-mode.enum"
import { GenderEnum } from "../enum/gender.enum"
import { IsEnum, IsString } from "class-validator"
import { ApiProperty } from "@nestjs/swagger"

export class GameLaunchDTO {
    @ApiProperty({
        description: 'ID do jogo',
        example: '12345',
        required: true
    })
    @IsString({ message: 'Missing required parameter: gameId' })
    gameId?: string

    @ApiProperty({
        description: 'Modo do jogo (real-play ou free-play)',
        enum: GameModeEnum,
        example: 'real-play',
        required: true
    })
    @IsEnum(GenderEnum, { message: 'Missing required parameter: gameMode' })
    gameMode: GameModeEnum
}

