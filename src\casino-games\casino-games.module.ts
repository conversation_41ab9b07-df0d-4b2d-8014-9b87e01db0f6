import { DatabaseModule } from '@/common/database/database.module';
import { JwtStrategy } from '@/common/guards/jwt/jwt.strategy';
import { S3Module } from '@/common/services/aws/s3.module';
import { PamService } from '@/pam/pam.service';
import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { BackofficeCasinoGamesController } from './controller/backoffice-casino-games.controller';
import { CasinoGamesController } from './controller/casino-games.controller';
import { BackofficeCasinoGamesService } from './services/backoffice-casino-games.service';
import { CasinoGamesService } from './services/casino-games.service';

@Module({
  imports: [
    DatabaseModule,
    S3Module,
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: '1d' },
    }),
  ],
  controllers: [BackofficeCasinoGamesController, CasinoGamesController],
  providers: [
    CasinoGamesService,
    BackofficeCasinoGamesService,
    JwtStrategy,
    PamService,
  ],
})
export class CasinoGamesModule {}
