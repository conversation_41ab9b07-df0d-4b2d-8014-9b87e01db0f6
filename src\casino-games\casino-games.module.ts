import { Modu<PERSON> } from '@nestjs/common';
import { CasinoGamesService } from './services/casino-games.service';
import { CasinoGamesController } from './controller/casino-games.controller';
import { JwtModule } from '@nestjs/jwt';
import { JwtStrategy } from '@/common/guards/jwt/jwt.strategy';
import { PamService } from '@/pam/pam.service';
import { DatabaseModule } from '@/common/database/database.module';
import { BackofficeCasinoGamesController } from './controller/backoffice-casino-games.controller';
import { BackofficeCasinoGamesService } from './services/backoffice-casino-games.service';

@Module({
  imports: [
    DatabaseModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: '1d' },
    }),
  ],
  controllers: [CasinoGamesController, BackofficeCasinoGamesController],
  providers: [CasinoGamesService,BackofficeCasinoGamesService, JwtStrategy, PamService],
})
export class CasinoGamesModule {}
