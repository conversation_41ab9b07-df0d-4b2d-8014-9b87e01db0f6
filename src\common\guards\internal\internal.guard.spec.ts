import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { InternalApiKeyGuard } from './internal.guard';

describe('InternalApiKeyGuard', () => {
  let guard: InternalApiKeyGuard;

  const createMockExecutionContext = (headers: any): ExecutionContext => {
    return {
      switchToHttp: () => ({
        getRequest: () => ({
          headers,
        }),
      }),
    } as any;
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [InternalApiKeyGuard],
    }).compile();

    guard = module.get<InternalApiKeyGuard>(InternalApiKeyGuard);
    jest.clearAllMocks();

    // Mock environment variable
    process.env.CASINO_INTERNAL_API_KEY = 'test-internal-api-key';
  });

  afterEach(() => {
    delete process.env.CASINO_INTERNAL_API_KEY;
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  describe('canActivate', () => {
    it('deve retornar true quando API key é válida', () => {
      const headers = {
        'x-api-key': 'test-internal-api-key',
      };

      const context = createMockExecutionContext(headers);
      const result = guard.canActivate(context);

      expect(result).toBe(true);
    });

    it('deve lançar UnauthorizedException quando API key está ausente', () => {
      const headers = {};

      const context = createMockExecutionContext(headers);

      expect(() => guard.canActivate(context)).toThrow(UnauthorizedException);
      expect(() => guard.canActivate(context)).toThrow(
        'Invalid or missing Internal API key',
      );
    });

    it('deve lançar UnauthorizedException quando API key é inválida', () => {
      const headers = {
        'x-api-key': 'invalid-api-key',
      };

      const context = createMockExecutionContext(headers);

      expect(() => guard.canActivate(context)).toThrow(UnauthorizedException);
      expect(() => guard.canActivate(context)).toThrow(
        'Invalid or missing Internal API key',
      );
    });

    it('deve lançar UnauthorizedException quando API key é vazia', () => {
      const headers = {
        'x-api-key': '',
      };

      const context = createMockExecutionContext(headers);

      expect(() => guard.canActivate(context)).toThrow(UnauthorizedException);
    });

    it('deve validar API key case-sensitive', () => {
      const headers = {
        'x-api-key': 'TEST-INTERNAL-API-KEY',
      };

      const context = createMockExecutionContext(headers);

      expect(() => guard.canActivate(context)).toThrow(UnauthorizedException);
    });

    it('deve aceitar API key com caracteres especiais', () => {
      process.env.CASINO_INTERNAL_API_KEY = 'key-with-special-chars-@#$%';

      const headers = {
        'x-api-key': 'key-with-special-chars-@#$%',
      };

      const context = createMockExecutionContext(headers);
      const result = guard.canActivate(context);

      expect(result).toBe(true);
    });

    it('deve verificar header x-api-key exato (case-sensitive no nome)', () => {
      const headers = {
        'X-API-KEY': 'test-internal-api-key',
      };

      const context = createMockExecutionContext(headers);

      // Headers são case-insensitive em HTTP, mas vamos testar o comportamento
      expect(() => guard.canActivate(context)).toThrow(UnauthorizedException);
    });

    it('deve rejeitar quando API key tem espaços extras', () => {
      const headers = {
        'x-api-key': ' test-internal-api-key ',
      };

      const context = createMockExecutionContext(headers);

      expect(() => guard.canActivate(context)).toThrow(UnauthorizedException);
    });

    it('deve funcionar com múltiplos headers presentes', () => {
      const headers = {
        'x-api-key': 'test-internal-api-key',
        authorization: 'Bearer some-token',
        'content-type': 'application/json',
      };

      const context = createMockExecutionContext(headers);
      const result = guard.canActivate(context);

      expect(result).toBe(true);
    });
  });
});
