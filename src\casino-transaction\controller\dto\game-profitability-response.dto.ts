import { ApiProperty } from "@nestjs/swagger";

export class GameProfitabilityResponseDto {
  @ApiProperty({
    description: "Game ID",
    example: "12345"
  })
  gameId: string;

  @ApiProperty({
    description: "Game name",
    example: "Book of Dead"
  })
  gameName: string;

  @ApiProperty({
    description: "Game provider",
    example: "PRAGMATIC"
  })
  gameProvider: string;

  @ApiProperty({
    description: "Total bet amount",
    example: 1000.50
  })
  totalBet: number;

  @ApiProperty({
    description: "Total settle amount",
    example: 900.25
  })
  totalSettle: number;

  @ApiProperty({
    description: "Gross Gaming Revenue (GGR)",
    example: 100.25
  })
  ggr: number;

  @ApiProperty({
    description: "Profitability percentage",
    example: 10.02
  })
  profitability: number;
} 