import { ApiProperty } from '@nestjs/swagger';

export class BetReportResponseDto {
  @ApiProperty({ description: 'ID da transação' })
  transactionId: string;

  @ApiProperty({ description: 'ID da rodada (round_id)' })
  roundId: string;

  @ApiProperty({ description: 'ID do jogador' })
  playerId: string;

  @ApiProperty({ description: 'ID do jogo' })
  gameId: string;

  @ApiProperty({ description: 'Nome do jogo' })
  gameName: string;

  @ApiProperty({ description: 'Fornecedor do jogo' })
  gameProvider: string;

  @ApiProperty({ description: 'Total apostado' })
  totalBet: number;

  @ApiProperty({ description: 'Total pago' })
  totalWin: number;

  @ApiProperty({
    description: 'Status da aposta',
    enum: ['Finalizada', 'Em andamento'],
  })
  betStatus: string;

  @ApiProperty({ description: 'Moeda' })
  currency: string;

  @ApiProperty({ description: 'Data de criação' })
  createdAt: Date;
}
