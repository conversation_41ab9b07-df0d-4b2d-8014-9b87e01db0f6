import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export const Auth = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    try {
      const request = ctx.switchToHttp().getRequest();
      const bearer = request.headers.authorization.split(' ')[1];
      const decoded = JSON.parse(
        Buffer.from(bearer.split('.')[1], 'base64').toString(),
      );
      return decoded;
    } catch (error) {
      return ctx.switchToHttp().getResponse().status(500).json({
        message: 'Error decoding AUTH TOKEN',
        error: error.message,
      });
    }
  },
);
