import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsOptional,
  IsString,
  IsUUID,
  Length,
  ValidateNested,
} from 'class-validator';

export class RollbackTransactionDto {
  @ApiProperty({
    description: 'Unique rollback transaction ID from Game Aggregator',
    example: 'a4_r_30104280_int',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Original transaction ID to rollback',
    example: 'a4_30104280_int',
  })
  @IsString()
  originalId: string;
}

export class RollbackRequestDto {
  @ApiProperty({
    description: 'Player identifier',
    example: 'eece8ba4-934a-48e1-bfd7-c111918b543e',
  })
  @IsUUID()
  playerId: string;

  @ApiProperty({
    description: 'Currency code (ISO 4217)',
    example: 'BRL',
  })
  @IsString()
  @Length(3, 4)
  currency: string;

  @ApiProperty({
    description: 'Game identifier',
    example: 'bgmng:CarnivalBonanza',
  })
  @IsString()
  gameId: string;

  @ApiProperty({
    description: 'Round identifier',
    example: 'a4-30yn-22826151',
  })
  @IsString()
  roundId: string;

  @ApiProperty({
    description: 'Whether the round is finished',
    example: true,
  })
  @IsBoolean()
  roundEnded: boolean;

  @ApiProperty({
    description: 'List of transactions to rollback',
    type: [RollbackTransactionDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RollbackTransactionDto)
  transactions: RollbackTransactionDto[];

  @ApiProperty({
    description: 'Session identifier from casino',
    example: '7ca63554-e320-47e9-8287-baaf7622d597',
    required: false,
  })
  @IsOptional()
  @IsString()
  sessionId: string;

  @ApiProperty({ example: 'string' })
  @IsOptional()
  @IsString()
  walletId?: string;

  @ApiProperty({
    description: 'Código do agregador',
    example: 'SOFTSWISS',
    required: true,
  })
  @IsString()
  aggregatorCode: string;
}
