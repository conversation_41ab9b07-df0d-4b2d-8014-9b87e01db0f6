import { Test, TestingModule } from '@nestjs/testing';
import { BonusService } from './bonus.service';
import { HttpService } from '@nestjs/axios';
import { of, throwError } from 'rxjs';
import { HttpException } from '@nestjs/common';

describe('BonusService', () => {
  let service: BonusService;
  let httpService: { post: jest.Mock };

  const GATEWAY_URL = 'http://gateway.test';

  beforeAll(() => {
    process.env.GATEWAY_URL = GATEWAY_URL;
  });

  beforeEach(async () => {
    httpService = { post: jest.fn() };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BonusService,
        { provide: HttpService, useValue: httpService },
      ],
    }).compile();

    service = module.get<BonusService>(BonusService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('finish should return static balance', async () => {
    const dto: any = { userId: 'u1', amount: '10' };
    await expect(service.finish(dto)).resolves.toEqual({ balance: '0.01' });
  });

  it('cancel should POST to gateway and return data', async () => {
    const dto: any = { promotionId: 'p1' };
    const data = { ok: true };
    httpService.post.mockReturnValue(of({ data }));

    await expect(service.cancel(dto)).resolves.toEqual(data);
    expect(httpService.post).toHaveBeenCalledWith(
      `${GATEWAY_URL}/casino/bonus/cancel`,
      dto,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-aggregator': 'softswiss',
        },
      },
    );
  });

  it('issue should POST to gateway and return data', async () => {
    const dto: any = { userId: 'u1', type: 'WELCOME' };
    const data = { bonusId: 'b1' };
    httpService.post.mockReturnValue(of({ data }));

    await expect(service.issue(dto)).resolves.toEqual(data);
    expect(httpService.post).toHaveBeenCalledWith(
      `${GATEWAY_URL}/casino/bonus/issue`,
      dto,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-aggregator': 'softswiss',
        },
      },
    );
  });

  it('types should POST to gateway and return data', async () => {
    const dto: any = { brandId: 'brand-1' };
    const data = { types: ['WELCOME', 'RELOAD'] };
    httpService.post.mockReturnValue(of({ data }));

    await expect(service.types(dto)).resolves.toEqual(data);
    expect(httpService.post).toHaveBeenCalledWith(
      `${GATEWAY_URL}/casino/bonus/types`,
      dto,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-aggregator': 'softswiss',
        },
      },
    );
  });

  it('cancel should wrap http errors in HttpException', async () => {
    const dto: any = { promotionId: 'p1' };
    const error: any = { message: 'upstream error', status: 502 };
    httpService.post.mockReturnValue(throwError(() => error));

    await expect(service.cancel(dto)).rejects.toBeInstanceOf(HttpException);
    await expect(service.cancel(dto)).rejects.toMatchObject({
      message: error.message,
    });
  });
});
