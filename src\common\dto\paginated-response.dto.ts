import { ApiProperty } from '@nestjs/swagger';

export class PaginationResponseDto<T> {
  @ApiProperty({
    description: 'Lista de itens',
    isArray: true,
    type: () => Object,
  })
  data: T[];

  @ApiProperty({
    example: 100,
    description: 'Total de itens encontrados',
    type: Number,
    required: false,
    minimum: 0,
  })
  totalItems: number;

  @ApiProperty({
    description: 'Página atual',
    type: Number,
    required: false,
    minimum: 1,
    example: 1,
  })
  currentPage: number;

  @ApiProperty({
    description: 'Tamanho da página',
    type: Number,
    required: false,
    minimum: 1,
    example: 10,
  })
  pageSize: number;

  @ApiProperty({
    description: 'Total de páginas',
    type: Number,
    required: false,
    minimum: 1,
    example: 10,
  })
  totalPages: number;

  constructor(init?: Partial<PaginationResponseDto<T>>) {
    if (init) {
      Object.assign(this, init);
    }
  }
}

export function PaginatedResponseDtoExample(dataSchema: any) {
  return {
    data: dataSchema,
    totalItems: 100,
    totalPages: 10,
    currentPage: 1,
    pageSize: 10,
  };
}
