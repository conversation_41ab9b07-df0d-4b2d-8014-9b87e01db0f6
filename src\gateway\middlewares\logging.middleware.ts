import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

/**
 * Middleware para logging detalhado das requisições do gateway
 */
@Injectable()
export class GatewayLoggingMiddleware implements NestMiddleware {
  private readonly logger = new Logger(GatewayLoggingMiddleware.name);

  use(req: Request, res: Response, next: NextFunction): void {
    const startTime = Date.now();
    const { method, originalUrl, ip } = req;
    const userAgent = req.get('User-Agent') || '';
    const provider = req.headers['x-provider'] || 'auto-detect';

    // Log da requisição
    this.logger.log(`[REQUEST] ${method} ${originalUrl}`, {
      ip,
      userAgent,
      provider,
      headers: this.sanitizeHeaders(req.headers),
      body: this.sanitizeBody(req.body),
      timestamp: new Date().toISOString(),
    });

    // Intercepta a resposta
    const originalSend = res.send;
    res.send = function (body: any) {
      const duration = Date.now() - startTime;
      
      // Log da resposta
      const logger = new Logger(GatewayLoggingMiddleware.name);
      logger.log(`[RESPONSE] ${method} ${originalUrl} - ${res.statusCode}`, {
        statusCode: res.statusCode,
        duration: `${duration}ms`,
        provider,
        responseSize: Buffer.byteLength(body, 'utf8'),
        timestamp: new Date().toISOString(),
      });

      return originalSend.call(this, body);
    };

    next();
  }

  /**
   * Remove dados sensíveis dos headers
   */
  private sanitizeHeaders(headers: any): any {
    const sanitized = { ...headers };
    const sensitiveHeaders = [
      'authorization',
      'x-api-key',
      'x-secret',
      'cookie',
      'x-auth-token',
    ];

    for (const header of sensitiveHeaders) {
      if (sanitized[header]) {
        sanitized[header] = '***';
      }
    }

    return sanitized;
  }

  /**
   * Remove dados sensíveis do body
   */
  private sanitizeBody(body: any): any {
    if (!body || typeof body !== 'object') {
      return body;
    }

    const sanitized = { ...body };
    const sensitiveFields = [
      'password',
      'token',
      'secret',
      'key',
      'auth',
      'credential',
    ];

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '***';
      }
    }

    return sanitized;
  }
}
