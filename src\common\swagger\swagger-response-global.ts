import { OpenAPIObject } from '@nestjs/swagger';
import { MessageCodes } from '../constants/message-codes';

export function addGlobalResponses(document: OpenAPIObject) {
  Object.keys(document.paths).forEach(path => {
    Object.keys(document.paths[path]).forEach(method => {
      if (
        document.paths[path][method].responses['200'] &&
        document.paths[path][method].responses['200'].description ===
          'Possíveis respostas'
      ) {
        return;
      }
      document.paths[path][method].responses = {
        '400': {
          description: 'Bad Request.',
          content: {
            'application/json': {
              examples: {
                GENERIC_ERROR_LIST: {
                  value: {
                    errorCode: MessageCodes.GENERIC_ERROR.code,
                    additionalInfo: {
                      message:
                        'Os dados não foram informados e são obrigatórios:',
                      details: [
                        'token should not be empty',
                        'token must be a string',
                      ],
                    },
                  },
                },
                GENERIC_ERROR: {
                  value: {
                    errorCode: MessageCodes.GENERIC_ERROR.code,
                    additionalInfo: 'Requisição inválida',
                  },
                },
              },
            },
          },
        },
        '401': {
          description: 'Unauthorized.',
          content: {
            'application/json': {
              examples: {
                INVALID_TOKEN: {
                  value: {
                    errorCode: MessageCodes.INVALID_TOKEN.code,
                    additionalInfo: 'Token obrigatório.',
                  },
                },
                EXPIRED_TOKEN: {
                  value: {
                    errorCode: MessageCodes.EXPIRED_TOKEN.code,
                    additionalInfo: 'Token expirado.',
                  },
                },
              },
            },
          },
        },
        '403': {
          description: 'Forbidden.',
          content: {
            'application/json': {
              examples: {
                PLAYER_BLOCKED: {
                  value: {
                    errorCode: MessageCodes.PLAYER_BLOCKED.code,
                    additionalInfo: 'Acesso negado.',
                  },
                },
              },
            },
          },
        },
        '404': {
          description: 'Not Found.',
          content: {
            'application/json': {
              examples: {
                PLAYER_NOT_FOUND: {
                  value: {
                    errorCode: MessageCodes.PLAYER_NOT_FOUND.code,
                    additionalInfo: 'Recurso não encontrado',
                  },
                },
              },
            },
          },
        },
        '500': {
          description: 'Internal Server Error.',
          content: {
            'application/json': {
              examples: {
                GENERIC_ERROR: {
                  value: {
                    errorCode: MessageCodes.GENERIC_ERROR.code,
                    additionalInfo: 'Erro interno do servidor',
                  },
                },
              },
            },
          },
        },
        ...document.paths[path][method].responses,
      };
    });
  });
}
