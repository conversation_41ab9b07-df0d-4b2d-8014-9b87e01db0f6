import { IsDecimal, IsObject, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateBetDto {
  @ApiProperty({
    description: 'ID da sessão do jogador',
    example: 'session-123',
    required: true,
  })
  @IsString()
  sessionId: string;

  @ApiProperty({
    description: 'ID da transação',
    example: 'tx-123',
    required: true,
  })
  @IsString()
  transactionId: string;

  @ApiProperty({
    description: 'ID do jogador',
    example: 'player-123',
    required: true,
  })
  @IsString()
  playerId: string;

  @ApiProperty({
    description: 'Tipo da requisição',
    example: 'RealMoney',
    required: true,
  })
  @IsString()
  requestType: string;

  @ApiProperty({
    description: 'Valor da aposta',
    example: 100.5,
    required: true,
  })
  @IsDecimal()
  amount: number;

  @ApiProperty({
    description: 'ID do jogo',
    example: 'game-123',
    required: true,
  })
  @IsString()
  gameId: string;

  @ApiProperty({
    description: 'ID da rodada',
    example: 'round-123',
    required: true,
  })
  @IsString()
  roundId: string;

  @ApiProperty({
    description: 'Informações de free spins',
    example: { spins: 10, value: 1.0 },
    required: false,
  })
  @IsObject()
  freeSpinInfo?: object;

  @ApiProperty({
    description: 'Informações da aposta',
    example: { type: 'normal', multiplier: 1 },
    required: false,
  })
  @IsObject()
  wagerInfo?: object;
}
