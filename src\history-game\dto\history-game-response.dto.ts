import { ApiProperty } from '@nestjs/swagger';

export class HistoryGameResponseDto {
  @ApiProperty({ description: 'ID do histórico' })
  id: string;

  @ApiProperty({ description: 'ID do jogador' })
  playerId: string;

  @ApiProperty({ description: 'ID do jogo' })
  gameId: string;

  @ApiProperty({ description: 'ID do parceiro' })
  partnerId: string;

  @ApiProperty({ description: 'ID da rodada' })
  roundId: string;

  @ApiProperty({ description: 'Valor da aposta' })
  amount: number;

  @ApiProperty({ description: 'Valor do ganho' })
  winAmount: number;

  @ApiProperty({ description: 'Tipo da transação' })
  type: string;

  @ApiProperty({ description: 'Status da rodada' })
  roundEnded: boolean;

  @ApiProperty({ description: 'Data de criação' })
  createdAt: Date;

  @ApiProperty({ description: 'Data de atualização' })
  updatedAt: Date;
}

export const HistoryGameResponseDtoExample = {
  id: '123',
  playerId: '123',
  gameId: '123',
  partnerId: '123',
  roundId: '123',
  amount: 100,
  winAmount: 100,
  type: 'win',
  roundEnded: true,
  createdAt: new Date(),
  updatedAt: new Date(),
};
