import { SUCCESS, ALREADY_PROCESSED, GENERIC_ERROR, PLAYER_NOT_FOUND, SESSION_NOT_FOUND, GAME_NOT_FOUND, ROUND_NOT_FOUND } from "@/common/constants/message-codes";
import { SwaggerApiCodeResponses } from "@/common/decorators/swagger-api-code-responses";
import { ApiPartnerIdHeader, ApiClientIpHeader, ApiPaginationQuery } from "@/common/decorators/swagger.decorator";
import { PaginationResponseDto, PaginatedResponseDtoExample } from "@/common/dto/paginated-response.dto";
import { GenericFilter } from "@/common/filters/generic-filter.dto";
import { Controller, Get, HttpCode, Query, Req } from "@nestjs/common";
import { ApiTags, ApiForbiddenResponse, ApiInternalServerErrorResponse, ApiBearerAuth, ApiOperation } from "@nestjs/swagger";
import { CasinoGamesResponseDto, CasinoGamesResponseDtoExample } from "../dto/casino-games-response.dto";
import { CasinoGames } from "../entities/casino-game.entity";
import { CasinoGamesService } from "../services/casino-games.service";


@ApiTags('CasinoGames')
@ApiPartnerIdHeader()
@ApiClientIpHeader()
@ApiForbiddenResponse()
@ApiInternalServerErrorResponse()
@Controller('casino-games')
@ApiBearerAuth('access-token')
export class CasinoGamesController {
  constructor(private readonly casinoGamesService: CasinoGamesService) {}
  @Get()
  @ApiOperation({
    summary: 'Listar jogos',
    description: 'Endpoint para listar todos os jogos de casino com paginação',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    PaginationResponseDto<CasinoGamesResponseDto>,
    PaginatedResponseDtoExample(CasinoGamesResponseDtoExample)
  )
  @ApiPaginationQuery()
  @HttpCode(200)
  findAll(
    @Query() genericFilter: GenericFilter,
    @Req() req
  ): Promise<
    | {
        data: CasinoGames[];
        totalItems: number;
        totalPages: number;
        currentPage: number;
        pageSize: number;
      }
  > {
    return this.casinoGamesService.findAll(genericFilter, req);
  }
}
