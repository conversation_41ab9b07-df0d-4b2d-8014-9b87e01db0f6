import { Injectable } from '@nestjs/common';
import { BaseProviderAdapter } from './base-adapter';
import {
  IBalanceRequest,
  IBalanceResponse,
  IBetRequest,
  IBetResponse,
  IWinRequest,
  IWinResponse,
  IProviderConfig,
  ProviderOperation,
} from '../interfaces/provider.interface';

/**
 * Adapter para o provedor EGT
 */
@Injectable()
export class EgtAdapter extends BaseProviderAdapter {
  readonly name = 'EGT';
  readonly version = '1.0.0';

  constructor(config: IProviderConfig) {
    super(config);
  }

  /**
   * Transforma requisição do EGT para formato padrão
   */
  transformRequest<T>(rawRequest: any, operation: string): T {
    this.logRequest(operation, rawRequest);

    switch (operation) {
      case ProviderOperation.BALANCE:
        return this.transformBalanceRequest(rawRequest) as T;
      case ProviderOperation.BET:
        return this.transformBetRequest(rawRequest) as T;
      case ProviderOperation.WIN:
        return this.transformWinRequest(rawRequest) as T;
      default:
        throw new Error(`Unsupported operation: ${operation}`);
    }
  }

  /**
   * Transforma resposta padrão para formato EGT
   */
  transformResponse<T>(standardResponse: any, operation: string): T {
    this.logResponse(operation, standardResponse);

    switch (operation) {
      case ProviderOperation.BALANCE:
        return this.transformBalanceResponse(standardResponse) as T;
      case ProviderOperation.BET:
        return this.transformBetResponse(standardResponse) as T;
      case ProviderOperation.WIN:
        return this.transformWinResponse(standardResponse) as T;
      default:
        throw new Error(`Unsupported operation: ${operation}`);
    }
  }

  /**
   * Validação específica para EGT
   */
  protected validateSpecificRequest(rawRequest: any, operation: string): boolean {
    switch (operation) {
      case ProviderOperation.BALANCE:
        return this.validateRequiredFields(rawRequest, ['user_id', 'game_id']);
      case ProviderOperation.BET:
        return this.validateRequiredFields(rawRequest, [
          'user_id',
          'game_id',
          'amount',
          'round_id',
        ]);
      case ProviderOperation.WIN:
        return this.validateRequiredFields(rawRequest, [
          'user_id',
          'game_id',
          'amount',
          'round_id',
          'bet_transaction_id',
        ]);
      default:
        return false;
    }
  }

  /**
   * Transforma requisição de saldo EGT
   */
  private transformBalanceRequest(rawRequest: any): IBalanceRequest {
    return {
      playerId: rawRequest.user_id,
      gameId: rawRequest.game_id,
      currency: rawRequest.currency || 'BRL',
      sessionId: rawRequest.session_id,
      timestamp: this.normalizeTimestamp(rawRequest.timestamp),
      metadata: {
        provider: this.name,
        originalRequest: rawRequest,
      },
    };
  }

  /**
   * Transforma requisição de aposta EGT
   */
  private transformBetRequest(rawRequest: any): IBetRequest {
    return {
      playerId: rawRequest.user_id,
      gameId: rawRequest.game_id,
      amount: this.formatAmount(rawRequest.amount),
      currency: rawRequest.currency || 'BRL',
      roundId: rawRequest.round_id,
      sessionId: rawRequest.session_id,
      timestamp: this.normalizeTimestamp(rawRequest.timestamp),
      metadata: {
        provider: this.name,
        betType: rawRequest.bet_type,
        originalRequest: rawRequest,
      },
    };
  }

  /**
   * Transforma requisição de ganho EGT
   */
  private transformWinRequest(rawRequest: any): IWinRequest {
    return {
      playerId: rawRequest.user_id,
      gameId: rawRequest.game_id,
      amount: this.formatAmount(rawRequest.amount),
      currency: rawRequest.currency || 'BRL',
      roundId: rawRequest.round_id,
      betTransactionId: rawRequest.bet_transaction_id,
      sessionId: rawRequest.session_id,
      timestamp: this.normalizeTimestamp(rawRequest.timestamp),
      metadata: {
        provider: this.name,
        winType: rawRequest.win_type,
        originalRequest: rawRequest,
      },
    };
  }

  /**
   * Transforma resposta de saldo para formato EGT
   */
  private transformBalanceResponse(standardResponse: IBalanceResponse): any {
    return {
      status: standardResponse.success ? 'success' : 'error',
      user_id: standardResponse.playerId,
      balance: standardResponse.balance,
      currency: standardResponse.currency,
      timestamp: standardResponse.timestamp.toISOString(),
      error_code: standardResponse.success ? null : 'BALANCE_ERROR',
      error_message: standardResponse.error || null,
    };
  }

  /**
   * Transforma resposta de aposta para formato EGT
   */
  private transformBetResponse(standardResponse: IBetResponse): any {
    return {
      status: standardResponse.success ? 'success' : 'error',
      user_id: standardResponse.playerId,
      transaction_id: standardResponse.transactionId,
      balance: standardResponse.balance,
      currency: standardResponse.currency,
      timestamp: standardResponse.timestamp.toISOString(),
      error_code: standardResponse.success ? null : 'BET_ERROR',
      error_message: standardResponse.error || null,
    };
  }

  /**
   * Transforma resposta de ganho para formato EGT
   */
  private transformWinResponse(standardResponse: IWinResponse): any {
    return {
      status: standardResponse.success ? 'success' : 'error',
      user_id: standardResponse.playerId,
      transaction_id: standardResponse.transactionId,
      balance: standardResponse.balance,
      currency: standardResponse.currency,
      timestamp: standardResponse.timestamp.toISOString(),
      error_code: standardResponse.success ? null : 'WIN_ERROR',
      error_message: standardResponse.error || null,
    };
  }
}
