import { Test, TestingModule } from '@nestjs/testing';
import { JackpotController } from './jackpot.controller';
import { JackpotService } from './jackpot.service';

describe('JackpotController', () => {
  let controller: JackpotController;
  let service: { feed: jest.Mock };

  beforeEach(async () => {
    service = {
      feed: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [JackpotController],
      providers: [{ provide: JackpotService, useValue: service }],
    }).compile();

    controller = module.get<JackpotController>(JackpotController);
  });

  it('feed should call service.feed and return result', async () => {
    const dto: any = { gameId: 'g1', amount: 10 };
    const result = { success: true };
    service.feed.mockResolvedValue(result);

    await expect(controller.feed(dto)).resolves.toEqual(result);
    expect(service.feed).toHaveBeenCalledWith(dto);
  });
});
