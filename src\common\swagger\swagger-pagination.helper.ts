import { getSchemaPath } from '@nestjs/swagger';
import { PaginationResponseDto } from '../dto/paginated-response.dto';

export function getPaginatedSchema(itemDto: any) {
  return {
    allOf: [
      { $ref: getSchemaPath(PaginationResponseDto) },
      {
        properties: {
          data: {
            type: 'array',
            description: 'Lista de itens',
            items: { $ref: getSchemaPath(itemDto) },
          },
        },
      },
    ],
  };
}
