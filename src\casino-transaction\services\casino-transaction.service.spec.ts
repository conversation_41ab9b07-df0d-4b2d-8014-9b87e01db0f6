import { DateUtilsService } from '@/common/utils/date';
import { HttpException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { EntityManager } from 'typeorm';
import { CasinoTransactionService } from './casino-transaction.service';

describe('CasinoTransactionService', () => {
  let service: CasinoTransactionService;
  let manager: any;
  let dateService: any;

  beforeEach(async () => {
    manager = {
      getRepository: jest.fn(),
      findOne: jest.fn(),
      save: jest.fn(),
      createQueryBuilder: jest.fn(() => queryBuilderMock),
    };
    dateService = {
      getDateRange: jest.fn((from, to) => ({ fromDate: from, toDate: to })),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CasinoTransactionService,
        { provide: EntityManager, useValue: manager },
        { provide: DateUtilsService, useValue: dateService },
      ],
    }).compile();

    service = module.get<CasinoTransactionService>(CasinoTransactionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  // Mocks auxiliares
  const queryBuilderMock: any = {
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    addSelect: jest.fn().mockReturnThis(),
    leftJoin: jest.fn().mockReturnThis(),
    groupBy: jest.fn().mockReturnThis(),
    getManyAndCount: jest.fn(),
    getRawOne: jest.fn(),
    getRawMany: jest.fn(),
    getRaw: jest.fn(),
    getOne: jest.fn(),
    getCount: jest.fn(),
  };

  const reqMock = { headers: { 'partner-id': 'partner1' } } as any;

  describe('findAll', () => {
    it('deve retornar lista paginada de transações', async () => {
      const filter = { page: 1, pageSize: 2, partners: 'partner1' };
      const transactionList = [
        { id: 1, transactionCancelId: null, winAmount: 10 },
        { id: 2, transactionCancelId: 'abc', winAmount: 0 },
      ];
      queryBuilderMock.getManyAndCount.mockResolvedValue([transactionList, 2]);
      manager.getRepository.mockReturnValue({
        createQueryBuilder: () => queryBuilderMock,
      });
      const result = await service.findAll(filter, reqMock);
      expect(result.data.length).toBe(2);
      expect(result.totalItems).toBe(2);
      expect(result.totalPages).toBe(1);
      expect(result.currentPage).toBe(1);
      expect(result.pageSize).toBe(2);
    });
    it('deve lançar exceção em caso de erro', async () => {
      manager.getRepository.mockImplementation(() => {
        throw new Error('fail');
      });
      await expect(
        service.findAll(
          { page: 1, pageSize: 10, partners: 'partner1' },
          reqMock,
        ),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('findAll - branches internos', () => {
    beforeEach(() => {
      manager.getRepository.mockReturnValue({
        createQueryBuilder: () => queryBuilderMock,
      });
    });

    it('deve filtrar por cancelTransaction = true', async () => {
      const filter = {
        page: 1,
        pageSize: 2,
        partners: 'partner1',
        cancelTransaction: 'true',
      };
      queryBuilderMock.getManyAndCount.mockResolvedValue([
        [{ id: 1, transactionCancelId: 'abc', winAmount: 0 }],
        1,
      ]);
      const result = await service.findAll(filter, reqMock);
      expect((result.data[0] as any).status).toBe('Canceled');
    });

    it('deve filtrar por cancelTransaction = false', async () => {
      const filter = {
        page: 1,
        pageSize: 2,
        partners: 'partner1',
        cancelTransaction: 'false',
      };
      // Para status Win, precisa ter winAmount > 0 e id não deve existir (ou ser null/undefined)
      // Mas como o serviço verifica transaction.id, vamos usar um objeto sem id definido
      queryBuilderMock.getManyAndCount.mockResolvedValue([
        [{ transactionCancelId: null, winAmount: 10 }],
        1,
      ]);
      const result = await service.findAll(filter, reqMock);
      expect((result.data[0] as any).status).toBe('Win');
    });

    it('deve filtrar por isWin = true', async () => {
      const filter = {
        page: 1,
        pageSize: 2,
        partners: 'partner1',
        isWin: 'true',
      };
      // Para isWin = true, precisa ter winAmount > 0
      queryBuilderMock.getManyAndCount.mockResolvedValue([
        [{ transactionCancelId: null, winAmount: 10 }],
        1,
      ]);
      const result = await service.findAll(filter, reqMock);
      expect((result.data[0] as any).status).toBe('Win');
    });

    it('deve filtrar por isWin = false', async () => {
      const filter = {
        page: 1,
        pageSize: 2,
        partners: 'partner1',
        isWin: 'false',
      };
      // Para isWin = false, precisa ter winAmount <= 0 ou null
      queryBuilderMock.getManyAndCount.mockResolvedValue([
        [{ transactionCancelId: null, winAmount: 0 }],
        1,
      ]);
      const result = await service.findAll(filter, reqMock);
      expect((result.data[0] as any).status).toBe('Lose');
    });

    it('deve filtrar por playerId', async () => {
      const filter = {
        page: 1,
        pageSize: 2,
        partners: 'partner1',
        playerId: 'player1',
      };
      queryBuilderMock.getManyAndCount.mockResolvedValue([
        [
          {
            id: 5,
            transactionCancelId: null,
            winAmount: 0,
            playerId: 'player1',
          },
        ],
        1,
      ]);
      const result = await service.findAll(filter, reqMock);
      expect(result.data[0].playerId).toBe('player1');
    });

    it('deve filtrar por gameId', async () => {
      const filter = {
        page: 1,
        pageSize: 2,
        partners: 'partner1',
        gameId: 'game1',
      };
      queryBuilderMock.getManyAndCount.mockResolvedValue([
        [{ id: 6, transactionCancelId: null, winAmount: 0, gameId: 'game1' }],
        1,
      ]);
      const result = await service.findAll(filter, reqMock);
      expect(result.data[0].gameId).toBe('game1');
    });

    it('deve filtrar por transactionId', async () => {
      const filter = {
        page: 1,
        pageSize: 2,
        partners: 'partner1',
        transactionId: 'tx1',
      };
      queryBuilderMock.getManyAndCount.mockResolvedValue([
        [{ id: 'tx1', transactionCancelId: null, winAmount: 0 }],
        1,
      ]);
      const result = await service.findAll(filter, reqMock);
      expect(result.data[0].id).toBe('tx1');
    });

    it('deve filtrar por betId', async () => {
      const filter = {
        page: 1,
        pageSize: 2,
        partners: 'partner1',
        betId: 'bet1',
      };
      queryBuilderMock.getManyAndCount.mockResolvedValue([
        [{ id: 'bet1', transactionCancelId: null, winAmount: 0 }],
        1,
      ]);
      const result = await service.findAll(filter, reqMock);
      expect(result.data[0].id).toBe('bet1');
    });

    it('deve mapear status Lose corretamente', async () => {
      const filter = { page: 1, pageSize: 2, partners: 'partner1' };
      // Para Lose: winAmount <= 0 e id não deve existir
      queryBuilderMock.getManyAndCount.mockResolvedValue([
        [{ transactionCancelId: null, winAmount: 0 }],
        1,
      ]);
      const result = await service.findAll(filter, reqMock);
      expect((result.data[0] as any).status).toBe('Lose');
    });
  });

  describe('findKpisPlayer', () => {
    it('deve retornar KPIs do jogador', async () => {
      // Corrigindo mock para simular corretamente todos os métodos encadeados
      const getRawMany = jest.fn().mockResolvedValue([
        {
          amount: 10,
          winAmount: 20,
          createdAt: '2024-01-01',
          requestType: 'RealMoney',
        },
        {
          amount: 5,
          winAmount: 0,
          createdAt: '2024-01-02',
          requestType: 'Bonus',
        },
      ]);
      const orderBy = jest.fn(() => ({ getRawMany }));
      const andWhere = jest.fn(() => ({ orderBy }));
      const where = jest.fn(() => ({ andWhere }));
      const addSelect = jest.fn(() => ({ addSelect, where }));
      manager.createQueryBuilder = jest
        .fn()
        .mockReturnValue({ select: addSelect });
      const result = await service.findKpisPlayer(
        { playerId: 'p1', page: 1, pageSize: 10, partners: 'partner1' },
        reqMock,
      );
      expect(result.totalStake).toBe(10);
      expect(result.totalWin).toBe(20);
      expect(result.bonus.stake).toBe(5);
      expect(result.bonus.win).toBe(0);
      expect(result.performance.profitLoss).toBe(10);
    });
    it('deve retornar valores zerados se não houver transações', async () => {
      const getRawMany = jest.fn().mockResolvedValue([]);
      const orderBy = jest.fn(() => ({ getRawMany }));
      const andWhere = jest.fn(() => ({ orderBy }));
      const where = jest.fn(() => ({ andWhere }));
      const addSelect = jest.fn(() => ({ addSelect, where }));
      manager.createQueryBuilder = jest
        .fn()
        .mockReturnValue({ select: addSelect });
      const result = await service.findKpisPlayer(
        { playerId: 'p1', page: 1, pageSize: 10, partners: 'partner1' },
        reqMock,
      );
      expect(result.totalStake).toBe(0);
      expect(result.totalWin).toBe(0);
      expect(result.bonus.stake).toBe(0);
      expect(result.bonus.win).toBe(0);
      expect(result.performance.profitLoss).toBe(0);
    });
    it('deve lançar exceção em caso de erro', async () => {
      manager.createQueryBuilder = jest.fn().mockImplementation(() => {
        throw new Error('fail');
      });
      await expect(
        service.findKpisPlayer(
          { playerId: 'p1', page: 1, pageSize: 10, partners: 'partner1' },
          reqMock,
        ),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('findAvgBet', () => {
    it('deve retornar averageBet', async () => {
      dateService.getDateRange = jest.fn(() => ({
        fromDate: '2024-01-01',
        toDate: '2024-01-31',
      }));
      manager.createQueryBuilder = jest.fn().mockReturnValue({
        select: () => ({
          addSelect: () => ({
            where: () => ({
              andWhere: () => ({
                getRawOne: () => Promise.resolve({ amount: 100, count: 4 }),
              }),
            }),
          }),
        }),
      });
      const result = await service.findAvgBet(
        {
          fromDate: '2024-01-01',
          toDate: '2024-01-31',
          page: 1,
          pageSize: 10,
          partners: 'partner1',
        },
        reqMock,
      );
      expect(result.averageBet).toBe(25);
    });
    it('deve retornar 0 se não houver apostas', async () => {
      manager.createQueryBuilder = jest.fn().mockReturnValue({
        select: () => ({
          addSelect: () => ({
            where: () => ({
              andWhere: () => ({
                getRawOne: () => Promise.resolve({ amount: 0, count: 0 }),
              }),
            }),
          }),
        }),
      });
      const result = await service.findAvgBet(
        {
          fromDate: '2024-01-01',
          toDate: '2024-01-31',
          page: 1,
          pageSize: 10,
          partners: 'partner1',
        },
        reqMock,
      );
      expect(result.averageBet).toBe(0);
    });
    it('deve lançar exceção em caso de erro', async () => {
      manager.createQueryBuilder = jest.fn().mockImplementation(() => {
        throw new Error('fail');
      });
      await expect(
        service.findAvgBet(
          {
            fromDate: '2024-01-01',
            toDate: '2024-01-31',
            page: 1,
            pageSize: 10,
            partners: 'partner1',
          },
          reqMock,
        ),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('findBetCount', () => {
    it('deve retornar totalPlayer', async () => {
      dateService.getDateRange = jest.fn(() => ({
        fromDate: '2024-01-01',
        toDate: '2024-01-31',
      }));
      manager.createQueryBuilder = jest.fn().mockReturnValue({
        select: () => ({
          leftJoin: () => ({
            where: () => ({
              andWhere: () => ({
                andWhere: () => ({
                  andWhere: () => ({
                    andWhere: () => ({
                      getRawOne: () => Promise.resolve({ totalPlayer: 3 }),
                    }),
                  }),
                }),
              }),
            }),
          }),
        }),
      });
      const result = await service.findBetCount(
        {
          fromDate: '2024-01-01',
          toDate: '2024-01-31',
          page: 1,
          pageSize: 10,
          partners: 'partner1',
        },
        reqMock,
      );
      expect(result.totalPlayer).toBe(3);
    });
    it('deve lançar exceção em caso de erro', async () => {
      manager.createQueryBuilder = jest.fn().mockImplementation(() => {
        throw new Error('fail');
      });
      await expect(
        service.findBetCount(
          {
            fromDate: '2024-01-01',
            toDate: '2024-01-31',
            page: 1,
            pageSize: 10,
            partners: 'partner1',
          },
          reqMock,
        ),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('findBetProfitable', () => {
    it('deve retornar top3Games e bottom3Games', async () => {
      // Corrigindo mock para simular múltiplos encadeamentos de leftJoin
      const getRawMany = jest.fn().mockResolvedValue([
        { gameName: 'A', profit: 10, totalBets: 2, totalWinAmount: 5 },
        { gameName: 'B', profit: 5, totalBets: 1, totalWinAmount: 2 },
        { gameName: 'C', profit: 1, totalBets: 1, totalWinAmount: 1 },
        { gameName: 'D', profit: -2, totalBets: 1, totalWinAmount: 0 },
      ]);
      const setParameters = jest.fn(() => ({ getRawMany }));
      const addGroupBy = jest.fn(() => ({ setParameters }));
      const groupBy = jest.fn(() => ({ addGroupBy }));
      const where = jest.fn(() => ({ groupBy }));
      // Mock encadeável de leftJoin
      const leftJoin = jest.fn(() => ({ leftJoin, where }));
      const addSelect = jest.fn(() => ({ addSelect, leftJoin }));
      manager.createQueryBuilder = jest
        .fn()
        .mockReturnValue({ select: addSelect });
      const result = await service.findBetProfitable(
        {
          fromDate: '2024-01-01',
          toDate: '2024-01-31',
          page: 1,
          pageSize: 10,
          partners: 'partner1',
        },
        reqMock,
      );
      expect(result.top3Games.length).toBe(3);
      expect(result.bottom3Games.length).toBe(3);
    });
    it('deve lançar exceção em caso de erro', async () => {
      manager.createQueryBuilder = jest.fn().mockImplementation(() => {
        throw new Error('fail');
      });
      await expect(
        service.findBetProfitable(
          {
            fromDate: '2024-01-01',
            toDate: '2024-01-31',
            page: 1,
            pageSize: 10,
            partners: 'partner1',
          },
          reqMock,
        ),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('findBetTransaction', () => {
    it('deve retornar estatísticas de transações', async () => {
      manager.createQueryBuilder = jest
        .fn()
        .mockReturnValueOnce({
          select: () => ({
            where: () => ({
              andWhere: () => ({
                andWhere: () => ({
                  andWhere: () => ({
                    andWhere: () => ({
                      getRawMany: () =>
                        Promise.resolve([{ totalBet: 100 }, { totalBet: 50 }]),
                    }),
                  }),
                }),
              }),
            }),
          }),
        })
        .mockReturnValueOnce({
          select: () => ({
            where: () => ({
              andWhere: () => ({
                andWhere: () => ({
                  andWhere: () => ({
                    andWhere: () => ({
                      getRawMany: () =>
                        Promise.resolve([
                          { totalSettle: 30 },
                          { totalSettle: 20 },
                        ]),
                    }),
                  }),
                }),
              }),
            }),
          }),
        })
        .mockReturnValueOnce({
          select: () => ({
            where: () => ({
              andWhere: () => ({
                andWhere: () => ({
                  andWhere: () => ({
                    andWhere: () => ({
                      getRawOne: () => Promise.resolve({ totalBets: 2 }),
                    }),
                  }),
                }),
              }),
            }),
          }),
        })
        .mockReturnValueOnce({
          select: () => ({
            where: () => ({
              andWhere: () => ({
                getRawOne: () => Promise.resolve({ totalPlayers: 1 }),
              }),
            }),
          }),
        });
      const result = await service.findBetTransaction(
        {
          fromDate: '2024-01-01',
          toDate: '2024-01-31',
          page: 1,
          pageSize: 10,
          partners: 'partner1',
        },
        reqMock,
      );
      expect(result.income).toBe(150);
      expect(result.profit).toBe(50);
      expect(result.GGR).toBe(100);
      expect(result.profitability).toBeCloseTo(66.67, 1);
      expect(result.averageBet).toBe(75);
      expect(result.totalUniquePlayers).toBe(1);
    });
    it('deve lançar exceção em caso de erro', async () => {
      manager.createQueryBuilder = jest.fn().mockImplementation(() => {
        throw new Error('fail');
      });
      await expect(
        service.findBetTransaction(
          {
            fromDate: '2024-01-01',
            toDate: '2024-01-31',
            page: 1,
            pageSize: 10,
            partners: 'partner1',
          },
          reqMock,
        ),
      ).rejects.toThrow(HttpException);
    });
  });
});
