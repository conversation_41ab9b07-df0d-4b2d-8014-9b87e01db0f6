import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class PartnerService {
  constructor(private readonly httpService: HttpService) {}
  private readonly urlPartner = `${process.env.API_PARTNER}/v1/pam/partner-settings`;
  private readonly logger = new Logger(PartnerService.name);

  public async getPartnerById(partnerId: string, req) {
    this.logger.log(`[Inicio] getPartnerById:${partnerId}`);

    const auth = req.headers.authorization;
    const authUserHeader = req.headers['x-user-context'];
    const response = await firstValueFrom(
      this.httpService.get(`${this.urlPartner}/general`, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: auth,
          'x-user-context': authUserHeader,
          'partner-id': partnerId,
        },
      }),
    );
    if (response && response.data !== undefined) {
      this.logger.log(
        `[Sucesso] getPartnerById: ${JSON.stringify(response.data)}`,
      );
      return response.data;
    } else {
      this.logger.error(
        `[Erro] getPartnerById: ${JSON.stringify(response.data)}`,
      );
      throw new HttpException(`Partner not found`, HttpStatus.NOT_FOUND);
    }
  }
}
