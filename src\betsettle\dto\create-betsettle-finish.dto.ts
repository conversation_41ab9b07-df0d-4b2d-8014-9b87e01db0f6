import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsUUID } from 'class-validator';

export class CreateBetsettleFinishDto {
  @ApiProperty({ example: '7be4657e-daff-40f7-a4a4-0ac0085906ac' })
  @IsUUID()
  playerId: string;

  @ApiProperty({
    example: 'cd96e617-f36d-4918-b006-7faca1dcc0d8',
    description: 'Pode ser UUID ou string gerada pelo provedor',
  })
  @IsString()
  roundId: string;

  @ApiProperty({ example: 'c1143f0f-d615-447a-aff3-9b6c285a4da1' })
  @IsString()
  sessionId: string;
}
