import { Test, TestingModule } from '@nestjs/testing';
import { AuthenticateService } from './authenticate.service';
import { WalletService } from '../wallet/wallet.service';
import { EntityManager } from 'typeorm';
import { HttpException } from '@nestjs/common';

const mockDto = {
  token: 'token-123',
  playerId: 'player-1',
  sessionId: 'session-1',
};

describe('AuthenticateService', () => {
  let service: AuthenticateService;
  let entityManager: any;
  let walletService: any;

  const mockEntityManager = {
    findOne: jest.fn(),
    save: jest.fn(),
  };
  const mockWalletService = {
    getBalanceByPlayer: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthenticateService,
        { provide: EntityManager, useValue: mockEntityManager },
        { provide: WalletService, useValue: mockWalletService },
      ],
    }).compile();

    service = module.get<AuthenticateService>(AuthenticateService);
    entityManager = module.get<EntityManager>(EntityManager);
    walletService = module.get<WalletService>(WalletService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('authenticate', () => {
    it('deve autenticar e retornar balance com sucesso', async () => {
      const mockSession = {
        launchToken: 'token-123',
        playerId: 'player-1',
        partnerId: 'partner-1',
      };
      entityManager.findOne.mockResolvedValue(mockSession);
      jest.spyOn(service, 'getBalance').mockResolvedValue(100);
      jest
        .spyOn(service as any, 'updateSessionAuthId')
        .mockResolvedValue(undefined);
      const result = await service.authenticate(mockDto);
      expect(result).toBe(100);
      expect(entityManager.findOne).toHaveBeenCalled();
      expect(service.getBalance).toHaveBeenCalledWith('player-1', 'partner-1');
      // Não é possível verificar método privado updateSessionAuthId diretamente
    });

    it('deve lançar erro se token inválido', async () => {
      entityManager.findOne.mockResolvedValue(undefined);
      await expect(service.authenticate(mockDto)).rejects.toThrow(
        HttpException,
      );
    });

    it('deve retornar balance 0 se getBalance lançar erro', async () => {
      const mockSession = {
        launchToken: 'token-123',
        playerId: 'player-1',
        partnerId: 'partner-1',
      };
      entityManager.findOne.mockResolvedValue(mockSession);
      jest
        .spyOn(service, 'getBalance')
        .mockRejectedValue(new Error('Erro balance'));
      jest
        .spyOn(service as any, 'updateSessionAuthId')
        .mockResolvedValue(undefined);
      const result = await service.authenticate(mockDto);
      expect(result).toBe(0);
    });
  });

  describe('getBalance', () => {
    it('deve retornar o balance do wallet com sucesso', async () => {
      walletService.getBalanceByPlayer.mockResolvedValue({ balance: 200 });
      const result = await service.getBalance('player-1', 'partner-1');
      expect(result).toBe(200);
    });

    it('deve lançar HttpException se não encontrar balance', async () => {
      walletService.getBalanceByPlayer.mockResolvedValue({});
      await expect(service.getBalance('player-1', 'partner-1')).rejects.toThrow(
        HttpException,
      );
    });

    it('deve lançar HttpException se ocorrer erro', async () => {
      walletService.getBalanceByPlayer.mockRejectedValue(
        new Error('Erro externo'),
      );
      await expect(service.getBalance('player-1', 'partner-1')).rejects.toThrow(
        HttpException,
      );
    });
  });
});
