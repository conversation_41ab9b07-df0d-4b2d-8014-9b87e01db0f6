import { Controller, Post, Body } from '@nestjs/common';
import { BonusService } from './bonus.service';
import { FinishBonusDto } from './dto/finish-bonus.dto';
import { CancelBonusDto } from './dto/cancel-bonus.dto';
import { IssueDto } from './dto/issue-bonus.dto';
import { TypesDto } from './dto/type-bonus.dto';
import { ResponseFinishBonusDto } from './dto/response-finish-bonus.dto';
import { ResponseTypesDto } from './dto/response-types.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@Controller('bonus')
@ApiTags('Bonus')
export class BonusController {
  constructor(private readonly bonusService: BonusService) {}

  @Post('finish')
  @ApiOperation({
    summary: 'Finalizar Bônus',
    description: 'Endpoint para finalizar bônus e processar ganhos',
  })
  @ApiResponse({
    status: 200,
    description: 'Bônus finalizado com sucesso',
    type: ResponseFinishBonusDto,
  })
  async finish(
    @Body() finishBonusDto: FinishBonusDto,
  ): Promise<ResponseFinishBonusDto> {
    return this.bonusService.finish(finishBonusDto);
  }

  @Post('cancel')
  @ApiOperation({
    summary: 'Cancelar Bônus',
    description: 'Endpoint para cancelar bônus',
  })
  @ApiResponse({
    status: 200,
    description: 'Bônus cancelado com sucesso',
  })
  async cancel(@Body() cancelBonusDto: CancelBonusDto): Promise<any> {
    return this.bonusService.cancel(cancelBonusDto);
  }

  @Post('issue')
  @ApiOperation({
    summary: 'Emitir Bônus',
    description: 'Endpoint para emitir bônus para um jogador',
  })
  @ApiResponse({
    status: 200,
    description: 'Bônus emitido com sucesso',
  })
  async issue(@Body() issueBonusDto: IssueDto): Promise<any> {
    return this.bonusService.issue(issueBonusDto);
  }

  @Post('types')
  @ApiOperation({
    summary: 'Tipos de Bônus',
    description: 'Endpoint para obter tipos de bônus disponíveis',
  })
  @ApiResponse({
    status: 200,
    description: 'Tipos de bônus retornados com sucesso',
    type: ResponseTypesDto,
  })
  async types(@Body() typeBonusDto: TypesDto): Promise<ResponseTypesDto> {
    return this.bonusService.types(typeBonusDto);
  }
}
