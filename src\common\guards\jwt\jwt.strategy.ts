import { ExtractJwt, Strategy } from "passport-jwt";
import { PassportStrategy } from "@nestjs/passport";
import { Injectable } from "@nestjs/common";
import "dotenv/config";
import { PamService } from './../../../pam/pam.service';
import { TokenPayload } from "@/common/entities/token-payload.entity";

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly PamService: PamService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: process.env.JWT_SECRET,
      passReqToCallback: true,
    });
  }

  async validate(request: any, payload: TokenPayload) {
    const user = await this.PamService.getUser(payload.email, request);
    if (user) {
        if (user.isTwoFactorAuthenticationEnabled) {
          return true;
        }
      return user;
    }
  }
}
