import { Injectable, NestMiddleware, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

/**
 * Interface para configuração de rate limiting
 */
interface RateLimitConfig {
  windowMs: number; // Janela de tempo em milissegundos
  maxRequests: number; // Máximo de requisições por janela
  message?: string; // Mensagem de erro personalizada
}

/**
 * Middleware para controle de rate limiting por provedor
 */
@Injectable()
export class RateLimitMiddleware implements NestMiddleware {
  private readonly logger = new Logger(RateLimitMiddleware.name);
  private readonly requestCounts = new Map<string, { count: number; resetTime: number }>();

  // Configurações de rate limit por provedor
  private readonly configs: Record<string, RateLimitConfig> = {
    EGT: {
      windowMs: 60 * 1000, // 1 minuto
      maxRequests: 100,
      message: 'Too many requests from EGT provider',
    },
    BGAMING: {
      windowMs: 60 * 1000, // 1 minuto
      maxRequests: 200,
      message: 'Too many requests from BGaming provider',
    },
    DEFAULT: {
      windowMs: 60 * 1000, // 1 minuto
      maxRequests: 50,
      message: 'Too many requests',
    },
  };

  use(req: Request, res: Response, next: NextFunction): void {
    const provider = this.getProvider(req);
    const clientId = this.getClientId(req);
    const key = `${provider}:${clientId}`;

    const config = this.configs[provider] || this.configs.DEFAULT;
    const now = Date.now();

    // Obtém ou cria o contador para este cliente/provedor
    let requestData = this.requestCounts.get(key);

    if (!requestData || now > requestData.resetTime) {
      // Primeira requisição ou janela expirou
      requestData = {
        count: 1,
        resetTime: now + config.windowMs,
      };
      this.requestCounts.set(key, requestData);
    } else {
      // Incrementa o contador
      requestData.count++;
    }

    // Verifica se excedeu o limite
    if (requestData.count > config.maxRequests) {
      this.logger.warn(`Rate limit exceeded for ${provider}:${clientId}`, {
        provider,
        clientId,
        count: requestData.count,
        limit: config.maxRequests,
        resetTime: new Date(requestData.resetTime).toISOString(),
      });

      throw new HttpException(
        {
          statusCode: HttpStatus.TOO_MANY_REQUESTS,
          message: config.message,
          retryAfter: Math.ceil((requestData.resetTime - now) / 1000),
        },
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }

    // Adiciona headers informativos
    res.setHeader('X-RateLimit-Limit', config.maxRequests);
    res.setHeader('X-RateLimit-Remaining', config.maxRequests - requestData.count);
    res.setHeader('X-RateLimit-Reset', Math.ceil(requestData.resetTime / 1000));

    // Log para monitoramento
    if (requestData.count > config.maxRequests * 0.8) {
      this.logger.warn(`Rate limit warning for ${provider}:${clientId}`, {
        provider,
        clientId,
        count: requestData.count,
        limit: config.maxRequests,
        percentage: Math.round((requestData.count / config.maxRequests) * 100),
      });
    }

    next();
  }

  /**
   * Identifica o provedor da requisição
   */
  private getProvider(req: Request): string {
    // Tenta obter do header
    if (req.headers['x-provider']) {
      return req.headers['x-provider'].toString().toUpperCase();
    }

    // Tenta obter da URL
    const pathSegments = req.path.split('/');
    if (pathSegments.length > 2 && pathSegments[1] === 'gateway') {
      const possibleProvider = pathSegments[2].toUpperCase();
      if (this.configs[possibleProvider]) {
        return possibleProvider;
      }
    }

    // Tenta detectar pelo User-Agent
    const userAgent = req.get('User-Agent')?.toLowerCase() || '';
    if (userAgent.includes('egt')) return 'EGT';
    if (userAgent.includes('bgaming')) return 'BGAMING';

    return 'DEFAULT';
  }

  /**
   * Identifica o cliente (IP + User-Agent hash)
   */
  private getClientId(req: Request): string {
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    
    // Cria um hash simples para identificar o cliente
    const clientString = `${ip}:${userAgent}`;
    return Buffer.from(clientString).toString('base64').substring(0, 16);
  }

  /**
   * Limpa contadores expirados (deve ser chamado periodicamente)
   */
  cleanupExpiredCounters(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, data] of this.requestCounts.entries()) {
      if (now > data.resetTime) {
        this.requestCounts.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      this.logger.log(`Cleaned up ${cleaned} expired rate limit counters`);
    }
  }

  /**
   * Obtém estatísticas de rate limiting
   */
  getStats(): Array<{ key: string; count: number; resetTime: Date }> {
    const stats = [];
    
    for (const [key, data] of this.requestCounts.entries()) {
      stats.push({
        key,
        count: data.count,
        resetTime: new Date(data.resetTime),
      });
    }

    return stats;
  }
}
