import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  JoinColumn,
  OneToOne,
  ManyToOne,
} from "typeorm";
import { ApiProperty } from "@nestjs/swagger";
import { Exclude } from "class-transformer";
import { RegionEntity } from "./region-utils.entity";
@Entity("player_contact_info", { schema: "player" })
export class ContactInfoEntity {
  @ApiProperty({
    description: "Identificador do contato",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: true,
  })
  @PrimaryGeneratedColumn("uuid", {
    name: "id",
  })
  id: string;

  @ApiProperty({
    description: "Identificador do jogador",
    type: "string",
    example: "761a1b06-b48f-4ffc-876e-9e80c6b7d59d",
    required: true,
  })
  @Column({
    name: "id_player",
    nullable: false,
  })
  playerId: string;

  // @ManyToOne(() => PlayerEntity, (player) => player.contactInfo)
  //   @OneToOne(() => PlayerEntity, (player) => player.contactInfo)
  //   @JoinColumn({ name: "id_player" })
  //   player: PlayerEntity;

  @ApiProperty({
    description: "Telefone do Jogador",
    type: "string",
    example: "11457654321",
    required: false,
  })
  @Column("varchar", {
    name: "phone",
    nullable: true,
  })
  phone: string | null;

  @ApiProperty({
    description: "Telefone celular do Jogador",
    type: "string",
    example: "11987654321",
    required: false,
  })
  @Column("varchar", {
    name: "mobile",
    nullable: true,
  })
  mobile: string | null;

  @ApiProperty({
    description: "E-mail do Jogador",
    type: "string",
    example: "<EMAIL>",
    required: false,
  })
  @Column("varchar", {
    name: "email",
    nullable: true,
  })
  email: string | null;

  @ApiProperty({
    description: "Identificador da região",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: false,
  })
  @Column("varchar", {
    name: "id_region",
    nullable: true,
  })
  regionId: string | null;

  @ManyToOne(() => RegionEntity, (region) => region.contactInfos, {
    eager: false,
    nullable: true,
  })
  @JoinColumn({ name: "id_region" })
  region: RegionEntity;

  @ApiProperty({
    description: "CEP do jogador",
    type: "string",
    example: "12345-678",
    required: false,
  })
  @Column("varchar", {
    name: "zip_code",
    nullable: true,
  })
  zipCode: string | null;

  @ApiProperty({
    description: "Cidade do jogador",
    type: "string",
    example: "São Paulo",
    required: false,
  })
  @Column("varchar", {
    name: "city",
    nullable: true,
  })
  city: string | null;

  @ApiProperty({
    description: "Endereço do jogador",
    type: "string",
    example: "Rua Exemplo, 123",
    required: false,
  })
  @Column("varchar", {
    name: "address",
    nullable: true,
  })
  address: string | null;

  @ApiProperty({
    description: "Estado do jogador",
    type: "string",
    example: "SP",
    required: false,
  })
  @Column("varchar", {
    name: "state",
    nullable: true,
  })
  state: string | null;

  @ApiProperty({
    description: "Data de criação",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: true,
  })
  @CreateDateColumn({
    name: "created_at",
    type: "timestamptz",
    nullable: false,
  })
  createdAt: Date;

  @ApiProperty({
    description: "Data de atualização",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: false,
  })
  @UpdateDateColumn({
    name: "updated_at",
    type: "timestamptz",
    nullable: true,
  })
  updatedAt: Date | null;

  @DeleteDateColumn({
    name: "deleted_at",
    type: "timestamptz",
    nullable: true,
  })
  @Exclude()
  deletedAt: Date | null;

  @Column("boolean", {
    name: "is_deleted",
    nullable: true,
  })
  @Exclude()
  isDeleted: boolean | null;
}
