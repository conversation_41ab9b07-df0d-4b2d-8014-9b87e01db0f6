import { CasinoSessionEntity } from "../../common/entities/casino-session.entity";
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";

@Entity("casino_games")
export class CasinoGames {
  @PrimaryGeneratedColumn("uuid", { name: "id" })
  id: string;

  @OneToMany(() => CasinoSessionEntity, (session) => session.games)
  sessions: CasinoSessionEntity[];

  @Column({ name: "name" })
  name: string;

  @Column({ name: "description" })
  description: string;

  @Column({ name: "game_id" })
  gameId: string;

  @Column({ name: "game_provider" })
  gameProvider: string;

  @Column({ name: "is_disabled", default: false })
  isDisable: boolean;

  @Column({ name: "image", nullable: true })
  image: string;

  @CreateDateColumn({
    name: "created_at",
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: "updated_at",
  })
  updatedAt: Date;

  @DeleteDateColumn({
    name: "deleted_at",
  })
  deletedAt: Date;

  @Column({
    name: "is_deleted",
    length: 1,
  })
  isDeleted: string;
}
