import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { CasinoSessionEntity } from '../../common/entities/casino-session.entity';
import { CasinoGameProvider } from './casino-game-provider.entity';
import { CasinoGamesCategories } from './casino-games-categories.entity';

@Entity('casino_games')
export class CasinoGames {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  @OneToMany(() => CasinoSessionEntity, session => session.games)
  sessions: CasinoSessionEntity[];

  @Column({ name: 'name' })
  name: string;

  @Column({ name: 'description' })
  description: string;

  @Column({ name: 'game_id' })
  gameId: string;

  @Column({ name: 'game_provider' })
  gameProvider: string;

  @Column({ name: 'is_disabled', default: false })
  isDisable: boolean;

  @Column({ name: 'image', nullable: true })
  image: string;

  @Column({ name: 'path', nullable: true })
  path: string;

  @Column({ name: 'image_aggregator', nullable: true })
  imageAggregator: string;

  @Column({ name: 'path_aggregator', nullable: true })
  pathAggregator: string;

  @CreateDateColumn({
    name: 'created_at',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updated_at',
  })
  updatedAt: Date;

  @DeleteDateColumn({
    name: 'deleted_at',
  })
  deletedAt: Date;

  @Column({
    name: 'is_deleted',
    default: false,
  })
  isDeleted: boolean;

  @Column({ name: 'game_provider_id' })
  gameProviderId: string;

  @Column({ name: 'game_category_id' })
  gameCategoryId: string;

  @Column({ name: 'is_desktop', nullable: true })
  isDesktop: boolean;

  @Column({ name: 'is_mobile', nullable: true })
  isMobile: boolean;

  @Column({ name: 'position', type: 'int', nullable: true })
  position: number;

  @Column({ name: 'last_user_modify', nullable: true })
  lastUserModify: string;

  @ManyToOne(() => CasinoGameProvider, provider => provider.id)
  @JoinColumn({ name: 'game_provider_id' })
  provider: CasinoGameProvider;

  @ManyToOne(() => CasinoGamesCategories, category => category.id)
  @JoinColumn({ name: 'game_category_id' })
  category: CasinoGamesCategories;
}
