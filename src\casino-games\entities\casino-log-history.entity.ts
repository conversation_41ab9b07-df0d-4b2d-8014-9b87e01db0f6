import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity({ name: 'casino_log_history', schema: 'casino' })
export class CasinoLogHistory {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  @Column({ name: 'action_type', nullable: true })
  actionType: string;

  @Column({ name: 'field', nullable: true })
  field: string;

  @Column({ name: 'old_value', nullable: true })
  oldValue: string;

  @Column({ name: 'new_value', nullable: true })
  newValue: string;

  @Column({ name: 'last_user_modify', nullable: true })
  lastUserModify: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;
}
