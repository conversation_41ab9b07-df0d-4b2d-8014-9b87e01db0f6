import { Test, TestingModule } from '@nestjs/testing';
import { BetService } from './bet.service';
import { WalletService } from '../wallet/wallet.service';
import { EntityManager } from 'typeorm';
import { Jwt } from '@/common/interfaces/JWT';
import { GenderEnum } from '@/gamelaunch/enum/gender.enum';

const mockCreateBetDto = {
  sessionId: 'session-123',
  transactionId: 'tx-123',
  playerId: 'player-123',
  requestType: 'RealMoney',
  amount: 100.5,
  gameId: 'game-123',
  roundId: 'round-123',
};
const uuid = '123e4567-e89b-12d3-a456-************';
const mockAuth = {
  payload: {
    id: uuid,
    currency: 'BRL',
    language: 'pt',
    country: 'BR',
    firstName: 'Test',
    lastName: 'User',
    alias: 'testuser',
    gender: GenderEnum.MALE,
    balance: '100',
    email: '<EMAIL>',
    password: '123',
    partnerId: uuid,
    partner: {
      id: uuid,
      cashierUrl: '',
      closeUrl: '',
      name: '<PERSON><PERSON><PERSON>',
    },
  },
  iat: 0,
  exp: 0,
} as Jwt;

describe('BetService', () => {
  let service: BetService;
  let entityManager: any;
  let walletService: any;

  const mockEntityManager = {
    findOne: jest.fn(),
    create: jest.fn(),
    insert: jest.fn(),
  };
  const mockWalletService = {
    debit: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BetService,
        { provide: EntityManager, useValue: mockEntityManager },
        { provide: WalletService, useValue: mockWalletService },
      ],
    }).compile();

    service = module.get<BetService>(BetService);
    entityManager = module.get<EntityManager>(EntityManager);
    walletService = module.get<WalletService>(WalletService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('deve criar uma aposta com sucesso', async () => {
    entityManager.findOne.mockResolvedValue({ gameProvider: 'provider-1' });
    walletService.debit.mockResolvedValue({
      subAccount: { balance: 50 },
      transactionId: 'wallet-tx-1',
    });
    entityManager.create.mockReturnValue({ id: 'casino-tx-1' });
    entityManager.insert.mockResolvedValue(undefined);

    const result = await service.create(mockCreateBetDto, mockAuth);
    expect(result).toEqual({
      balance: 50,
      errorCode: 0,
      casinoTransactionId: 'casino-tx-1',
      reconcileAmount: 0,
    });
    expect(entityManager.findOne).toHaveBeenCalled();
    expect(walletService.debit).toHaveBeenCalled();
    expect(entityManager.create).toHaveBeenCalled();
    expect(entityManager.insert).toHaveBeenCalled();
  });

  it('deve retornar erro se ocorrer exception', async () => {
    entityManager.findOne.mockRejectedValue(new Error('DB error'));
    const result = await service.create(mockCreateBetDto, mockAuth);
    expect(result.errorCode).toBe(2);
    expect(result.errorMsg).toBe('DB error');
  });

  it('deve retornar erro customizado se exception tiver response', async () => {
    entityManager.findOne.mockRejectedValue({ response: { errorCode: 99, errorMsg: 'Erro customizado' } });
    const result = await service.create(mockCreateBetDto, mockAuth);
    expect(result.errorCode).toBe(99);
    expect(result.errorMsg).toBe('Erro customizado');
  });
}); 