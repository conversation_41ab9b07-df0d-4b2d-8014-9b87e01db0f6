import { Test, TestingModule } from '@nestjs/testing';
import { BetService } from './bet.service';
import { WalletService } from '../wallet/wallet.service';
import { EntityManager } from 'typeorm';
import { Jwt } from '@/common/interfaces/JWT';
import { GenderEnum } from '@/gamelaunch/enum/gender.enum';

const mockCreateBetDto = {
  sessionId: 'session-123',
  transactionId: 'tx-123',
  playerId: 'player-123',
  requestType: 'RealMoney',
  amount: 100.5,
  gameId: 'game-123',
  roundId: 'round-123',
};
const uuid = '123e4567-e89b-12d3-a456-************';
const mockAuth = {
  payload: {
    id: uuid,
    currency: 'BRL',
    language: 'pt',
    country: 'BR',
    firstName: 'Test',
    lastName: 'User',
    alias: 'testuser',
    gender: GenderEnum.MALE,
    balance: '100',
    email: '<EMAIL>',
    password: '123',
    partnerId: uuid,
    partner: {
      id: uuid,
      cashierUrl: '',
      closeUrl: '',
      name: '<PERSON><PERSON><PERSON>',
    },
  },
  iat: 0,
  exp: 0,
} as Jwt;

describe('BetService', () => {
  let service: BetService;
  let entityManager: any;
  let walletService: any;

  const mockEntityManager = {
    findOne: jest.fn(),
    create: jest.fn(),
    insert: jest.fn(),
  };
  const mockWalletService = {
    debit: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BetService,
        { provide: EntityManager, useValue: mockEntityManager },
        { provide: WalletService, useValue: mockWalletService },
      ],
    }).compile();

    service = module.get<BetService>(BetService);
    entityManager = module.get<EntityManager>(EntityManager);
    walletService = module.get<WalletService>(WalletService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('deve criar uma aposta com sucesso', async () => {
    entityManager.findOne.mockResolvedValue({ gameProvider: 'provider-1' });
    walletService.debit.mockResolvedValue({
      subAccount: { balance: 50 },
      transactionId: 'wallet-tx-1',
    });
    entityManager.create.mockReturnValue({ id: 'casino-tx-1' });
    entityManager.insert.mockResolvedValue(undefined);

    const result = await service.create(mockCreateBetDto, mockAuth);
    expect(result).toEqual({
      balance: 50,
      casinoTransactionId: 'casino-tx-1',
      reconcileAmount: 0,
    });
    expect(entityManager.findOne).toHaveBeenCalled();
    expect(walletService.debit).toHaveBeenCalled();
    expect(entityManager.create).toHaveBeenCalled();
    expect(entityManager.insert).toHaveBeenCalled();
  });

  it('deve lançar HttpException quando ocorre erro no banco de dados', async () => {
    entityManager.findOne.mockRejectedValue(new Error('DB error'));

    await expect(service.create(mockCreateBetDto, mockAuth)).rejects.toThrow(
      'Erro ao criar aposta.',
    );
  });

  it('deve lançar HttpException quando jogo não é encontrado', async () => {
    entityManager.findOne.mockResolvedValue(null);

    await expect(service.create(mockCreateBetDto, mockAuth)).rejects.toThrow();
  });

  it('deve lançar HttpException quando wallet service falha', async () => {
    entityManager.findOne.mockResolvedValue({ gameProvider: 'provider-1' });
    walletService.debit.mockRejectedValue(new Error('Insufficient funds'));

    await expect(service.create(mockCreateBetDto, mockAuth)).rejects.toThrow();
  });

  it('deve criar transação com valores corretos', async () => {
    const gameData = { gameProvider: 'test-provider' };
    const walletResult = {
      subAccount: { balance: 75.5 },
      transactionId: 'wallet-tx-123',
    };

    entityManager.findOne.mockResolvedValue(gameData);
    walletService.debit.mockResolvedValue(walletResult);
    entityManager.create.mockReturnValue({ id: 'casino-tx-123' });
    entityManager.insert.mockResolvedValue(undefined);

    await service.create(mockCreateBetDto, mockAuth);

    expect(entityManager.create).toHaveBeenCalledWith(
      expect.anything(),
      expect.objectContaining({
        playerId: mockCreateBetDto.playerId,
        gameId: mockCreateBetDto.gameId,
        amount: mockCreateBetDto.amount,
        balance: 75.5,
        transactionId: 'wallet-tx-123',
      }),
    );
  });

  it('deve chamar wallet debit com parâmetros corretos', async () => {
    const gameData = { gameProvider: 'provider-test' };
    entityManager.findOne.mockResolvedValue(gameData);
    walletService.debit.mockResolvedValue({
      subAccount: { balance: 50 },
      transactionId: 'wallet-tx-1',
    });
    entityManager.create.mockReturnValue({ id: 'casino-tx-1' });
    entityManager.insert.mockResolvedValue(undefined);

    await service.create(mockCreateBetDto, mockAuth);

    expect(walletService.debit).toHaveBeenCalledWith(
      {
        amount: mockCreateBetDto.amount,
        reason: 'Bet',
        isBonusOperation: false,
        gameId: mockCreateBetDto.gameId,
        gameProvider: 'provider-test',
      },
      mockAuth.payload.partnerId,
      mockCreateBetDto.playerId,
    );
  });

  it('deve inserir transação no banco de dados', async () => {
    entityManager.findOne.mockResolvedValue({ gameProvider: 'provider-1' });
    walletService.debit.mockResolvedValue({
      subAccount: { balance: 50 },
      transactionId: 'wallet-tx-1',
    });
    const mockTransaction = { id: 'casino-tx-1' };
    entityManager.create.mockReturnValue(mockTransaction);
    entityManager.insert.mockResolvedValue(undefined);

    await service.create(mockCreateBetDto, mockAuth);

    expect(entityManager.insert).toHaveBeenCalledWith(
      expect.anything(),
      mockTransaction,
    );
  });

  it('deve usar RealMoney como requestType padrão quando não fornecido', async () => {
    const dtoWithoutRequestType = { ...mockCreateBetDto };
    delete dtoWithoutRequestType.requestType;

    entityManager.findOne.mockResolvedValue({ gameProvider: 'provider-1' });
    walletService.debit.mockResolvedValue({
      subAccount: { balance: 50 },
      transactionId: 'wallet-tx-1',
    });
    const mockTransaction = { id: 'casino-tx-1' };
    entityManager.create.mockReturnValue(mockTransaction);
    entityManager.insert.mockResolvedValue(undefined);

    await service.create(dtoWithoutRequestType, mockAuth);

    expect(entityManager.create).toHaveBeenCalledWith(
      expect.anything(),
      expect.objectContaining({
        requestType: 'RealMoney',
      }),
    );
  });
});
