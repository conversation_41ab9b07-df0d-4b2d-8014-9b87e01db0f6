import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  <PERSON><PERSON><PERSON><PERSON>umn,
  OneToOne,
  ManyToOne,
} from "typeorm";
import { ApiProperty } from "@nestjs/swagger";
import { Exclude } from "class-transformer";

@Entity("player_account_info", { schema: "player" })
export class AccountInfoEntity {
  @ApiProperty({
    description: "Identificador da conta",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: true,
  })
  @PrimaryGeneratedColumn("uuid", {
    name: "id",
  })
  id: string;

  @ApiProperty({
    description: "Identificador do jogador",
    type: "string",
    example: "761a1b06-b48f-4ffc-876e-9e80c6b7d59d",
    required: true,
  })
  @Column({
    name: "id_player",
    nullable: false,
  })
  playerId: string;

  //   @OneToOne(() => PlayerEntity, (player) => player.accountInfo)
  //   @JoinColumn({ name: "id_player" })
  //   player: PlayerEntity;

  @ApiProperty({
    description: "Identificador do parceiro",
    type: "string",
    example: "904ed994-0cc1-4f2f-8e59-fc9c4040b876",
    required: false,
  })
  @Column("uuid", {
    name: "id_partner",
    nullable: true,
  })
  partnerId: string | null;

  @ApiProperty({
    description: "Identificador externo",
    type: "string",
    example: "EXT123456",
    required: false,
  })
  @Column("varchar", {
    name: "id_external",
    length: 20,
    nullable: true,
  })
  externalId: string | null;

  @ApiProperty({
    description: "Indica o status da conta",
    type: "boolean",
    example: true,
    required: true,
  })
  @Column("boolean", {
    name: "status",
    nullable: true,
  })
  status: boolean;

  @ApiProperty({
    description: "Indica se a conta está verificada",
    type: "boolean",
    example: true,
    required: false,
  })
  @Column("boolean", {
    name: "is_verified",
    nullable: true,
  })
  isVerified: boolean | null;

  @ApiProperty({
    description: "Indica se o jogador está bloqueado para cassino",
    type: "boolean",
    example: false,
    required: false,
  })
  @Column("boolean", {
    name: "is_cassino_blocked",
    nullable: true,
  })
  isCassinoBlocked: boolean | null;

  @ApiProperty({
    description: "Indica se o jogador está bloqueado para apostas esportivas",
    type: "boolean",
    example: false,
    required: false,
  })
  @Column("boolean", {
    name: "is_sport_blocked",
    nullable: true,
  })
  isSportBlocked: boolean | null;

  @ApiProperty({
    description: "Indica se o jogador está bloqueado para RMT",
    type: "boolean",
    example: false,
    required: false,
  })
  @Column("boolean", {
    name: "is_rmt_blocked",
    nullable: true,
  })
  isRmtBlocked: boolean | null;

  @ApiProperty({
    description: "Indica se o jogador é um teste",
    type: "boolean",
    example: false,
    required: false,
  })
  @Column("boolean", {
    name: "is_test",
    nullable: true,
  })
  isTest: boolean | null;

  @ApiProperty({
    description: "Tag do jogador",
    type: "string",
    example: "123456",
    required: false,
  })
  @Column("varchar", {
    name: "btag",
    length: 20,
    nullable: true,
  })
  btag: string | null;

  @ApiProperty({
    description: "Indica se o jogador habilitou a autenticação de multifator",
    type: "boolean",
    example: true,
    required: false,
  })
  @Column("boolean", {
    name: "enable_multi_factor",
    nullable: true,
  })
  enableMultiFactor: boolean | null;

  @ApiProperty({
    description: "Indica se o jogador é uma pessoa politicamente exposta",
    type: "boolean",
    example: false,
    required: false,
  })
  @Column("boolean", {
    name: "is_pep",
    nullable: true,
  })
  isPep: boolean | null;

  @ApiProperty({
    description: "Data de criação",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: true,
  })
  @CreateDateColumn({
    name: "created_at",
    type: "timestamptz",
    nullable: false,
  })
  createdAt: Date;

  @ApiProperty({
    description: "Data de atualização",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: false,
  })
  @UpdateDateColumn({
    name: "updated_at",
    type: "timestamptz",
    nullable: true,
  })
  updatedAt: Date | null;

  @DeleteDateColumn({
    name: "deleted_at",
    type: "timestamptz",
    nullable: true,
  })
  @Exclude()
  deletedAt: Date | null;

  @Column("boolean", {
    name: "is_deleted",
    nullable: true,
  })
  @Exclude()
  isDeleted: boolean | null;

  @ApiProperty({
    description: "Identificador tipo exclusao",
    type: "string",
    example: "761a1b06-b48f-4ffc-876e-9e80c6b7d59d",
    required: false,
  })
  @Column({
    name: "id_type_exclusion",
    nullable: false,
  })
  exclusionTypeId: string;

  //   @ManyToOne(() => TypeExclusionEntity, { eager: false })
  //   @JoinColumn({ name: "id_type_exclusion" })
  //   exclusionType: TypeExclusionEntity;
}
