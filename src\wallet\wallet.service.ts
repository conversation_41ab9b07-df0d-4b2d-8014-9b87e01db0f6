import { HttpException, HttpStatus, Injectable, Logger } from "@nestjs/common";
import { OperationDto } from "./dto/operation.dto";
import { HttpService } from "@nestjs/axios";
import { firstValueFrom } from "rxjs";
import { TransactionResponse } from "./interfaces/debit";
import { BalanceResponse } from "./interfaces/balance";

@Injectable()
export class WalletService {
  constructor(private readonly httpService: HttpService) {}
  private urlWallet = process.env.API_WALLET;
  private readonly logger = new Logger(WalletService.name);
  public async getBalanceByPlayer(partnerId: string, playerId: string) {
    this.logger.log(`[Inicio] Fetching balance for player ${playerId}`);
    const response = await firstValueFrom(
      this.httpService.get<BalanceResponse>(
        `${this.urlWallet}/wallet/id/${partnerId}/${playerId}`
      )
    );
    this.logger.debug(
      `[Debug] Response for balance: ${JSON.stringify(response.data)}`
    );
    if (response && response.data !== undefined) {
      this.logger.debug(
        `[Debug] Inside if response undefined: ${JSON.stringify(response.data)}`
      );
      const realBalance = response.data.subAccounts.filter(
        (el) => el.accountType === "regular"
      )[0];
      this.logger.log(`[Fim] Fetching balance for player ${playerId}`);
      return realBalance;
    } else {
      this.logger.error(`Balance not found for player ${playerId}`);
      throw new HttpException(
        {
          errorCode: 1201,
          errorMsg: "Balance not found.",
        },
        HttpStatus.NOT_FOUND
      );
    }
  }

  public async debit(
    operation: OperationDto,
    partnerId: string,
    playerId: string
  ): Promise<TransactionResponse> {
    this.logger.log(`[Inicio] Debiting balance for player ${playerId}`);
    const newOperation = {
      partnerId: partnerId,
      playerId: playerId,
      accountType: "regular",
      amount: operation.amount,
      notes: operation.reason,
      origin: "casino",
      adjustmentType: operation.adjustmentType ?? "debit",
      gameId: operation.gameId,
      gameProvider: operation.gameProvider,
    };
    try {
      this.logger.debug(
        `Debiting balance for player ${JSON.stringify(newOperation)}`
      );
      const response = await firstValueFrom(
        this.httpService.post<TransactionResponse>(
          `${this.urlWallet}/wallet/transaction/withdrawal/by-partner-player`,
          newOperation
        )
      );
      this.logger.log(`[Fim] Debiting balance for player ${playerId}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Error debiting balance: ${error}`);
      throw new HttpException(
        {
          errorCode: error.response.data ? error.response.data.errorCode : 2,
          errorMsg: error.response.data ? error.response.data.errorMsg : error,
        },
        error.response.data
          ? error.response.data.errorCode
          : HttpStatus.SERVICE_UNAVAILABLE
      );
    }
  }

  public async credit(
    operation: OperationDto,
    partnerId: string,
    playerId: string
  ): Promise<TransactionResponse> {
    try {
      this.logger.log(`[Inicio] Credit balance for player ${playerId}`);
      const newOperation = {
        partnerId: partnerId,
        playerId: playerId,
        accountType: "regular",
        amount: operation.amount,
        notes: operation.reason,
        origin: "casino",
        adjustmentType: operation.adjustmentType ?? "credit",
        gameId: operation.gameId,
      };
      const response = await firstValueFrom(
        this.httpService.post<TransactionResponse>(
          `${this.urlWallet}/wallet/transaction/deposit/by-partner-player`,
          newOperation
        )
      );
      this.logger.log(`[Fim] Credit balance for player ${playerId}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Error crediting balance: ${error}`);

      // this.logger.error(error);
      if (error.response && error.response.data) {
        this.logger.error(JSON.stringify(error.response.data));
      } else {
        this.logger.error("wallet service não tem response.data");
      }

      throw new HttpException(
        {
          errorCode: error.response.data ? error.response.data.errorCode : 2,
          errorMsg: error.response.data ? error.response.data.errorMsg : error,
        },
        HttpStatus.SERVICE_UNAVAILABLE
      );
    }
  }

  public async limit(playerId: string, partnerId: string) {
    try {
      this.logger.log(`[Inicio] Fetching limit for player ${playerId}`);
      const response = await firstValueFrom(
        this.httpService.post(`${this.urlWallet}/wallet/bet-platform/limits`, {
          playerId: playerId,
          partnerId: partnerId,
        })
      );
      this.logger.log(`[Fim] Fetching limit for player ${playerId}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Error fetching limit: ${error}`);
      throw new HttpException(
        {
          errorCode: error.response.data ? error.response.data.errorCode : 2,
          errorMsg: error.response.data ? error.response.data.errorMsg : error,
        },
        HttpStatus.SERVICE_UNAVAILABLE
      );
    }
  }
}
