import { ApiProperty } from "@nestjs/swagger";
import { IsDateString, IsNotEmpty } from "class-validator";

export class DateRangeDto {
  @ApiProperty({
    description: "Start date",
    example: "2024-01-01",
    required: true
  })
  @IsDateString()
  @IsNotEmpty()
  from: string;

  @ApiProperty({
    description: "End date",
    example: "2024-01-31",
    required: true
  })
  @IsDateString()
  @IsNotEmpty()
  to: string;
} 