import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class CreateCasinoSessionDto {
  @ApiProperty({
    description: 'ID da sessão de atividade do jogador',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: true,
  })
  @IsString()
  playerSessionActivityId: string;

  @ApiProperty({
    description: 'Game ID',
    example: '-80373',
    required: true,
  })
  @IsString()
  gameId: string;
}
