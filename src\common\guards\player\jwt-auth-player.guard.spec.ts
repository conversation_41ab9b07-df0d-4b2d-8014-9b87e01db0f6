import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { JwtAuthPlayerGuard } from './jwt-auth-player.guard';

describe('JwtAuthPlayerGuard', () => {
  let guard: JwtAuthPlayerGuard;
  let jwtService: JwtService;
  let configService: ConfigService;

  const mockJwtService = {
    verify: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const createMockExecutionContext = (headers: any): ExecutionContext => {
    const mockRequest = {
      headers,
      player: null,
    };

    return {
      switchToHttp: () => ({
        getRequest: () => mockRequest,
      }),
    } as any;
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JwtAuthPlayerGuard,
        { provide: JwtService, useValue: mockJwtService },
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();

    guard = module.get<JwtAuthPlayerGuard>(JwtAuthPlayerGuard);
    jwtService = module.get<JwtService>(JwtService);
    configService = module.get<ConfigService>(ConfigService);
    jest.clearAllMocks();

    mockConfigService.get.mockReturnValue('test-player-secret');
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  describe('canActivate', () => {
    it('deve retornar true quando token é válido', async () => {
      const token = 'valid-player-token';
      const headers = {
        authorization: `Bearer ${token}`,
      };

      const mockPayload = {
        playerId: 'player-123',
        email: '<EMAIL>',
      };

      mockJwtService.verify.mockResolvedValue(mockPayload);

      const context = createMockExecutionContext(headers);
      const result = await guard.canActivate(context);

      expect(result).toBe(true);
      expect(jwtService.verify).toHaveBeenCalledWith(token, {
        secret: 'test-player-secret',
      });
    });

    it('deve popular request.player com payload do token', async () => {
      const token = 'valid-player-token';
      const headers = {
        authorization: `Bearer ${token}`,
      };

      const mockPayload = {
        playerId: 'player-456',
        email: '<EMAIL>',
        name: 'Test Player',
      };

      mockJwtService.verify.mockResolvedValue(mockPayload);

      const mockRequest = {
        headers,
        player: null,
      };

      const context = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
        }),
      } as ExecutionContext;

      await guard.canActivate(context);

      expect(mockRequest.player).toEqual(mockPayload);
    });

    it('deve lançar UnauthorizedException quando token não está presente', async () => {
      const headers = {};

      const context = createMockExecutionContext(headers);

      await expect(guard.canActivate(context)).rejects.toThrow(
        new UnauthorizedException('Token not provided'),
      );
    });

    it('deve lançar UnauthorizedException quando header Authorization está vazio', async () => {
      const headers = {
        authorization: '',
      };

      const context = createMockExecutionContext(headers);

      await expect(guard.canActivate(context)).rejects.toThrow(
        new UnauthorizedException('Token not provided'),
      );
    });

    it('deve lançar UnauthorizedException quando não é Bearer token', async () => {
      const headers = {
        authorization: 'Basic some-token',
      };

      const context = createMockExecutionContext(headers);

      await expect(guard.canActivate(context)).rejects.toThrow(
        new UnauthorizedException('Token not provided'),
      );
    });

    it('deve lançar UnauthorizedException quando token é inválido', async () => {
      const token = 'invalid-token';
      const headers = {
        authorization: `Bearer ${token}`,
      };

      mockJwtService.verify.mockRejectedValue(new Error('Invalid token'));

      const context = createMockExecutionContext(headers);

      await expect(guard.canActivate(context)).rejects.toThrow(
        new UnauthorizedException('Invalid token'),
      );
    });

    it('deve lançar UnauthorizedException quando token expirou', async () => {
      const token = 'expired-token';
      const headers = {
        authorization: `Bearer ${token}`,
      };

      mockJwtService.verify.mockRejectedValue(new Error('Token expired'));

      const context = createMockExecutionContext(headers);

      await expect(guard.canActivate(context)).rejects.toThrow(
        new UnauthorizedException('Invalid token'),
      );
    });

    it('deve usar JWT_SECRET_PLAYER do ConfigService', async () => {
      const token = 'test-token';
      const headers = {
        authorization: `Bearer ${token}`,
      };

      mockConfigService.get.mockReturnValue('custom-player-secret');
      mockJwtService.verify.mockResolvedValue({ playerId: 'player-123' });

      const context = createMockExecutionContext(headers);
      await guard.canActivate(context);

      expect(configService.get).toHaveBeenCalledWith('JWT_SECRET_PLAYER');
      expect(jwtService.verify).toHaveBeenCalledWith(token, {
        secret: 'custom-player-secret',
      });
    });

    it('deve extrair token corretamente com espaços', async () => {
      const token = 'token-with-spaces';
      const headers = {
        authorization: `Bearer ${token}`,
      };

      mockJwtService.verify.mockResolvedValue({ playerId: 'player-789' });

      const context = createMockExecutionContext(headers);
      await guard.canActivate(context);

      expect(jwtService.verify).toHaveBeenCalledWith(token, {
        secret: 'test-player-secret',
      });
    });
  });

  describe('verify', () => {
    it('deve verificar token com secret correto', async () => {
      const token = 'test-token';
      const mockPayload = { playerId: 'player-123' };

      mockConfigService.get.mockReturnValue('player-secret');
      mockJwtService.verify.mockResolvedValue(mockPayload);

      const result = await guard.verify(token);

      expect(result).toEqual(mockPayload);
      expect(jwtService.verify).toHaveBeenCalledWith(token, {
        secret: 'player-secret',
      });
    });

    it('deve lançar erro quando token é inválido', async () => {
      const token = 'invalid-token';

      mockJwtService.verify.mockRejectedValue(new Error('Invalid signature'));

      await expect(guard.verify(token)).rejects.toThrow('Invalid signature');
    });
  });
});
