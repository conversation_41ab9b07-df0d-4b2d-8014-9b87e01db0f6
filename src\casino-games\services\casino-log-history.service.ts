import { Injectable, Logger } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { CasinoLogHistory } from '../entities/casino-log-history.entity';

@Injectable()
export class CasinoLogHistoryService {
  private readonly logger = new Logger(CasinoLogHistoryService.name);

  constructor(
    @InjectEntityManager() private readonly casinoManager: EntityManager,
  ) {}

  /**
   * Cria logs de histórico comparando valores antigos e novos
   * @param actionType Tipo de ação (ex: 'CasinoGames', 'CasinoGameProvider', 'CasinoGamesCategories')
   * @param oldValues Objeto com valores antigos
   * @param newValues Objeto com valores novos
   * @param userEmail Email do usuário que fez a alteração
   */
  async createLogEntries(
    actionType: string,
    oldValues: Record<string, any>,
    newValues: Record<string, any>,
    userEmail: string,
  ): Promise<void> {
    try {
      const logs: Partial<CasinoLogHistory>[] = [];

      // Compara cada campo do objeto novo com o antigo
      for (const field in newValues) {
        // Ignora campos que não devem ser logados
        if (
          field === 'updatedAt' ||
          field === 'createdAt' ||
          field === 'lastUserModify'
        ) {
          continue;
        }

        const oldValue = oldValues[field];
        const newValue = newValues[field];

        // Se o valor foi alterado, cria um log
        if (oldValue !== newValue && newValue !== undefined) {
          logs.push({
            actionType,
            field,
            oldValue: oldValue?.toString() || null,
            newValue: newValue?.toString() || null,
            lastUserModify: userEmail,
          });
        }
      }

      // Insere todos os logs de uma vez
      if (logs.length > 0) {
        await this.casinoManager.insert(CasinoLogHistory, logs);
        this.logger.log(
          `Created ${logs.length} log entries for action: ${actionType}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error creating log entries for ${actionType}: ${error.message}`,
      );
      // Não lança erro para não interromper a operação principal
    }
  }

  /**
   * Cria um único log de histórico
   * @param actionType Tipo de ação
   * @param field Campo alterado
   * @param oldValue Valor antigo
   * @param newValue Valor novo
   * @param userEmail Email do usuário
   */
  async createSingleLog(
    actionType: string,
    field: string,
    oldValue: string | null,
    newValue: string | null,
    userEmail: string,
  ): Promise<void> {
    try {
      await this.casinoManager.insert(CasinoLogHistory, {
        actionType,
        field,
        oldValue,
        newValue,
        lastUserModify: userEmail,
      });

      this.logger.log(
        `Created single log entry: ${actionType} - ${field} changed from "${oldValue}" to "${newValue}"`,
      );
    } catch (error) {
      this.logger.error(`Error creating single log: ${error.message}`);
    }
  }
}
