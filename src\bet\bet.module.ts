import { Module } from '@nestjs/common';
import { BetService } from './bet.service';
import { BetController } from './bet.controller';
import { HMACModule } from '@/hmac/hmac.module';
import { DatabaseModule } from '@/common/database/database.module';
import { WalletModule } from '@/wallet/wallet.module';

@Module({
  imports: [DatabaseModule, HMACModule, WalletModule],
  controllers: [BetController],
  providers: [BetService],
})
export class BetModule {}
