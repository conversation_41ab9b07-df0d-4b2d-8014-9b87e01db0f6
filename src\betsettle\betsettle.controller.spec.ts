import { Test, TestingModule } from '@nestjs/testing';
import { BetSettleController } from './betsettle.controller';
import { BetSettleService } from './betsettle.service';
import { HMACGuard } from '@/common/guards/hmac.guard';

describe('BetSettleController', () => {
  let controller: BetSettleController;

  const mockBetSettleService = {};
  const mockHMACGuard = { canActivate: jest.fn(() => true) };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BetSettleController],
      providers: [
        { provide: BetSettleService, useValue: mockBetSettleService },
      ],
    })
      .overrideGuard(HMACGuard)
      .useValue(mockHMACGuard)
      .compile();

    controller = module.get<BetSettleController>(BetSettleController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
