import { Body, Controller, HttpCode, Post, UseGuards } from '@nestjs/common';
import { BetService } from './bet.service';
import { CreateBetDto } from './dto/create-bet.dto';
import { HMACGuard } from '@/common/guards/hmac.guard';
import { Jwt } from '@/common/interfaces/JWT';
import { Auth } from '@/common/decorators/auth.decorator';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import {
  ApiHmacHeader,
  ApiHmacUnauthorizedResponse,
} from '@/common/decorators/swagger.decorator';
import { SwaggerApiCodeResponses } from '@/common/decorators/swagger-api-code-responses';
import {
  SUCCESS,
  ALREADY_PROCESSED,
  GENERIC_ERROR,
  PLAYER_NOT_FOUND,
  SESSION_NOT_FOUND,
  GAME_NOT_FOUND,
  ROUND_NOT_FOUND,
  INSUFFICIENT_FUNDS,
  PLAYER_BLOCKED,
  TOTAL_LOSS_LIMIT,
  TOTAL_STAKE_LIMIT,
  MAX_STAKE_LIMIT,
  DAILY_TIME_LIMIT,
  WEEKLY_TIME_LIMIT,
  MONTHLY_TIME_LIMIT,
  SELF_EXCLUDED,
} from '@/common/constants/message-codes';
import {
  CreateBetResponseDto,
  CreateBetResponseDtoExample,
} from './dto/create-bet-response.dto';

@Controller('bet')
@ApiTags('Bet')
export class BetController {
  constructor(private readonly betService: BetService) {}

  @UseGuards(HMACGuard)
  @Post()
  @HttpCode(200)
  @ApiOperation({
    summary: 'Realizar aposta',
    description: 'Endpoint para realizar uma aposta em um jogo',
  })
  @ApiHmacHeader()
  @ApiHmacUnauthorizedResponse()
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
      INSUFFICIENT_FUNDS.code,
      PLAYER_BLOCKED.code,
      TOTAL_LOSS_LIMIT.code,
      TOTAL_STAKE_LIMIT.code,
      MAX_STAKE_LIMIT.code,
      DAILY_TIME_LIMIT.code,
      WEEKLY_TIME_LIMIT.code,
      MONTHLY_TIME_LIMIT.code,
      SELF_EXCLUDED.code,
    ],
    CreateBetResponseDto,
    CreateBetResponseDtoExample
  )
  async create(
    @Body() createBetDto: CreateBetDto,
    @Auth() auth: Jwt
  ): Promise<{
    balance?: number;
    errorCode: number;
    errorMsg?: string;
    casinoTransactionId?: string;
    reconcileAmount?: number;
  }> {
    return await this.betService.create(createBetDto, auth);
  }
}
