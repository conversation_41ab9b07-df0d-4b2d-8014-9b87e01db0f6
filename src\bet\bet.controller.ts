import {
  ALREADY_PROCESSED,
  DAILY_TIME_LIMIT,
  GAME_NOT_FOUND,
  GENERIC_ERROR,
  INSUFFICIENT_FUNDS,
  MAX_STAKE_LIMIT,
  MONTHLY_TIME_LIMIT,
  PLAYER_BLOCKED,
  PLAYER_NOT_FOUND,
  ROUND_NOT_FOUND,
  SELF_EXCLUDED,
  SESSION_NOT_FOUND,
  SUCCESS,
  TOTAL_LOSS_LIMIT,
  TOTAL_STAKE_LIMIT,
  WEEKLY_TIME_LIMIT,
} from '@/common/constants/message-codes';
import { Auth } from '@/common/decorators/auth.decorator';
import { SwaggerApiCodeResponses } from '@/common/decorators/swagger-api-code-responses';
import {
  ApiHmacHeader,
  ApiHmacUnauthorizedResponse,
} from '@/common/decorators/swagger.decorator';
import { Jwt } from '@/common/interfaces/JWT';
import { Body, Controller, HttpCode, Post } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { BetService } from './bet.service';
import {
  CreateBetResponseDto,
  CreateBetResponseDtoExample,
} from './dto/create-bet-response.dto';
import { CreateBetDto } from './dto/create-bet.dto';

@Controller('bet')
@ApiTags('Bet')
export class BetController {
  constructor(private readonly betService: BetService) {}

  //@UseGuards(HMACGuard)
  @Post()
  @HttpCode(200)
  @ApiOperation({
    summary: 'Realizar aposta',
    description: 'Endpoint para realizar uma aposta em um jogo',
  })
  @ApiHmacHeader()
  @ApiHmacUnauthorizedResponse()
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
      INSUFFICIENT_FUNDS.code,
      PLAYER_BLOCKED.code,
      TOTAL_LOSS_LIMIT.code,
      TOTAL_STAKE_LIMIT.code,
      MAX_STAKE_LIMIT.code,
      DAILY_TIME_LIMIT.code,
      WEEKLY_TIME_LIMIT.code,
      MONTHLY_TIME_LIMIT.code,
      SELF_EXCLUDED.code,
    ],
    CreateBetResponseDto,
    CreateBetResponseDtoExample,
  )
  async create(
    @Body() createBetDto: CreateBetDto,
    @Auth() auth: Jwt,
  ): Promise<{
    balance?: number;
    casinoTransactionId?: string;
    reconcileAmount?: number;
  }> {
    return await this.betService.create(createBetDto, auth);
  }
}
