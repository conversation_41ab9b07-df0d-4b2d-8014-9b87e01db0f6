stages:
  - analyze-and-report
  - deploy-image
  - update-gitops

variables:
  SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
  GIT_DEPTH: "0"

#  Unimos os dois jobs do Sonar em um só para mais eficiência
sonar-analysis:
  stage: analyze-and-report
  image: node:18-bullseye
  cache:
    policy: pull-push
    key: "sonar-cache-$CI_COMMIT_REF_SLUG"
    paths:
      - ".sonar/cache"
      - node_modules/

  script:
    # Instala o scanner via npm globalmente
    - npm install -g sonarqube-scanner

    # Instala dependências do projeto
    - npm install

    # Rodar testes e gerar cobertura
    - npm test -- --coverage

    # Rodar análise Sonar
    - sonar-scanner \
        -Dsonar.host.url="${SONAR_HOST_URL}" \
        -Dsonar.projectKey="${********************}" \
        -Dsonar.qualitygate.wait=true \
        -Dsonar.qualitygate.timeout=300 \
        -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info

    # Criar artefato vazio para garantir que sempre exista
    - touch gl-sast-sonar-report.json

    # Gerar relatório SAST somente em merge requests
    - |
      if [ "$CI_PIPELINE_SOURCE" == "merge_request_event" ]; then
        curl -u "${SONAR_TOKEN}:" "${SONAR_HOST_URL}/api/issues/gitlab_sast_export?projectKey=${********************}&branch=${CI_COMMIT_BRANCH}&pullRequest=${CI_MERGE_REQUEST_IID}" -o gl-sast-sonar-report.json
      fi

  artifacts:
    expire_in: 1 day
    when: always
    reports:
      sast: gl-sast-sonar-report.json
  allow_failure: true
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" || $CI_COMMIT_BRANCH == "development" || $CI_COMMIT_BRANCH == "staging" || $CI_COMMIT_BRANCH == "production"'
      when: always
    
deploy-image:
  stage: deploy-image
  environment: $CI_COMMIT_REF_NAME
  tags:
    - h2
  image: public.ecr.aws/debian/debian:bullseye-slim
  only:
    refs:
      - development
      - qa
      - staging
      - production
  before_script:
    - apt-get update -qq
    - apt-get install -y awscli docker.io curl
    - mkdir -vp ~/.docker/cli-plugins/ ~/dockercache
    - curl --silent -L "https://github.com/docker/buildx/releases/download/v0.13.1/buildx-v0.13.1.linux-arm64" > ~/.docker/cli-plugins/docker-buildx
    - chmod a+x ~/.docker/cli-plugins/docker-buildx
  script:
    - source /root/.bashrc
    - docker buildx create --use
    - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $ECR_URI_BASE
    - docker buildx build --push --platform linux/arm64 -f ./Dockerfile -t $ECR_URI_BASE/$CI_COMMIT_REF_NAME/pam/api-casino:$CI_COMMIT_SHA .
    - echo "Build e publicação ok!"

update-gitops:
  stage: update-gitops
  tags:
    - h2
  environment: $CI_COMMIT_REF_NAME
  image: public.ecr.aws/debian/debian:bullseye-slim
  needs: [deploy-image]
  only:
    refs:
      - development
      - qa
      - staging
      - production
  script:
    - apt-get update
    - apt-get install git wget -y
    - wget https://github.com/mikefarah/yq/releases/latest/download/yq_linux_arm64 -O /usr/bin/yq && chmod +x /usr/bin/yq
    - echo "Clonando o repositório..."
    - git clone https://${GITOPS_REPOSITORY_USER}:${GITOPS_REPOSITORY_ACCESS_TOKEN}@gitlab.opah.com.br/h2/pam/gitops/template-pam-casino-api.git template-pam-casino-api
    - cd template-pam-casino-api
    - git checkout $CI_COMMIT_REF_NAME
    - yq e ".spec.template.spec.containers[0].image = \"${ECR_URI_BASE}/${CI_COMMIT_REF_NAME}/pam/api-casino:${CI_COMMIT_SHA}\"" -i deployment.yml
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "Administrator"
    - git add .
    - git commit -m "Atualizando imagem docker"
    - echo "Fazendo push das alterações..."
    - git push https://${GITOPS_REPOSITORY_USER}:${GITOPS_REPOSITORY_ACCESS_TOKEN}@gitlab.opah.com.br/h2/pam/gitops/template-pam-casino-api.git $CI_COMMIT_REF_NAME