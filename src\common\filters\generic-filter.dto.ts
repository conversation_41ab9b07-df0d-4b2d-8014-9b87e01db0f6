import { Transform } from 'class-transformer';
import { IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';
import { SortOrder } from './sortOrder';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class GenericFilter {
  @ApiPropertyOptional({ type: Number })
  @Transform(({ value }) => Number(value))
  @IsNumber({}, { message: ' "page" atrribute should be a number' })
  public page: number;

  @ApiPropertyOptional({ type: Number })
  @Transform(({ value }) => Number(value))
  @IsNumber({}, { message: ' "pageSize" attribute should be a number ' })
  public pageSize: number;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  public orderBy?: string;

  @ApiPropertyOptional({ enum: SortOrder })
  @IsOptional()
  public sortOrder?: SortOrder = SortOrder.DESC;

  @ApiPropertyOptional({ type: String })
  @IsString()
  @IsOptional()
  partners: string;
}
