import { HttpService } from '@nestjs/axios';
import { HttpException, Injectable, Logger } from '@nestjs/common';
import { ResponseFinishFreespinDto } from './dto/response-finish-freespin.dto';
import { FreeSpinsFinishDto } from './dto/finish-freespin.dto';
import { firstValueFrom } from 'rxjs';
import { CancelFreeSpinDto } from './dto/cancel-freespin.dto';
import { IssueFreeSpinDto } from './dto/issue-freespin.dto';

@Injectable()
export class FreespinService {
  private readonly logger = new Logger(FreespinService.name);
  constructor(private readonly httpService: HttpService) {}
  private urlGateway = process.env.GATEWAY_URL;

  async finish(
    finishFreespinDto: FreeSpinsFinishDto,
  ): Promise<ResponseFinishFreespinDto> {
    try {
      this.logger.log('FreespinService', JSON.stringify(finishFreespinDto));
      return { balance: '0.01' };
    } catch (e) {
      this.logger.error('Error to finish freespin', e);
      throw new HttpException(e.message, e.status);
    }
  }

  async issue(createFreespinDto: IssueFreeSpinDto) {
    try {
      this.logger.log('FreespinService', JSON.stringify(createFreespinDto));
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.urlGateway}/casino/freespin/issue`,
          createFreespinDto,
          {
            headers: {
              'Content-Type': 'application/json',
              'x-aggregator': 'softswiss',
            },
          },
        ),
      );
      return response.data;
    } catch (e) {
      this.logger.error('Error to issue freespin', e);
      throw new HttpException(e.message, e.status);
    }
  }

  async cancel(cancelFreespinDto: CancelFreeSpinDto) {
    try {
      this.logger.log('FreespinService', JSON.stringify(cancelFreespinDto));
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.urlGateway}/casino/freespin/cancel`,
          cancelFreespinDto,
          {
            headers: {
              'Content-Type': 'application/json',
              'x-aggregator': 'softswiss',
            },
          },
        ),
      );
      return response.data;
    } catch (e) {
      this.logger.error('Error to cancel freespin', e);
      throw new HttpException(e.message, e.status);
    }
  }
}
