import { Test, TestingModule } from '@nestjs/testing';
import { PartnerService } from './partner.service';
import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';

jest.mock('rxjs', () => ({
  ...jest.requireActual('rxjs'),
  firstValueFrom: jest.fn(),
}));

describe('PartnerService', () => {
  let service: PartnerService;
  let httpService: HttpService;

  const mockHttpService = {
    get: jest.fn(),
  };

  const mockRequest = {
    headers: {
      authorization: 'Bearer test-token',
      'x-user-context': 'user-context-token',
    },
  };

  beforeEach(async () => {
    // Mock environment variable BEFORE creating the module
    process.env.API_PARTNER = 'http://api-partner.test';

    (firstValueFrom as jest.Mock).mockReset();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PartnerService,
        { provide: HttpService, useValue: mockHttpService },
      ],
    }).compile();

    service = module.get<PartnerService>(PartnerService);
    httpService = module.get<HttpService>(HttpService);
    jest.clearAllMocks();
  });

  afterEach(() => {
    delete process.env.API_PARTNER;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getPartnerById', () => {
    it('deve retornar informações do partner com sucesso', async () => {
      const partnerId = 'partner-123';
      const mockPartnerData = {
        id: partnerId,
        name: 'Test Partner',
        settings: {
          currency: 'BRL',
        },
      };

      const mockResponse = {
        data: mockPartnerData,
      };

      (firstValueFrom as jest.Mock).mockResolvedValue(mockResponse);

      const result = await service.getPartnerById(partnerId, mockRequest);

      expect(result).toEqual(mockPartnerData);
      expect(httpService.get).toHaveBeenCalledWith(
        'http://api-partner.test/v1/pam/partner-settings/general',
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer test-token',
            'x-user-context': 'user-context-token',
            'partner-id': partnerId,
          },
        },
      );
      expect(firstValueFrom).toHaveBeenCalled();
    });

    it('deve lançar HttpException quando partner não encontrado (data undefined)', async () => {
      const partnerId = 'invalid-partner';
      const mockResponse = {
        data: undefined,
      };

      (firstValueFrom as jest.Mock).mockResolvedValue(mockResponse);

      await expect(
        service.getPartnerById(partnerId, mockRequest),
      ).rejects.toThrow(
        new HttpException('Partner not found', HttpStatus.NOT_FOUND),
      );
    });

    it('deve lançar HttpException quando response não tem data', async () => {
      const partnerId = 'partner-456';
      const mockResponse = {};

      (firstValueFrom as jest.Mock).mockResolvedValue(mockResponse);

      await expect(
        service.getPartnerById(partnerId, mockRequest),
      ).rejects.toThrow(
        new HttpException('Partner not found', HttpStatus.NOT_FOUND),
      );
    });

    it('deve propagar erro quando API externa falha', async () => {
      const partnerId = 'partner-789';
      const error = new Error('External API error');

      (firstValueFrom as jest.Mock).mockRejectedValue(error);

      await expect(
        service.getPartnerById(partnerId, mockRequest),
      ).rejects.toThrow('External API error');
    });

    it('deve lançar erro quando ocorrer erro de rede', async () => {
      const partnerId = 'partner-999';
      const networkError = new Error('Network timeout');

      (firstValueFrom as jest.Mock).mockRejectedValue(networkError);

      await expect(
        service.getPartnerById(partnerId, mockRequest),
      ).rejects.toThrow('Network timeout');
    });

    it('deve incluir partner-id correto no header', async () => {
      const partnerId = 'partner-abc';
      const mockResponse = {
        data: { id: partnerId, name: 'Partner ABC' },
      };

      (firstValueFrom as jest.Mock).mockResolvedValue(mockResponse);

      await service.getPartnerById(partnerId, mockRequest);

      expect(httpService.get).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'partner-id': partnerId,
          }),
        }),
      );
    });

    it('deve usar headers customizados da requisição', async () => {
      const partnerId = 'partner-custom';
      const customRequest = {
        headers: {
          authorization: 'Bearer custom-token',
          'x-user-context': 'custom-context',
        },
      };

      const mockResponse = {
        data: { id: partnerId, name: 'Custom Partner' },
      };

      (firstValueFrom as jest.Mock).mockResolvedValue(mockResponse);

      await service.getPartnerById(partnerId, customRequest);

      expect(httpService.get).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer custom-token',
            'x-user-context': 'custom-context',
            'partner-id': partnerId,
          },
        }),
      );
    });

    it('deve retornar dados completos do partner quando response é válida', async () => {
      const partnerId = 'partner-full';
      const fullPartnerData = {
        id: partnerId,
        name: 'Full Partner',
        settings: {
          currency: 'USD',
          timezone: 'UTC',
          features: ['casino', 'sports'],
        },
        status: 'active',
      };

      const mockResponse = {
        data: fullPartnerData,
      };

      (firstValueFrom as jest.Mock).mockResolvedValue(mockResponse);

      const result = await service.getPartnerById(partnerId, mockRequest);

      expect(result).toEqual(fullPartnerData);
      expect(result.id).toBe(partnerId);
      expect(result.name).toBe('Full Partner');
      expect(result.settings).toBeDefined();
      expect(result.status).toBe('active');
    });
  });
});
