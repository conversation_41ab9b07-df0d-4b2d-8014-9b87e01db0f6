import { HttpException, Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ResponseFinishBonusDto } from './dto/response-finish-bonus.dto';
import { firstValueFrom } from 'rxjs';
import { CancelBonusDto } from './dto/cancel-bonus.dto';
import { FinishBonusDto } from './dto/finish-bonus.dto';
import { IssueDto } from './dto/issue-bonus.dto';
import { TypesDto } from './dto/type-bonus.dto';

@Injectable()
export class BonusService {
  private readonly logger = new Logger(BonusService.name);
  constructor(private readonly httpService: HttpService) {}
  private urlGateway = process.env.GATEWAY_URL;

  async finish(
    finishBonusDto: FinishBonusDto,
  ): Promise<ResponseFinishBonusDto> {
    try {
      this.logger.log('BonusService', JSON.stringify(finishBonusDto));
      return { balance: '0.01' };
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(error.message, error.status);
    }
  }
  async cancel(cancelBonusDto: CancelBonusDto) {
    try {
      this.logger.log('BonusService', JSON.stringify(cancelBonusDto));
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.urlGateway}/casino/bonus/cancel`,
          cancelBonusDto,
          {
            headers: {
              'Content-Type': 'application/json',
              'x-aggregator': 'softswiss',
            },
          },
        ),
      );
      return response.data;
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(error.message, error.status);
    }
  }

  async issue(issueBonusDto: IssueDto) {
    try {
      this.logger.log('BonusService', JSON.stringify(issueBonusDto));
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.urlGateway}/casino/bonus/issue`,
          issueBonusDto,
          {
            headers: {
              'Content-Type': 'application/json',
              'x-aggregator': 'softswiss',
            },
          },
        ),
      );
      return response.data;
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(error.message, error.status);
    }
  }

  async types(typeBonusDto: TypesDto) {
    try {
      this.logger.log('BonusService', JSON.stringify(typeBonusDto));
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.urlGateway}/casino/bonus/types`,
          typeBonusDto,
          {
            headers: {
              'Content-Type': 'application/json',
              'x-aggregator': 'softswiss',
            },
          },
        ),
      );
      return response.data;
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(error.message, error.status);
    }
  }
}
