import {
  <PERSON>,
  Get,
  Param,
  Query,
  Req,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { DailyReportService } from '../services/daily-report.service';
import { GenericFilter } from '@/common/filters/generic-filter.dto';
import { GameProfitabilityFilterDto } from './dto/game-profitability-filter.dto';
import { GameProfitabilityResponseDto } from './dto/game-profitability-response.dto';
import { BackofficeGuard } from '@/common/guards/backoffice/backoffice.guard';
import { 
  ApiForbiddenResponse, 
  ApiInternalServerErrorResponse, 
  ApiPaginatedResponse,
  ApiPartnerIdHeader,
  ApiDateRangeQuery,
  ApiPaginationQuery,  
} from '@/common/decorators/swagger.decorator';
import {
  DailyReportGameFilterDto,
  DailyReportGamePlayerPartnerFilterDto,
} from './dto/daily-report-filter.dto';
import { DailyReportGameResponseDto } from './dto/daily-report-game-response.dto';
import { DailyReportGamePlayerPartnerResponseDto } from './dto/daily-report-game-player-partner-response.dto';
import { DailyReportPartnerResponseDto } from './dto/daily-report-partner-response.dto';
import { DailyReportPlayerResponseDto } from './dto/daily-report-player-response.dto';
import { DailyReportProvidersResponseDto } from './dto/daily-report-providers-response.dto';

@ApiTags('DailyReport')
@ApiPartnerIdHeader()
@ApiForbiddenResponse()
@ApiInternalServerErrorResponse()
@Controller('daily-report')
export class DailyReportController {
  constructor(private readonly dailyReportService: DailyReportService) {}

  @Get('cronJob')
  @ApiOperation({
    summary: 'Executar relatório diário',
    description: 'Endpoint para forçar a execução do relatório diário'
  })
  @ApiResponse({
    status: 200,
    description: 'Relatório diário executado com sucesso',
    schema: {
      type: 'string',
      example: 'Cron job run successfully'
    }
  })
  async forceDailyReport() {
    await this.dailyReportService.saveDailyReportGame();
    await this.dailyReportService.saveDailyReportGamePlayerPartner();
    await this.dailyReportService.saveDailyReportPlayer();
    await this.dailyReportService.saveDailyReportProviders();
    await this.dailyReportService.saveDailyReportPartner();
    return 'Cron job run successfully';
  }  

  @Get('game')
  @ApiOperation({
    summary: 'Listar relatório de jogos',
    description: 'Endpoint para listar o relatório diário de jogos com paginação'
  })
  @ApiPaginatedResponse('Lista de relatórios de jogos retornada com sucesso', DailyReportGameResponseDto)
  @ApiDateRangeQuery()
  @ApiPaginationQuery()
  @ApiQuery({ name: 'gameId', required: false, description: 'ID do jogo' })
  @ApiQuery({ name: 'provider', required: false, description: 'Provedor do jogo' })
  @ApiQuery({ name: 'accountType', required: false, description: 'Tipo de conta' })
  @UseGuards(BackofficeGuard)
  @ApiBearerAuth('access-token')
  async getDailyReportGamePagination(
    @Query()
    genericFilter: DailyReportGameFilterDto,
    @Req() req
  ) {
    return await this.dailyReportService.getDailyReportGamePagination(
      genericFilter,
      req
    );
  }

  @Get('game-all')
  @ApiOperation({
    summary: 'Listar todos os relatórios de jogos',
    description: 'Endpoint para listar todos os relatórios diários de jogos sem paginação'
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de relatórios de jogos retornada com sucesso',
    type: [DailyReportGameResponseDto]
  })
  @ApiDateRangeQuery()
  @UseGuards(BackofficeGuard)
  @ApiBearerAuth('access-token')
  async getDailyReportGame(
    @Query('fromDate') fromDate: string,
    @Query('toDate') toDate: string,
    @Req() req
  ) {
    return await this.dailyReportService.getDailyReportGame(
      fromDate,
      toDate,
      req
    );
  }

  @Get('game-player-partner')
  @ApiOperation({
    summary: 'Listar relatório de jogos por jogador e parceiro',
    description: 'Endpoint para listar o relatório diário de jogos por jogador e parceiro com paginação'
  })
  @ApiPaginatedResponse('Lista de relatórios retornada com sucesso', DailyReportGamePlayerPartnerResponseDto)
  @ApiDateRangeQuery()
  @ApiPaginationQuery()
  @ApiQuery({ name: 'gameId', required: false, description: 'ID do jogo' })
  @ApiQuery({ name: 'provider', required: false, description: 'Provedor do jogo' })
  @ApiQuery({ name: 'playerId', required: false, description: 'ID do jogador' })
  @UseGuards(BackofficeGuard)
  @ApiBearerAuth('access-token')
  async getDailyReportGamePlayerPartnerPagination(
    @Query() genericFilter: DailyReportGamePlayerPartnerFilterDto,
    @Req() req
  ) {
    return await this.dailyReportService.getDailyReportGamePlayerPartnerPagination(
      genericFilter,
      req
    );
  }

  @Get('game-player-partner-all')
  @ApiOperation({
    summary: 'Listar todos os relatórios de jogos por jogador e parceiro',
    description: 'Endpoint para listar todos os relatórios diários de jogos por jogador e parceiro sem paginação'
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de relatórios retornada com sucesso',
    type: [DailyReportGamePlayerPartnerResponseDto]
  })
  @ApiDateRangeQuery()
  @UseGuards(BackofficeGuard)
  @ApiBearerAuth('access-token')
  async getDailyReportGamePlayerPartner(
    @Query('fromDate') fromDate: string,
    @Query('toDate') toDate: string,
    @Req() req
  ) {
    return await this.dailyReportService.getDailyReportGamePlayerPartner(
      fromDate,
      toDate,
      req
    );
  }

  @Get(':playerId/by-player')
  @ApiOperation({
    summary: 'Buscar relatório por jogador',
    description: 'Endpoint para buscar o relatório diário de um jogador específico'
  })
  @ApiResponse({
    status: 200,
    description: 'Relatório do jogador retornado com sucesso',
    type: DailyReportPlayerResponseDto
  })
  @UseGuards(BackofficeGuard)
  @ApiBearerAuth('access-token')
  async getDailyReportPlayerById(
    @Param('playerId') playerId: string,
    @Req() req
  ) {
    return await this.dailyReportService.getDailyReportPlayerById(
      playerId,
      req
    );
  }

  @Get('player')
  @ApiOperation({
    summary: 'Listar relatório de jogadores',
    description: 'Endpoint para listar o relatório diário de jogadores com paginação'
  })
  @ApiPaginatedResponse('Lista de relatórios de jogadores retornada com sucesso', DailyReportPlayerResponseDto)  
  @ApiQuery({ name: 'playerId', required: false })
  @ApiQuery({ name: 'email', required: false })
  @ApiDateRangeQuery()
  @UseGuards(BackofficeGuard)
  @ApiBearerAuth('access-token')
  async getDailyReportPlayer(
    @Query('fromDate') fromDate: string,
    @Query('toDate') toDate: string,
    @Query() genericFilter: GenericFilter,
    @Req() req,
    @Query('playerId') playerId?: string,
    @Query('email') email?: string
  ) {
    return await this.dailyReportService.getDailyReportPlayer(
      fromDate,
      toDate,
      genericFilter,
      req,
      playerId,
      email
    );
  }

  @Get('providers-all')
  @ApiOperation({
    summary: 'Listar todos os relatórios de provedores',
    description: 'Endpoint para listar todos os relatórios diários de provedores sem paginação'
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de relatórios de provedores retornada com sucesso',
    type: [DailyReportProvidersResponseDto]
  })
  @ApiDateRangeQuery()
  @UseGuards(BackofficeGuard)
  @ApiBearerAuth('access-token')
  async getDailyReportProviders(
    @Query('fromDate') fromDate: string,
    @Query('toDate') toDate: string,
    @Req() req
  ) {
    return await this.dailyReportService.getDailyReportProviders(
      fromDate,
      toDate,
      req
    );
  }

  @Get('providers')
  @ApiOperation({
    summary: 'Listar relatório de provedores',
    description: 'Endpoint para listar o relatório diário de provedores com paginação'
  })
  @ApiPaginatedResponse('Lista de relatórios de provedores retornada com sucesso', DailyReportProvidersResponseDto)
  @ApiDateRangeQuery()
  @UseGuards(BackofficeGuard)
  @ApiBearerAuth('access-token')
  async getDailyReportProvidersPagination(
    @Query('fromDate') fromDate: string,
    @Query('toDate') toDate: string,
    @Query() genericFilter: GenericFilter,
    @Req() req
  ) {
    return await this.dailyReportService.getDailyReportProvidersPagination(
      fromDate,
      toDate,
      genericFilter,
      req
    );
  }

  @Get('partner-all')
  @ApiOperation({
    summary: 'Listar todos os relatórios de parceiros',
    description: 'Endpoint para listar todos os relatórios diários de parceiros sem paginação'
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de relatórios de parceiros retornada com sucesso',
    type: [DailyReportPartnerResponseDto]
  })
  @ApiDateRangeQuery()
  @UseGuards(BackofficeGuard)
  @ApiBearerAuth('access-token')
  async getDailyReportPartner(
    @Query('fromDate') fromDate: string,
    @Query('toDate') toDate: string,
    @Req() req
  ) {
    return await this.dailyReportService.getDailyReportPartner(
      fromDate,
      toDate,
      req
    );
  }

  @Get('partner')
  @ApiOperation({
    summary: 'Listar relatório de parceiros',
    description: 'Endpoint para listar o relatório diário de parceiros com paginação'
  })
  @ApiPaginatedResponse('Lista de relatórios de parceiros retornada com sucesso', DailyReportPartnerResponseDto)
  @ApiDateRangeQuery()
  @UseGuards(BackofficeGuard)
  @ApiBearerAuth('access-token')
  async getDailyReportPartnerPagination(
    @Query('fromDate') fromDate: string,
    @Query('toDate') toDate: string,
    @Query() genericFilter: GenericFilter,
    @Req() req
  ) {
    return await this.dailyReportService.getDailyReportPartnerPagination(
      fromDate,
      toDate,
      genericFilter,
      req
    );
  }

  @Get('game/profitability')
  @UsePipes(new ValidationPipe({ transform: true }))
  @ApiOperation({
    summary: 'Buscar lucratividade dos jogos',
    description: 'Endpoint para buscar o relatório de lucratividade dos jogos'
  })
  @UseGuards(BackofficeGuard)
  @ApiBearerAuth('access-token')
  @ApiResponse({
    status: 200,
    description: 'Relatório de lucratividade retornado com sucesso',
    type: [GameProfitabilityResponseDto]
  })
  @ApiQuery({
    name: 'dateRange[from]',
    type: 'string',
    required: true,
    description: 'Data inicial (YYYY-MM-DD)',
    example: '2024-01-01',
  })
  @ApiQuery({
    name: 'dateRange[to]',
    type: 'string',
    required: true,
    description: 'Data final (YYYY-MM-DD)',
    example: '2024-01-31',
  })
  async getGameProfitability(
    @Query() filter: GameProfitabilityFilterDto,
    @Req() req
  ) {
    return await this.dailyReportService.findGameProfitability(
      filter,
      req.headers['partner-id']
    );
  }
}
