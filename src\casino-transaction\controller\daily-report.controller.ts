import {
  ApiDateRangeQuery,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiPaginatedResponse,
  ApiPaginationQuery,
  ApiPartnerIdHeader,
} from '@/common/decorators/swagger.decorator';
import { GenericFilter } from '@/common/filters/generic-filter.dto';
import { KeycloakBackofficeGuard } from '@/common/guards/keycloak/keycloak-backoffice.guard';
import { DateUtilsService } from '@/common/utils/date';
import {
  BadRequestException,
  Controller,
  Get,
  Logger,
  Param,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { startOfDay, subDays } from 'date-fns';
import { DailyReportService } from '../services/daily-report.service';
import { BetReportFilterDto } from './dto/bet-report-filter.dto';
import { BetReportResponseDto } from './dto/bet-report-response.dto';
import {
  DailyReportGameFilterDto,
  DailyReportGamePlayerPartnerFilterDto,
} from './dto/daily-report-filter.dto';
import { DailyReportGamePlayerPartnerResponseDto } from './dto/daily-report-game-player-partner-response.dto';
import { DailyReportGameResponseDto } from './dto/daily-report-game-response.dto';
import { DailyReportPlayerResponseDto } from './dto/daily-report-player-response.dto';
import { DailyReportProvidersResponseDto } from './dto/daily-report-providers-response.dto';
import { GameProfitabilityFilterDto } from './dto/game-profitability-filter.dto';
import { GameProfitabilityResponseDto } from './dto/game-profitability-response.dto';

@ApiTags('DailyReport')
@ApiPartnerIdHeader()
@ApiForbiddenResponse()
@ApiInternalServerErrorResponse()
@Controller('daily-report')
export class DailyReportController {
  private readonly logger = new Logger(DailyReportController.name);
  constructor(
    private readonly dailyReportService: DailyReportService,
    private readonly dateService: DateUtilsService,
  ) {}

  @Get('cronJob')
  @ApiOperation({
    summary: 'Executar relatório diário',
    description: 'Endpoint para forçar a execução do relatório diário',
  })
  @ApiResponse({
    status: 200,
    description: 'Relatório diário executado com sucesso',
    schema: {
      type: 'string',
      example: 'Cron job run successfully',
    },
  })
  async DailyReport() {
    const yesterday = subDays(new Date(), 1);
    const { fromDate, toDate } = this.dateService.getDateRange(
      yesterday.toISOString(),
      yesterday.toISOString(),
    );

    await Promise.all([
      this.dailyReportService.saveDailyReportGame(fromDate, toDate),
      this.dailyReportService.saveDailyReportGamePlayerPartner(
        fromDate,
        toDate,
      ),
      this.dailyReportService.saveDailyReportPlayer(fromDate, toDate),
      this.dailyReportService.saveDailyReportProviders(fromDate, toDate),
    ]);

    this.logger.log('Daily report cron job executed successfully');

    return 'Cron job run successfully';
  }

  @Get('reprocess-daily-report')
  @ApiOperation({
    summary: 'Executar relatório diário',
    description: 'Endpoint para forçar a execução do relatório diário',
  })
  @ApiResponse({
    status: 200,
    description: 'Relatório diário executado com sucesso',
    schema: {
      type: 'string',
      example: 'Cron job run successfully',
    },
  })
  async reprocessDailyReport(@Query('date') date: string) {
    const { fromDate, toDate } = this.dateService.getDateRange(date, date);

    if (!fromDate || !toDate) {
      throw new BadRequestException('Invalid date');
    }

    await Promise.all([
      this.dailyReportService.saveDailyReportGame(fromDate, toDate),
      this.dailyReportService.saveDailyReportGamePlayerPartner(
        fromDate,
        toDate,
      ),
      this.dailyReportService.saveDailyReportPlayer(fromDate, toDate),
      this.dailyReportService.saveDailyReportProviders(fromDate, toDate),
    ]);

    this.logger.log('Daily report cron job executed successfully');

    return 'Cron job run successfully';
  }

  @Get('reprocess-early-daily-report')
  @ApiOperation({
    summary: 'Executar relatório diário',
    description: 'Endpoint para forçar a execução do relatório diário',
  })
  @ApiResponse({
    status: 200,
    description: 'Relatório diário executado com sucesso',
    schema: {
      type: 'string',
      example: 'Cron job run successfully',
    },
  })
  async reprocessEarlyDailyReport(@Query('dayPast') dayPast: number) {
    const date = subDays(new Date(), dayPast);
    const xxx = startOfDay(date);
    const dateFormatted = date.toISOString();
    const { fromDate, toDate } = this.dateService.getDateRange(
      dateFormatted,
      dateFormatted,
    );

    if (!fromDate || !toDate) {
      throw new BadRequestException('Invalid date');
    }

    await Promise.all([
      this.dailyReportService.saveDailyReportGame(fromDate, toDate),
      this.dailyReportService.saveDailyReportGamePlayerPartner(
        fromDate,
        toDate,
      ),
      this.dailyReportService.saveDailyReportPlayer(fromDate, toDate),
      this.dailyReportService.saveDailyReportProviders(fromDate, toDate),
    ]);

    this.logger.log('Daily report cron job executed successfully');

    return 'Cron job run successfully';
  }

  @Get('game')
  @ApiOperation({
    summary: 'Listar relatório de jogos',
    description:
      'Endpoint para listar o relatório diário de jogos com paginação',
  })
  @ApiPaginatedResponse(
    'Lista de relatórios de jogos retornada com sucesso',
    DailyReportGameResponseDto,
  )
  @ApiDateRangeQuery()
  @ApiPaginationQuery()
  @ApiQuery({ name: 'gameId', required: false, description: 'ID do jogo' })
  @ApiQuery({
    name: 'provider',
    required: false,
    description: 'Provedor do jogo',
  })
  @ApiQuery({
    name: 'gameProviderId',
    required: false,
    description: 'ID do provedor do jogo',
  })
  @ApiQuery({
    name: 'accountType',
    required: false,
    description: 'Tipo de conta',
  })
  @UseGuards(KeycloakBackofficeGuard)
  @ApiBearerAuth('access-token')
  async getDailyReportGamePagination(
    @Query()
    genericFilter: DailyReportGameFilterDto,
    @Req() req,
  ) {
    this.logger.log(
      `[GET] /daily-report/game called with filter=${JSON.stringify(
        genericFilter,
      )}`,
    );
    return await this.dailyReportService.getDailyReportGamePagination(
      genericFilter,
      req,
    );
  }

  @Get('game-all')
  @ApiOperation({
    summary: 'Listar todos os relatórios de jogos',
    description:
      'Endpoint para listar todos os relatórios diários de jogos sem paginação',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de relatórios de jogos retornada com sucesso',
    type: [DailyReportGameResponseDto],
  })
  @ApiDateRangeQuery()
  @UseGuards(KeycloakBackofficeGuard)
  @ApiBearerAuth('access-token')
  async getDailyReportGame(
    @Query('fromDate') fromDate: string,
    @Query('toDate') toDate: string,
    @Req() req,
  ) {
    this.logger.log(
      `[GET] /daily-report/game-all called with fromDate=${fromDate} and toDate=${toDate}`,
    );
    return await this.dailyReportService.getDailyReportGame(
      fromDate,
      toDate,
      req,
    );
  }

  @Get('game-player-partner')
  @ApiOperation({
    summary: 'Listar relatório de jogos por jogador e parceiro',
    description:
      'Endpoint para listar o relatório diário de jogos por jogador e parceiro com paginação',
  })
  @ApiPaginatedResponse(
    'Lista de relatórios retornada com sucesso',
    DailyReportGamePlayerPartnerResponseDto,
  )
  @ApiDateRangeQuery()
  @ApiPaginationQuery()
  @UseGuards(KeycloakBackofficeGuard)
  @ApiBearerAuth('access-token')
  async getDailyReportGamePlayerPartnerPagination(
    @Query() genericFilter: DailyReportGamePlayerPartnerFilterDto,
    @Req() req,
  ) {
    this.logger.log(
      `[GET] /daily-report/game-player-partner called with filter=${JSON.stringify(
        genericFilter,
      )}`,
    );
    return await this.dailyReportService.getDailyReportGamePlayerPartnerPagination(
      genericFilter,
      req,
    );
  }

  @Get('game-player-partner-all')
  @ApiOperation({
    summary: 'Listar todos os relatórios de jogos por jogador e parceiro',
    description:
      'Endpoint para listar todos os relatórios diários de jogos por jogador e parceiro sem paginação',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de relatórios retornada com sucesso',
    type: [DailyReportGamePlayerPartnerResponseDto],
  })
  @ApiDateRangeQuery()
  @UseGuards(KeycloakBackofficeGuard)
  @ApiBearerAuth('access-token')
  async getDailyReportGamePlayerPartner(
    @Query('fromDate') fromDate: string,
    @Query('toDate') toDate: string,
    @Req() req,
  ) {
    this.logger.log(
      `[GET] /daily-report/game-player-partner-all called with fromDate=${fromDate} and toDate=${toDate}`,
    );
    return await this.dailyReportService.getDailyReportGamePlayerPartner(
      fromDate,
      toDate,
      req,
    );
  }

  @Get(':playerId/by-player')
  @ApiOperation({
    summary: 'Buscar relatório por jogador',
    description:
      'Endpoint para buscar o relatório diário de um jogador específico',
  })
  @ApiResponse({
    status: 200,
    description: 'Relatório do jogador retornado com sucesso',
    type: DailyReportPlayerResponseDto,
  })
  @UseGuards(KeycloakBackofficeGuard)
  @ApiBearerAuth('access-token')
  async getDailyReportPlayerById(
    @Param('playerId') playerId: string,
    @Req() req,
  ) {
    this.logger.log(`[GET] /daily-report/${playerId}/by-player called`);
    return await this.dailyReportService.getDailyReportPlayerById(
      playerId,
      req,
    );
  }

  @Get('player')
  @ApiOperation({
    summary: 'Listar relatório de jogadores',
    description:
      'Endpoint para listar o relatório diário de jogadores com paginação',
  })
  @ApiPaginatedResponse(
    'Lista de relatórios de jogadores retornada com sucesso',
    DailyReportPlayerResponseDto,
  )
  @ApiQuery({ name: 'playerId', required: false })
  @ApiQuery({ name: 'email', required: false })
  @ApiDateRangeQuery()
  @UseGuards(KeycloakBackofficeGuard)
  @ApiBearerAuth('access-token')
  async getDailyReportPlayer(
    @Query('fromDate') fromDate: string,
    @Query('toDate') toDate: string,
    @Query() genericFilter: GenericFilter,
    @Req() req,
    @Query('playerId') playerId?: string,
    @Query('email') email?: string,
  ) {
    this.logger.log(
      `[GET] /daily-report/player called with filter=${JSON.stringify(
        genericFilter,
      )}`,
    );
    return await this.dailyReportService.getDailyReportPlayer(
      fromDate,
      toDate,
      genericFilter,
      req,
      playerId,
      email,
    );
  }

  @Get('providers-all')
  @ApiOperation({
    summary: 'Listar todos os relatórios de provedores',
    description:
      'Endpoint para listar todos os relatórios diários de provedores sem paginação',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de relatórios de provedores retornada com sucesso',
    type: [DailyReportProvidersResponseDto],
  })
  @ApiDateRangeQuery()
  @UseGuards(KeycloakBackofficeGuard)
  @ApiBearerAuth('access-token')
  async getDailyReportProviders(
    @Query('fromDate') fromDate: string,
    @Query('toDate') toDate: string,
    @Req() req,
  ) {
    this.logger.log(
      `[GET] /daily-report/providers-all called with fromDate=${fromDate} and toDate=${toDate}`,
    );
    return await this.dailyReportService.getDailyReportProviders(
      fromDate,
      toDate,
      req,
    );
  }

  @Get('providers')
  @ApiOperation({
    summary: 'Listar relatório de provedores',
    description:
      'Endpoint para listar o relatório diário de provedores com paginação',
  })
  @ApiPaginatedResponse(
    'Lista de relatórios de provedores retornada com sucesso',
    DailyReportProvidersResponseDto,
  )
  @ApiQuery({ name: 'gameProviderId', required: false })
  @ApiDateRangeQuery()
  @UseGuards(KeycloakBackofficeGuard)
  @ApiBearerAuth('access-token')
  async getDailyReportProvidersPagination(
    @Query('fromDate') fromDate: string,
    @Query('toDate') toDate: string,
    @Query() genericFilter: GenericFilter,
    @Req() req,
    @Query('gameProviderId') gameProviderId?: string,
  ) {
    this.logger.log(
      `[GET] /daily-report/providers called with filter=${JSON.stringify(
        genericFilter,
      )}`,
    );
    return await this.dailyReportService.getDailyReportProvidersPagination(
      fromDate,
      toDate,
      genericFilter,
      req,
      gameProviderId,
    );
  }

  @Get('game/profitability')
  @ApiOperation({
    summary: 'Buscar lucratividade dos jogos',
    description: 'Endpoint para buscar o relatório de lucratividade dos jogos',
  })
  @UseGuards(KeycloakBackofficeGuard)
  @ApiBearerAuth('access-token')
  @ApiResponse({
    status: 200,
    description: 'Relatório de lucratividade retornado com sucesso',
    type: [GameProfitabilityResponseDto],
  })
  @ApiQuery({
    name: 'dateRange[from]',
    type: 'string',
    required: true,
    description: 'Data inicial (YYYY-MM-DD)',
    example: '2024-01-01',
  })
  @ApiQuery({
    name: 'dateRange[to]',
    type: 'string',
    required: true,
    description: 'Data final (YYYY-MM-DD)',
    example: '2024-01-31',
  })
  async getGameProfitability(
    @Query() filter: GameProfitabilityFilterDto,
    @Req() req,
  ) {
    this.logger.log(
      `[GET] /daily-report/game/profitability called with filter=${JSON.stringify(
        filter,
      )}`,
    );
    return await this.dailyReportService.findGameProfitability(
      filter,
      req.headers['partner-id'],
    );
  }

  @Get('bets')
  @ApiOperation({
    summary: 'Listar relatório de apostas',
    description:
      'Endpoint para listar o relatório de apostas com informações detalhadas por transação. Permite consultar dados em realtime.',
  })
  @ApiPaginatedResponse(
    'Lista de apostas retornada com sucesso',
    BetReportResponseDto,
  )
  @ApiDateRangeQuery()
  @ApiPaginationQuery()
  @UseGuards(KeycloakBackofficeGuard)
  @ApiBearerAuth('access-token')
  async getBetReport(@Query() filter: BetReportFilterDto, @Req() req) {
    this.logger.log(
      `[GET] /daily-report/bets called with filter=${JSON.stringify(filter)}`,
    );
    return await this.dailyReportService.getBetReportPagination(filter, req);
  }
}
