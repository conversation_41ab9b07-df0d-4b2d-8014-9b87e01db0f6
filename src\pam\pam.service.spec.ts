import { Test, TestingModule } from '@nestjs/testing';
import { PamService } from './pam.service';

describe('PamService', () => {
  let service: PamService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PamService],
    }).compile();

    service = module.get<PamService>(PamService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
}); 