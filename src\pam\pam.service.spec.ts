import { Test, TestingModule } from '@nestjs/testing';
import { PamService } from './pam.service';
import axios from 'axios';

jest.mock('axios');

describe('PamService', () => {
  let service: PamService;

  const pamUrl = 'http://fake-pam-url';
  beforeEach(async () => {
    process.env.API_PAM = pamUrl;
    const module: TestingModule = await Test.createTestingModule({
      providers: [PamService],
    }).compile();

    service = module.get<PamService>(PamService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getUser', () => {
    const reqMock = {
      headers: {
        authorization: 'Bearer token123',
        'partner-id': 'partner1',
        'client-ip': '127.0.0.1',
      },
    };
    const email = '<EMAIL>';
    const userData = { id: 1, email: '<EMAIL>', name: '<PERSON><PERSON>' };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('deve retornar os dados do usuário em caso de sucesso', async () => {
      (axios.get as jest.Mock).mockResolvedValue({ data: userData });
      const result = await service.getUser(email, reqMock);
      expect(result).toEqual(userData);
      expect(axios.get).toHaveBeenCalledWith(`${pamUrl}/users/email/${email}`, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer token123',
          'partner-id': 'partner1',
          'client-ip': '127.0.0.1',
        },
      });
    });

    it('deve tratar erro e retornar undefined', async () => {
      (axios.get as jest.Mock).mockRejectedValue(new Error('Erro na API'));
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const result = await service.getUser(email, reqMock);
      expect(result).toBeUndefined();
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });
});
