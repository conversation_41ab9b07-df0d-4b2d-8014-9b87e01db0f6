import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON>Array,
  IsNumberString,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class ResponseTypesGameDto {
  @ApiProperty({ example: 'sample_provider:sample_game' })
  @IsString()
  game: string;

  @ApiProperty({ example: '20.5' })
  @IsNumberString()
  cost_multiplier: string;
}

class ResponseTypesBonusDto {
  @ApiProperty({ example: 'MegaBonus' })
  @IsString()
  name: string;

  @ApiProperty({ example: 'Mega mega bonus' })
  @IsString()
  description: string;

  @ApiProperty({ example: 'sample_provider' })
  @IsString()
  provider: string;

  @ApiProperty({ type: [ResponseTypesGameDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ResponseTypesGameDto)
  games: ResponseTypesGameDto[];
}

export class ResponseTypesDto {
  @ApiProperty({ type: [ResponseTypesBonusDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ResponseTypesBonusDto)
  bonus_types: ResponseTypesBonusDto[];
}
