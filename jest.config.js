module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '.',
  testRegex: '.*\\.spec\\.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: [
    '**/*.(t|j)s',
    '!src/common/**',
    '/src/.*/entities/.*\\.ts$',
    '/src/.*/dto/.*\\.ts$',
  ],
  coverageDirectory: './coverage',
  testEnvironment: 'node',
  preset: 'ts-jest',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^src/(.*)$': '<rootDir>/src/$1',
  },
  coveragePathIgnorePatterns: [
    '/node_modules/',
    '\\.enum\\.ts$',
    '/src/.*/entities/.*\\.ts$',
    '/src/.*/dto/.*\\.ts$',
    '.*\\.entity\\.ts$',
    '.*\\.dto\\.ts$',
  ],
};
 