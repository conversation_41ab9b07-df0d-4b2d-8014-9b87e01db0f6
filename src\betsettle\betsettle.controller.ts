import { Body, Controller, HttpCode, Post, UseGuards } from '@nestjs/common';
import { BetSettleService } from './betsettle.service';
import { CreateBetSettleDto } from './dto/create-betsettle.dto';
import { HMACGuard } from '@/common/guards/hmac.guard';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import {
  ApiHmacHeader,
  ApiHmacUnauthorizedResponse,
} from '@/common/decorators/swagger.decorator';
import { SwaggerApiCodeResponses } from '@/common/decorators/swagger-api-code-responses';
import {
  SUCCESS,
  ALREADY_PROCESSED,
  GENERIC_ERROR,
  PLAYER_NOT_FOUND,
  SESSION_NOT_FOUND,
  GAME_NOT_FOUND,
  ROUND_NOT_FOUND,
  INSUFFICIENT_FUNDS,
  PLAYER_BLOCKED,
  TOTAL_LOSS_LIMIT,
  TOTAL_STAKE_LIMIT,
  MAX_STAKE_LIMIT,
  DAILY_TIME_LIMIT,
  WEEKLY_TIME_LIMIT,
  MONTHLY_TIME_LIMIT,
  SELF_EXCLUDED,
} from '@/common/constants/message-codes';
import {
  CreateBetSettleResponseDto,
  CreateBetSettleResponseDtoExample,
} from './dto/create-betsettle-response.dto';

@Controller('betsettle')
@ApiTags('Betsettle')
export class BetSettleController {
  constructor(private readonly betSettleService: BetSettleService) {}

  @UseGuards(HMACGuard)
  @Post()
  @HttpCode(200)
  @ApiOperation({
    summary: 'Liquidar aposta',
    description: 'Endpoint para liquidar uma aposta e processar os ganhos',
  })
  @ApiHmacHeader()
  @ApiHmacUnauthorizedResponse()
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
      INSUFFICIENT_FUNDS.code,
      PLAYER_BLOCKED.code,
      TOTAL_LOSS_LIMIT.code,
      TOTAL_STAKE_LIMIT.code,
      MAX_STAKE_LIMIT.code,
      DAILY_TIME_LIMIT.code,
      WEEKLY_TIME_LIMIT.code,
      MONTHLY_TIME_LIMIT.code,
      SELF_EXCLUDED.code,
    ],
    CreateBetSettleResponseDto,
    CreateBetSettleResponseDtoExample
  )
  async create(@Body() createBetSettleDto: CreateBetSettleDto): Promise<{
    balance?: number;
    errorCode: number;
    errorMsg?: string;
    casinoTransactionId?: string;
    reconcileAmount?: number;
    reconcileWinAmount?: number;
  }> {
    return this.betSettleService.create(createBetSettleDto);
  }
}
