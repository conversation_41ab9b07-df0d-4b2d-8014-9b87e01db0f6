import {
  ALREADY_PROCESSED,
  DAILY_TIME_LIMIT,
  GAME_NOT_FOUND,
  GENERIC_ERROR,
  INSUFFICIENT_FUNDS,
  MAX_STAKE_LIMIT,
  MONTHLY_TIME_LIMIT,
  PLAYER_BLOCKED,
  PLAYER_NOT_FOUND,
  ROUND_NOT_FOUND,
  SELF_EXCLUDED,
  SESSION_NOT_FOUND,
  SUCCESS,
  TOTAL_LOSS_LIMIT,
  TOTAL_STAKE_LIMIT,
  WEEKLY_TIME_LIMIT,
} from '@/common/constants/message-codes';
import { SwaggerApiCodeResponses } from '@/common/decorators/swagger-api-code-responses';
import { BusinessExceptionFilter } from '@/common/filters/business-exception.filter';
import { getOrigin } from '@/common/utils/device';
import {
  Body,
  Controller,
  HttpCode,
  Post,
  Req,
  UseFilters,
} from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { BetSettleService } from './betsettle.service';
import { CreateBetSettleResponseDto } from './dto/create-betsettle-response.dto';
import { CreateBetSettleDto } from './dto/create-betsettle.dto';
import { CreateBetsettleFinishDto } from './dto/create-betsettle-finish.dto';
import { BetsettleFinishResponseDto } from './dto/create-betsettle-finish-response.dto';

@Controller('betsettle')
@ApiTags('Betsettle')
export class BetSettleController {
  constructor(private readonly betSettleService: BetSettleService) {}

  @Post()
  @UseFilters(BusinessExceptionFilter)
  @HttpCode(200)
  @ApiOperation({
    summary: 'Liquidar aposta',
    description: 'Endpoint para liquidar uma aposta e processar os ganhos',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      ALREADY_PROCESSED.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      GAME_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
      INSUFFICIENT_FUNDS.code,
      PLAYER_BLOCKED.code,
      TOTAL_LOSS_LIMIT.code,
      TOTAL_STAKE_LIMIT.code,
      MAX_STAKE_LIMIT.code,
      DAILY_TIME_LIMIT.code,
      WEEKLY_TIME_LIMIT.code,
      MONTHLY_TIME_LIMIT.code,
      SELF_EXCLUDED.code,
    ],
    CreateBetSettleResponseDto,
  )
  async create(
    @Body() createBetSettleDto: CreateBetSettleDto,
    @Req() req: Request,
  ): Promise<{
    balance?: number;
    casinoTransactionId?: string;
    reconcileAmount?: number;
    reconcileWinAmount?: number;
  }> {
    return this.betSettleService.create(createBetSettleDto, getOrigin(req));
  }

  @Post('finish')
  @UseFilters(BusinessExceptionFilter)
  @HttpCode(200)
  @ApiOperation({
    summary: 'Finalizar aposta',
    description: 'Endpoint para finalizar uma aposta',
  })
  @SwaggerApiCodeResponses(
    200,
    [
      SUCCESS.code,
      GENERIC_ERROR.code,
      PLAYER_NOT_FOUND.code,
      SESSION_NOT_FOUND.code,
      ROUND_NOT_FOUND.code,
    ],
    CreateBetSettleResponseDto,
  )
  async finish(
    @Body() dto: CreateBetsettleFinishDto,
  ): Promise<BetsettleFinishResponseDto> {
    return this.betSettleService.finish(dto);
  }
}
