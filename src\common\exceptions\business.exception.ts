import { HttpException, HttpStatus } from '@nestjs/common';
import { ErrorCode, ErrorMetadata } from '../enums/error-codes.enum';

/**
 * Custom business exception that includes error code and metadata
 * This exception will be caught by the global exception filter
 */
export class BusinessException extends HttpException {
  constructor(
    public readonly errorCode: ErrorCode,
    public readonly message: string,
    public readonly metadata?: ErrorMetadata,
    public readonly httpStatus: HttpStatus = HttpStatus.BAD_REQUEST,
  ) {
    super(message, httpStatus);
  }

  getErrorCode(): ErrorCode {
    return this.errorCode;
  }

  getMetadata(): ErrorMetadata | undefined {
    return this.metadata;
  }
}
