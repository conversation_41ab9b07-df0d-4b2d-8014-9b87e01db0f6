import { ApiProperty } from '@nestjs/swagger';

class SubAccountWalletDto {
  @ApiProperty({ example: 'e3e54764-96b9-484e-946f-fb57...' })
  id: string;

  @ApiProperty({ example: 'partner-123' })
  partnerId: string;

  @ApiProperty({ example: 'a477dc13-2b4a-4108-aa31-a...' })
  playerId: string;

  @ApiProperty({ example: 'WALLET-123456' })
  walletNumber: string;

  @ApiProperty({ example: 'BRL' })
  currency: string;

  @ApiProperty({ example: '2025-10-15T05:15:56.569Z' })
  createdAt: string;

  @ApiProperty({ example: '2025-10-15T05:15:56.569Z' })
  updatedAt: string;

  @ApiProperty({ example: null })
  deletedAt: string | null;
}

class SubAccountDto {
  @ApiProperty({ example: 'sub-account-123' })
  id: string;

  @ApiProperty({ example: 'regular' })
  accountType: string;

  @ApiProperty({ example: 1000.5 })
  balance: number;

  @ApiProperty({ example: '2025-10-15T05:15:56.569Z' })
  createdAt: string;

  @ApiProperty({ example: '2025-10-15T05:15:56.569Z' })
  updatedAt: string;

  @ApiProperty({ example: null })
  deletedAt: string | null;

  @ApiProperty({ type: SubAccountWalletDto })
  wallet: SubAccountWalletDto;
}

export class UpdateBalanceResponseDto {
  @ApiProperty({ example: 'partner-123' })
  partnerId: string;

  @ApiProperty({ example: 'transaction-123' })
  transactionId: string;

  @ApiProperty({ example: 100 })
  amount: number;

  @ApiProperty({ example: 'Deposit' })
  notes: string;

  @ApiProperty({ example: 'casino' })
  origin: string;

  @ApiProperty({ example: 900.5 })
  initialBalance: number;

  @ApiProperty({ example: 1000.5 })
  endBalance: number;

  @ApiProperty({ type: SubAccountDto })
  subAccount: SubAccountDto;

  @ApiProperty({ example: 1 })
  id: number;

  @ApiProperty({ example: '2025-10-15T05:15:56.569Z' })
  createdAt: string;

  @ApiProperty({ example: '2025-10-15T05:15:56.569Z' })
  updatedAt: string;

  @ApiProperty({ example: null })
  deletedAt: string | null;
}
