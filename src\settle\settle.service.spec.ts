import { Test, TestingModule } from '@nestjs/testing';
import { SettleService } from './settle.service';
import { WalletService } from '../wallet/wallet.service';
import { EntityManager } from 'typeorm';
import { CreateSettleDto } from './dto/create-settle.dto';

const mockTransaction = {
  partnerId: 'partner-1',
};

const dto: CreateSettleDto = {
  playerId: 'player-1',
  sessionId: 'session-1',
  transactionId: 'trans-1',
  requestType: 'RealMoney',
  gameId: 1,
  roundId: 'round-1',
  roundEnded: true,
  amount: 100,
};

describe('SettleService', () => {
  let service: SettleService;
  let entityManager: any;
  let walletService: any;

  const mockEntityManager = {
    findOne: jest.fn(),
    update: jest.fn(),
  };
  const mockWalletService = {
    credit: jest.fn(),
    getBalanceByPlayer: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SettleService,
        { provide: EntityManager, useValue: mockEntityManager },
        { provide: WalletService, useValue: mockWalletService },
      ],
    }).compile();

    service = module.get<SettleService>(SettleService);
    entityManager = module.get<EntityManager>(EntityManager);
    walletService = module.get<WalletService>(WalletService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('set', () => {
    it('deve liquidar aposta com sucesso quando amount > 0', async () => {
      entityManager.findOne.mockResolvedValue(mockTransaction);
      entityManager.update.mockResolvedValue({});
      walletService.credit.mockResolvedValue({ subAccount: { balance: 300 } });
      const result = await service.set(dto);
      expect(result).toEqual({ balance: 300, errorCode: 0 });
      expect(walletService.credit).toHaveBeenCalledWith(
        {
          amount: 100,
          reason: 'Set',
          isBonusOperation: false,
          gameId: '1',
        },
        'partner-1',
        'player-1'
      );
    });

    it('deve liquidar aposta com sucesso quando amount <= 0', async () => {
      const dtoZero = { ...dto, amount: 0 };
      entityManager.findOne.mockResolvedValue(mockTransaction);
      entityManager.update.mockResolvedValue({});
      walletService.getBalanceByPlayer.mockResolvedValue({ balance: 200 });
      const result = await service.set(dtoZero);
      expect(result).toEqual({ balance: 200, errorCode: 0 });
      expect(walletService.getBalanceByPlayer).toHaveBeenCalledWith('partner-1', 'player-1');
    });

    it('deve retornar erro se transação não encontrada', async () => {
      entityManager.findOne.mockResolvedValue(null);
      const result = await service.set(dto);
      expect(result).toEqual({ errorCode: 1115, errorMsg: 'Bet not found' });
    });

    it('deve retornar erro se walletService.credit lançar erro', async () => {
      entityManager.findOne.mockResolvedValue(mockTransaction);
      entityManager.update.mockResolvedValue({});
      walletService.credit.mockRejectedValue(new Error('Erro no crédito'));
      const result = await service.set(dto);
      expect(result).toEqual({ errorCode: 2, errorMsg: 'Erro no crédito' });
    });

    it('deve retornar erro se walletService.getBalanceByPlayer lançar erro', async () => {
      const dtoZero = { ...dto, amount: 0 };
      entityManager.findOne.mockResolvedValue(mockTransaction);
      entityManager.update.mockResolvedValue({});
      walletService.getBalanceByPlayer.mockRejectedValue(new Error('Erro no balance'));
      const result = await service.set(dtoZero);
      expect(result).toEqual({ errorCode: 2, errorMsg: 'Erro no balance' });
    });

    it('deve retornar erro customizado se walletService lançar erro com response', async () => {
      entityManager.findOne.mockResolvedValue(mockTransaction);
      entityManager.update.mockResolvedValue({});
      const error: any = new Error('Erro customizado');
      error.response = { errorCode: 99, errorMsg: 'Erro customizado' };
      walletService.credit.mockRejectedValue(error);
      const result = await service.set(dto);
      expect(result).toEqual({ errorCode: 99, errorMsg: 'Erro customizado' });
    });
  });
}); 