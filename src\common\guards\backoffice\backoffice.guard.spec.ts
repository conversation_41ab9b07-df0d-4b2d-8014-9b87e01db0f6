import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, HttpException, HttpStatus } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { BackofficeGuard } from './backoffice.guard';

describe('BackofficeGuard', () => {
  let guard: BackofficeGuard;
  let jwtService: JwtService;

  const mockJwtService = {
    verify: jest.fn(),
  };

  const createMockExecutionContext = (
    headers: any,
    method: string = 'GET',
    path: string = '/test',
  ): ExecutionContext => {
    return {
      switchToHttp: () => ({
        getRequest: () => ({
          headers,
          method,
          route: { path },
          user: null,
        }),
      }),
    } as any;
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BackofficeGuard,
        { provide: JwtService, useValue: mockJwtService },
      ],
    }).compile();

    guard = module.get<BackofficeGuard>(BackofficeGuard);
    jwtService = module.get<JwtService>(JwtService);
    jest.clearAllMocks();

    // Mock environment variables
    process.env.JWT_SECRET = 'test-secret';
    process.env.BASE_BACKOFFICE_URL = 'http://backoffice.test';

    // Mock global fetch
    global.fetch = jest.fn();
  });

  afterEach(() => {
    delete process.env.JWT_SECRET;
    delete process.env.BASE_BACKOFFICE_URL;
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  describe('canActivate', () => {
    it('deve retornar true quando token é válido e usuário tem permissão', async () => {
      // Token JWT válido com payload simples: {"userId":"user-123","email":"<EMAIL>"}
      const token =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************.signature';
      const headers = {
        authorization: `Bearer ${token}`,
        'partner-id': 'partner-123',
      };

      const mockPayload = {
        userId: 'user-123',
        email: '<EMAIL>',
      };

      mockJwtService.verify.mockResolvedValue(mockPayload);

      (global.fetch as jest.Mock).mockResolvedValue({
        json: async () => true,
      });

      const context = createMockExecutionContext(headers);
      const result = await guard.canActivate(context);

      expect(result).toBe(true);
      expect(jwtService.verify).toHaveBeenCalledWith(token, {
        secret: 'test-secret',
      });
    });

    it('deve lançar HttpException quando header Authorization não está presente', async () => {
      const context = createMockExecutionContext({});

      await expect(guard.canActivate(context)).rejects.toThrow(
        new HttpException(
          'No authentication header provided.',
          HttpStatus.UNAUTHORIZED,
        ),
      );
    });

    it('deve lançar HttpException quando token não é Bearer', async () => {
      const headers = {
        authorization: 'Basic some-token',
      };

      const context = createMockExecutionContext(headers);

      await expect(guard.canActivate(context)).rejects.toThrow(
        new HttpException(
          'No authentication header provided.',
          HttpStatus.UNAUTHORIZED,
        ),
      );
    });

    it('deve lançar HttpException quando token é inválido', async () => {
      const token = 'invalid.token';
      const headers = {
        authorization: `Bearer ${token}`,
        'partner-id': 'partner-123',
      };

      mockJwtService.verify.mockRejectedValue(new Error('Invalid token'));

      const context = createMockExecutionContext(headers);

      await expect(guard.canActivate(context)).rejects.toThrow(HttpException);
    });

    it('deve lançar HttpException quando usuário não tem permissão', async () => {
      const token = 'valid.jwt.token';
      const headers = {
        authorization: `Bearer ${token}`,
        'partner-id': 'partner-123',
      };

      const mockPayload = {
        userId: 'user-123',
        email: '<EMAIL>',
      };

      mockJwtService.verify.mockResolvedValue(mockPayload);

      (global.fetch as jest.Mock).mockResolvedValue({
        json: async () => false,
      });

      const context = createMockExecutionContext(headers);

      await expect(guard.canActivate(context)).rejects.toThrow(
        new HttpException(
          'Authentication failed: Token expired or invalid.',
          HttpStatus.FORBIDDEN,
        ),
      );
    });

    it('deve popular req.user com payload do token', async () => {
      const token =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************.signature';
      const headers = {
        authorization: `Bearer ${token}`,
        'partner-id': 'partner-123',
      };

      mockJwtService.verify.mockResolvedValue({
        userId: 'user-123',
        email: '<EMAIL>',
      });

      (global.fetch as jest.Mock).mockResolvedValue({
        json: async () => true,
      });

      const mockRequest = {
        headers,
        method: 'GET',
        route: { path: '/test' },
        user: null,
      };

      const context = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
        }),
      } as ExecutionContext;

      await guard.canActivate(context);

      expect(mockRequest.user).toBeDefined();
      expect(mockRequest.user.userId).toBe('user-123');
    });

    it('deve chamar API de permissões com parâmetros corretos', async () => {
      // Token JWT válido com userId
      const token =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************.signature';
      const headers = {
        authorization: `Bearer ${token}`,
        'partner-id': 'partner-456',
      };

      const mockPayload = {
        userId: 'user-456',
        email: '<EMAIL>',
      };

      mockJwtService.verify.mockResolvedValue(mockPayload);

      (global.fetch as jest.Mock).mockResolvedValue({
        json: async () => true,
      });

      const context = createMockExecutionContext(headers, 'POST', '/api/test');

      await guard.canActivate(context);

      expect(global.fetch).toHaveBeenCalledWith(
        'http://backoffice.test/v1/pam/users/user-456/check-permission?method=POST&path=/api/test&customerPartnerId=partner-456',
        {
          method: 'GET',
          headers: { Authorization: `Bearer ${token}` },
        },
      );
    });

    it('deve lançar HttpException UNAUTHORIZED quando token expirou', async () => {
      const token = 'expired.token';
      const headers = {
        authorization: `Bearer ${token}`,
        'partner-id': 'partner-123',
      };

      const expiredError: any = new Error('Token expired');
      expiredError.expiredAt = new Date();

      mockJwtService.verify.mockRejectedValue(expiredError);

      const context = createMockExecutionContext(headers);

      await expect(guard.canActivate(context)).rejects.toThrow(HttpException);
      await expect(guard.canActivate(context)).rejects.toMatchObject({
        status: HttpStatus.UNAUTHORIZED,
      });
    });
  });
});
