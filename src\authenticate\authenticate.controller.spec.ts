import { Test, TestingModule } from '@nestjs/testing';
import { AuthenticateController } from './authenticate.controller';
import { AuthenticateService } from './authenticate.service';
import { HMACGuard } from '@/common/guards/hmac.guard';

describe('AuthenticateController', () => {
  let controller: AuthenticateController;

  const mockAuthenticateService = {};
  const mockHMACGuard = { canActivate: jest.fn(() => true) };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthenticateController],
      providers: [
        { provide: AuthenticateService, useValue: mockAuthenticateService },
      ],
    })
      .overrideGuard(HMACGuard)
      .useValue(mockHMACGuard)
      .compile();

    controller = module.get<AuthenticateController>(AuthenticateController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
}); 