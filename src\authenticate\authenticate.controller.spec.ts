import { Test, TestingModule } from '@nestjs/testing';
import { AuthenticateController } from './authenticate.controller';
import { AuthenticateService } from './authenticate.service';
import { HMACGuard } from '@/common/guards/hmac.guard';
import { CreateRegisterAuthenticateDto } from './dto/create-register-authenticate.dto';

describe('AuthenticateController', () => {
  let controller: AuthenticateController;
  let service: AuthenticateService;

  const mockAuthenticateService = {
    authenticate: jest.fn(),
  };
  const mockHMACGuard = { canActivate: jest.fn(() => true) };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthenticateController],
      providers: [
        { provide: AuthenticateService, useValue: mockAuthenticateService },
      ],
    })
      .overrideGuard(HMACGuard)
      .useValue(mockHMACGuard)
      .compile();

    controller = module.get<AuthenticateController>(AuthenticateController);
    service = module.get<AuthenticateService>(AuthenticateService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('authenticate', () => {
    it('deve autenticar jogador com sucesso', async () => {
      const dto: CreateRegisterAuthenticateDto = {
        token: 'valid-token-123',
        playerId: 'player-123',
        sessionId: 'session-123',
      };

      const expectedBalance = 100.5;

      mockAuthenticateService.authenticate.mockResolvedValue(expectedBalance);

      const result = await controller.authenticate(dto);

      expect(result).toBe(expectedBalance);
      expect(service.authenticate).toHaveBeenCalledWith(dto);
    });

    it('deve chamar service com DTO correto', async () => {
      const dto: CreateRegisterAuthenticateDto = {
        token: 'token-456',
        playerId: 'player-456',
        sessionId: 'session-456',
      };

      mockAuthenticateService.authenticate.mockResolvedValue(200);

      await controller.authenticate(dto);

      expect(service.authenticate).toHaveBeenCalledTimes(1);
      expect(service.authenticate).toHaveBeenCalledWith(dto);
    });

    it('deve propagar HttpException quando token é inválido', async () => {
      const dto: CreateRegisterAuthenticateDto = {
        token: 'invalid-token',
        playerId: 'player-789',
        sessionId: 'session-789',
      };

      mockAuthenticateService.authenticate.mockRejectedValue(
        new Error('Token inválido ou expirado.'),
      );

      await expect(controller.authenticate(dto)).rejects.toThrow(
        'Token inválido ou expirado.',
      );
    });

    it('deve propagar erro do service', async () => {
      const dto: CreateRegisterAuthenticateDto = {
        token: 'error-token',
        playerId: 'player-error',
        sessionId: 'session-error',
      };

      mockAuthenticateService.authenticate.mockRejectedValue(
        new Error('Authentication failed'),
      );

      await expect(controller.authenticate(dto)).rejects.toThrow(
        'Authentication failed',
      );
    });

    it('deve retornar balance correto após autenticação', async () => {
      const dto: CreateRegisterAuthenticateDto = {
        token: 'balance-token',
        playerId: 'player-balance',
        sessionId: 'session-balance',
      };

      mockAuthenticateService.authenticate.mockResolvedValue(500.75);

      const result = await controller.authenticate(dto);

      expect(result).toBe(500.75);
    });
  });
});
