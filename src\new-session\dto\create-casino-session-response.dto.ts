import { ApiProperty } from '@nestjs/swagger';
import { SessionStatus } from '../enums/new-session.enum';
export class CreateCasinoSessionResponseDto {
  @ApiProperty({
    description: 'ID da sessão criada',
    example: '5a553398-40e7-48fa-9f52-3c0e8502ce7d',
  })
  id: string;

  @ApiProperty({
    description: 'ID da sessão do jogador',
    example: '5a553398-40e7-48fa-9f52-3c0e8502ce7d',
  })
  playerLoginSessionId: string;

  @ApiProperty({
    description: 'ID da sessão de atividade do jogador',
    example: '5a553398-40e7-48fa-9f52-3c0e8502ce7d',
  })
  playerActivitySessionId: string;

  @ApiProperty({
    description: 'Status da sessão',
    example: 'ACTIVE',
  })
  status: SessionStatus;

  @ApiProperty({
    description: 'Data de expiração da sessão',
    example: '2025-08-01T01:14:41.483Z',
  })
  expiredAt: Date;
}
