import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber, IsString, ValidateNested } from 'class-validator';

export class CreateBetSettleTransactionResponseDto {
  @ApiProperty({
    description: 'Identificador da transação',
    example: '12345678-1234-1234-1234-123456789012',
    required: true,
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Identificador externo da transação do casino',
    example: '12345678-1234-1234-1234-123456789012',
    required: true,
  })
  @IsString()
  externalId: string;

  @ApiProperty({
    description: 'Valor da transação',
    example: 100.5,
    required: true,
  })
  @IsNumber()
  amount: number;

  type?: string;
}

export class CreateBetSettleResponseDto {
  @ApiProperty({
    description: 'Identificador da sessão do casino',
    example: '12345678-1234-1234-1234-123456789012',
    required: true,
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Identificador externo da sessão do casino',
    example: '12345678-1234-1234-1234-123456789012',
    required: true,
  })
  @IsString()
  externalId: string;

  @ApiProperty({
    description: 'Saldo do jogador',
    example: 100.5,
    required: true,
  })
  @IsNumber()
  balance: number;

  @ApiProperty({
    description: 'Transações',
    type: [CreateBetSettleTransactionResponseDto],
    required: true,
  })
  @Type(() => CreateBetSettleTransactionResponseDto)
  @ValidateNested({ each: true })
  transactions: CreateBetSettleTransactionResponseDto[];

  @ApiProperty({
    description: 'Código de erro',
    example: 0,
    required: false,
  })
  @IsNumber()
  errorCode?: number;

  @ApiProperty({
    description: 'Mensagem de erro',
    example: 'Erro interno do servidor',
    required: false,
  })
  @IsString()
  errorMsg?: string;
}
