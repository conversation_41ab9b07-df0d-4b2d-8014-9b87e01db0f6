import { Test, TestingModule } from '@nestjs/testing';
import { FreechipService } from './freechip.service';
import { HttpService } from '@nestjs/axios';
import { of, throwError } from 'rxjs';
import { HttpException } from '@nestjs/common';

describe('FreechipService', () => {
  let service: FreechipService;
  let httpService: { post: jest.Mock };

  const GATEWAY_URL = 'http://gateway.test';

  beforeAll(() => {
    process.env.GATEWAY_URL = GATEWAY_URL;
  });

  beforeEach(async () => {
    httpService = { post: jest.fn() };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FreechipService,
        { provide: HttpService, useValue: httpService },
      ],
    }).compile();

    service = module.get<FreechipService>(FreechipService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('finish should return static balance', async () => {
    const dto: any = { userId: 'u1', amount: '5' };
    await expect(service.finish(dto)).resolves.toEqual({ balance: '0.01' });
  });

  it('issue should POST to gateway and return data', async () => {
    const dto: any = { userId: 'u1', amount: '5' };
    const data = { issued: true };
    httpService.post.mockReturnValue(of({ data }));

    await expect(service.issue(dto)).resolves.toEqual(data);
    expect(httpService.post).toHaveBeenCalledWith(
      `${GATEWAY_URL}/casino/freechip/issue`,
      dto,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-aggregator': 'softswiss',
        },
      },
    );
  });

  it('cancel should POST to gateway and return data', async () => {
    const dto: any = { promotionId: 'p1' };
    const data = { cancelled: true };
    httpService.post.mockReturnValue(of({ data }));

    await expect(service.cancel(dto)).resolves.toEqual(data);
    expect(httpService.post).toHaveBeenCalledWith(
      `${GATEWAY_URL}/casino/freechip/cancel`,
      dto,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-aggregator': 'softswiss',
        },
      },
    );
  });

  it('issue should wrap http errors in HttpException', async () => {
    const dto: any = { userId: 'u1', amount: '5' };
    const error: any = { message: 'bad gateway', status: 502 };
    httpService.post.mockReturnValue(throwError(() => error));

    await expect(service.issue(dto)).rejects.toBeInstanceOf(HttpException);
    await expect(service.issue(dto)).rejects.toMatchObject({
      message: error.message,
    });
  });
});
