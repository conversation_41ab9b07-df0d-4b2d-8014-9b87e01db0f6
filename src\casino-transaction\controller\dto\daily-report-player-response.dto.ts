import { ApiProperty } from '@nestjs/swagger';

export class DailyReportPlayerResponseDto {
  @ApiProperty({ description: 'ID do jogador' })
  playerId: string;

  @ApiProperty({ description: 'ID do parceiro' })
  partnerId: string;

  @ApiProperty({ description: 'Email do jogador' })
  email: string;

  @ApiProperty({ description: 'Total de apostas' })
  totalBet: number;

  @ApiProperty({ description: 'Total de ganhos' })
  totalSettle: number;

  @ApiProperty({ description: 'Moeda' })
  currency: string;

  @ApiProperty({ description: 'GGR (Gross Gaming Revenue)' })
  ggr: number;

  @ApiProperty({ description: 'Data do relatório' })
  date: Date;
} 