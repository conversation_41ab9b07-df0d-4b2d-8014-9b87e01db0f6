import { Module } from '@nestjs/common';
import { BalanceService } from './balance.service';
import { BalanceController } from './balance.controller';
import { HMACModule } from '../hmac/hmac.module';
import { DatabaseModule } from '../common/database/database.module';
import { WalletModule } from '@/wallet/wallet.module';

@Module({
  imports: [DatabaseModule, HMACModule, WalletModule],
  controllers: [BalanceController],
  providers: [BalanceService],
  exports: [BalanceService],
})
export class BalanceModule {}
