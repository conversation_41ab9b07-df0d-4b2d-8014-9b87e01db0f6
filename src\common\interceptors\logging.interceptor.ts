import {
  Call<PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  Logger,
  NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const { method, url } = request;
    const now = Date.now();

    this.logger.log(`[${method}] ${url} - Started`);

    return next.handle().pipe(
      tap({
        next: response => {
          const duration = Date.now() - now;
          this.logger.log(
            `[${method}] ${url} - Completed (${duration}ms) - Status: 
  ${response?.status || 200}`,
          );
        },
        error: error => {
          const duration = Date.now() - now;
          this.logger.error(
            `[${method}] ${url} - Failed (${duration}ms) - Error: ${error.message}`,
          );
        },
      }),
    );
  }
}
