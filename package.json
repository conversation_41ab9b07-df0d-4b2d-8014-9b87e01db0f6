{"name": "nest-typescript-starter", "version": "1.0.0", "description": "Nest TypeScript starter repository", "license": "MIT", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" ", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint '{src,apps,libs}/**/*.ts' --fix", "format:lint": "npm run format && npm run lint", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "echo 'No e2e tests implemented yet.'", "test:sonar": "jest --coverage && npx sonar-scanner"}, "dependencies": {"@aws-sdk/client-s3": "^3.922.0", "@h2/rabbitmq": "^1.0.12", "@nestjs/axios": "^3.1.1", "@nestjs/common": "^10.3.2", "@nestjs/config": "^3.3.0", "@nestjs/core": "10.3.2", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "10.3.2", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.3.8", "@nestjs/schedule": "^4.1.2", "@nestjs/swagger": "^7.4.2", "@nestjs/terminus": "^10.2.3", "@nestjs/typeorm": "^10.0.2", "axios": "^1.7.7", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.5", "passport-jwt": "^4.0.1", "pg": "^8.13.1", "reflect-metadata": "0.2.1", "rimraf": "5.0.5", "rxjs": "7.8.1", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.20", "uuid": "^11.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "10.3.2", "@nestjs/schematics": "10.1.1", "@nestjs/testing": "10.3.2", "@types/amqplib": "0.10.4", "@types/express": "4.17.21", "@types/jest": "^29.5.14", "@types/multer": "^2.0.0", "@types/node": "20.8.7", "@types/supertest": "2.0.16", "@typescript-eslint/eslint-plugin": "^8.20.0", "@typescript-eslint/parser": "^8.20.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^15.14.0", "prettier": "^3.4.2", "jest": "^29.7.0", "jest-junit": "^16.0.0", "supertest": "6.3.3", "ts-jest": "^29.1.2", "ts-loader": "9.5.1", "ts-node": "10.9.2", "tsconfig-paths": "4.2.0", "typescript": "5.3.3", "typescript-eslint": "^8.20.0"}}