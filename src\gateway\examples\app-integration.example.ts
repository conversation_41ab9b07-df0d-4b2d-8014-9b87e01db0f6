/**
 * Exemplo de como integrar o Gateway no app.module.ts principal
 */

import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { GatewayModule } from '../gateway.module';
import { GatewayLoggingMiddleware } from '../middlewares/logging.middleware';
import { RateLimitMiddleware } from '../middlewares/rate-limit.middleware';

@Module({
  imports: [
    // ... outros módulos existentes
    GatewayModule,
  ],
  // ... outros providers e controllers
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Aplica middlewares específicos para rotas do gateway
    consumer
      .apply(GatewayLoggingMiddleware, RateLimitMiddleware)
      .forRoutes('gateway/*');
  }
}

/**
 * Exemplo de como integrar com o BalanceController existente
 */

// No arquivo casino-integration.service.ts, substitua os métodos mock:

/*
import { Injectable, Logger } from '@nestjs/common';
import { BalanceService } from '../../balance/balance.service'; // Ajuste o path
import { TransactionService } from '../../transaction/transaction.service'; // Ajuste o path

@Injectable()
export class CasinoIntegrationService {
  constructor(
    private readonly balanceService: BalanceService,
    private readonly transactionService: TransactionService,
  ) {}

  async getPlayerBalance(request: IBalanceRequest): Promise<IBalanceResponse> {
    try {
      // Chama o serviço existente de saldo
      const balanceResult = await this.balanceService.getBalance({
        playerId: request.playerId,
        gameId: request.gameId,
        currency: request.currency,
      });

      return {
        playerId: request.playerId,
        balance: balanceResult.balance,
        currency: balanceResult.currency,
        timestamp: new Date(),
        success: true,
        metadata: {
          source: 'casino-system',
          gameId: request.gameId,
        },
      };
    } catch (error) {
      return {
        playerId: request.playerId,
        balance: 0,
        currency: request.currency || 'BRL',
        timestamp: new Date(),
        success: false,
        error: error.message,
      };
    }
  }

  async processBet(request: IBetRequest): Promise<IBetResponse> {
    try {
      // Chama o serviço existente de transações
      const betResult = await this.transactionService.processBet({
        playerId: request.playerId,
        gameId: request.gameId,
        amount: request.amount,
        currency: request.currency,
        roundId: request.roundId,
        sessionId: request.sessionId,
      });

      return {
        playerId: request.playerId,
        transactionId: betResult.transactionId,
        balance: betResult.newBalance,
        currency: request.currency,
        timestamp: new Date(),
        success: true,
        metadata: {
          source: 'casino-system',
          roundId: request.roundId,
        },
      };
    } catch (error) {
      return {
        playerId: request.playerId,
        transactionId: `failed_${Date.now()}`,
        balance: 0,
        currency: request.currency,
        timestamp: new Date(),
        success: false,
        error: error.message,
      };
    }
  }

  async processWin(request: IWinRequest): Promise<IWinResponse> {
    try {
      // Chama o serviço existente de ganhos
      const winResult = await this.transactionService.processWin({
        playerId: request.playerId,
        gameId: request.gameId,
        amount: request.amount,
        currency: request.currency,
        roundId: request.roundId,
        betTransactionId: request.betTransactionId,
        sessionId: request.sessionId,
      });

      return {
        playerId: request.playerId,
        transactionId: winResult.transactionId,
        balance: winResult.newBalance,
        currency: request.currency,
        timestamp: new Date(),
        success: true,
        metadata: {
          source: 'casino-system',
          roundId: request.roundId,
          betTransactionId: request.betTransactionId,
        },
      };
    } catch (error) {
      return {
        playerId: request.playerId,
        transactionId: `failed_${Date.now()}`,
        balance: 0,
        currency: request.currency,
        timestamp: new Date(),
        success: false,
        error: error.message,
      };
    }
  }
}
*/

/**
 * Exemplo de configuração de variáveis de ambiente
 */

/*
# .env file

# Gateway Configuration
GATEWAY_ENABLED=true
GATEWAY_LOG_LEVEL=debug

# EGT Provider
EGT_API_TOKEN=your_egt_token_here
EGT_API_URL=https://api.egt.com
EGT_TIMEOUT=5000
EGT_RETRIES=3

# BGaming Provider
BGAMING_API_KEY=your_bgaming_api_key
BGAMING_SECRET_KEY=your_bgaming_secret_key
BGAMING_API_URL=https://api.bgaming.com
BGAMING_TIMEOUT=5000
BGAMING_RETRIES=3

# Rate Limiting
RATE_LIMIT_EGT=100
RATE_LIMIT_BGAMING=200
RATE_LIMIT_DEFAULT=50
RATE_LIMIT_WINDOW=60000
*/

/**
 * Exemplo de uso em um controller existente
 */

/*
import { Controller, Post, Body, Headers } from '@nestjs/common';
import { GatewayService } from '../gateway/services/gateway.service';

@Controller('legacy-api')
export class LegacyApiController {
  constructor(private readonly gatewayService: GatewayService) {}

  // Redireciona chamadas antigas para o gateway
  @Post('balance')
  async getBalance(@Body() body: any, @Headers() headers: any) {
    return await this.gatewayService.processBalance(body, headers);
  }

  @Post('bet')
  async placeBet(@Body() body: any, @Headers() headers: any) {
    return await this.gatewayService.processBet(body, headers);
  }

  @Post('win')
  async processWin(@Body() body: any, @Headers() headers: any) {
    return await this.gatewayService.processWin(body, headers);
  }
}
*/

/**
 * Exemplo de teste de integração
 */

/*
import { Test, TestingModule } from '@nestjs/testing';
import { GatewayService } from '../services/gateway.service';
import { AdapterFactoryService } from '../services/adapter-factory.service';

describe('Gateway Integration', () => {
  let gatewayService: GatewayService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [GatewayModule],
    }).compile();

    gatewayService = module.get<GatewayService>(GatewayService);
  });

  it('should process EGT balance request', async () => {
    const egtRequest = {
      user_id: 'player123',
      game_id: 'game456',
      currency: 'BRL',
    };

    const headers = {
      'x-provider': 'EGT',
      'user-agent': 'EGT-Client/1.0',
    };

    const result = await gatewayService.processBalance(egtRequest, headers);

    expect(result).toHaveProperty('status', 'success');
    expect(result).toHaveProperty('user_id', 'player123');
    expect(result).toHaveProperty('balance');
  });

  it('should process BGaming bet request', async () => {
    const bgamingRequest = {
      playerId: 'player123',
      gameCode: 'game456',
      betAmount: 10.50,
      currency: 'BRL',
      roundId: 'round789',
    };

    const headers = {
      'x-provider': 'BGAMING',
    };

    const result = await gatewayService.processBet(bgamingRequest, headers);

    expect(result).toHaveProperty('status', 'OK');
    expect(result).toHaveProperty('playerId', 'player123');
    expect(result).toHaveProperty('transactionId');
  });
});
*/
