import { Test, TestingModule } from '@nestjs/testing';
import { NewSessionService } from './new-session.service';
import { WalletService } from '../wallet/wallet.service';
import { EntityManager } from 'typeorm';
import { HttpException } from '@nestjs/common';

const mockCasinoSessionEntity = {
  id: 'sess-1',
  aggregatorId: 'agg-1',
  statusId: 1,
  playerId: 'player-1',
  partnerId: 'partner-1',
  games: { gameId: 'game-1' },
  gameId: 'game-1',
  requestId: 'req-1',
  launchUrl: 'url',
  launchToken: 'token',
  gameMode: 'real-play',
  token: 'token',
  lastUserUpdated: null,
  errorCode: 0,
  errorMsg: '',
};

describe('NewSessionService', () => {
  let service: NewSessionService;
  let entityManager: any;
  let walletService: any;

  const mockEntityManager = {
    findOneBy: jest.fn(),
    findOne: jest.fn(),
    insert: jest.fn(),
  };
  const mockWalletService = {
    getBalanceByPlayer: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NewSessionService,
        { provide: EntityManager, useValue: mockEntityManager },
        { provide: WalletService, useValue: mockWalletService },
      ],
    }).compile();

    service = module.get<NewSessionService>(NewSessionService);
    entityManager = module.get<EntityManager>(EntityManager);
    walletService = module.get<WalletService>(WalletService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    const dto = {
      sessionId: 'agg-1',
      playerId: 'player-1',
      newSessionId: 'agg-2',
      gameId: 'game-1',
    };

    it('deve criar nova sessão com sucesso', async () => {
      entityManager.findOneBy.mockResolvedValue({ gameId: 'game-1' });
      entityManager.findOne.mockResolvedValue(mockCasinoSessionEntity);
      walletService.getBalanceByPlayer.mockResolvedValue({ balance: 200 });
      entityManager.insert.mockResolvedValue({});
      const result = await service.create(dto);
      expect(result).toEqual({ errorCode: 0, balance: 200 });
    });

    it('deve lançar erro se sessão não encontrada', async () => {
      entityManager.findOneBy.mockResolvedValue({ gameId: 'game-1' });
      entityManager.findOne.mockResolvedValue(null);
      await expect(service.create(dto)).rejects.toThrow(HttpException);
    });

    it('deve lançar erro se saldo não encontrado', async () => {
      entityManager.findOneBy.mockResolvedValue({ gameId: 'game-1' });
      entityManager.findOne.mockResolvedValue(mockCasinoSessionEntity);
      walletService.getBalanceByPlayer.mockResolvedValue({});
      await expect(service.create(dto)).rejects.toThrow(HttpException);
    });

    it('deve lançar erro se falhar ao criar nova sessão', async () => {
      entityManager.findOneBy.mockResolvedValue({ gameId: 'game-1' });
      entityManager.findOne.mockResolvedValue(mockCasinoSessionEntity);
      walletService.getBalanceByPlayer.mockResolvedValue({ balance: 200 });
      entityManager.insert.mockRejectedValue(new Error('Falha ao inserir'));
      await expect(service.create(dto)).rejects.toThrow(HttpException);
    });

    it('deve lançar erro genérico', async () => {
      entityManager.findOneBy.mockImplementation(() => { throw new Error('Erro inesperado'); });
      await expect(service.create(dto)).rejects.toThrow(HttpException);
    });
  });
}); 