import { Test, TestingModule } from '@nestjs/testing';
import { BalanceService } from './balance.service';
import { WalletService } from '../wallet/wallet.service';
import { EntityManager } from 'typeorm';
import { HttpException, HttpStatus } from '@nestjs/common';

const mockGetBalanceDto = {
  playerId: 'player-1',
  sessionId: 'session-1',
};

describe('BalanceService', () => {
  let service: BalanceService;
  let entityManager: any;
  let walletService: any;

  const mockEntityManager = {
    findOne: jest.fn(),
    insert: jest.fn(),
  };
  const mockWalletService = {
    getBalanceByPlayer: jest.fn(),
    getBalanceByPlayerWithBonus: jest.fn(),
    credit: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BalanceService,
        { provide: EntityManager, useValue: mockEntityManager },
        { provide: WalletService, useValue: mockWalletService },
      ],
    }).compile();

    service = module.get<BalanceService>(BalanceService);
    entityManager = module.get<EntityManager>(EntityManager);
    walletService = module.get<WalletService>(WalletService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getBalance', () => {
    it('deve retornar o balance com sucesso', async () => {
      const mockSession = { partnerId: 'partner-1', playerId: 'player-1' };
      entityManager.findOne.mockResolvedValue(mockSession);
      jest.spyOn(service, 'getBalanceInWallet').mockResolvedValue(100);
      const result = await service.getBalance(mockGetBalanceDto);
      expect(result).toBe(100);
      expect(entityManager.findOne).toHaveBeenCalled();
      expect(service.getBalanceInWallet).toHaveBeenCalledWith(
        'partner-1',
        'player-1',
      );
    });

    it('deve lançar HttpException quando sessão não encontrada', async () => {
      entityManager.findOne.mockResolvedValue(undefined);
      await expect(service.getBalance(mockGetBalanceDto)).rejects.toThrow(
        'Sessão não encontrada.',
      );
    });
  });

  describe('getBalanceInWallet', () => {
    it('deve retornar o balance do wallet com sucesso', async () => {
      walletService.getBalanceByPlayer.mockResolvedValue({ balance: 200 });
      const result = await service.getBalanceInWallet('partner-1', 'player-1');
      expect(result).toBe(200);
    });

    it('deve lançar HttpException se não encontrar balance', async () => {
      walletService.getBalanceByPlayer.mockResolvedValue({});
      await expect(
        service.getBalanceInWallet('partner-1', 'player-1'),
      ).rejects.toThrow('Erro ao buscar saldo.');
    });

    it('deve lançar HttpException quando response é null', async () => {
      walletService.getBalanceByPlayer.mockResolvedValue(null);
      await expect(
        service.getBalanceInWallet('partner-1', 'player-1'),
      ).rejects.toThrow('Erro ao buscar saldo.');
    });

    it('deve lançar HttpException quando response.balance é undefined', async () => {
      walletService.getBalanceByPlayer.mockResolvedValue({
        balance: undefined,
      });
      await expect(
        service.getBalanceInWallet('partner-1', 'player-1'),
      ).rejects.toThrow('Erro ao buscar saldo.');
    });

    it('deve lançar HttpException se ocorrer erro', async () => {
      walletService.getBalanceByPlayer.mockRejectedValue(
        new Error('Erro externo'),
      );
      await expect(
        service.getBalanceInWallet('partner-1', 'player-1'),
      ).rejects.toThrow('Erro ao buscar saldo.');
    });
  });

  describe('getDetailedBalance', () => {
    it('deve retornar saldo detalhado com sucesso', async () => {
      const mockSession = { partnerId: 'partner-1', playerId: 'player-1' };
      entityManager.findOne.mockResolvedValue(mockSession);
      walletService.getBalanceByPlayerWithBonus.mockResolvedValue({
        realBalance: { balance: 200 },
        bonusBalance: { balance: 50 },
      });
      const result = await service.getDetailedBalance(mockGetBalanceDto);
      expect(result).toEqual({ realBalance: 200, bonusBalance: 50 });
    });

    it('deve retornar 0 quando realBalance é null', async () => {
      const mockSession = { partnerId: 'partner-1', playerId: 'player-1' };
      entityManager.findOne.mockResolvedValue(mockSession);
      walletService.getBalanceByPlayerWithBonus.mockResolvedValue({
        realBalance: null,
        bonusBalance: { balance: 50 },
      });
      const result = await service.getDetailedBalance(mockGetBalanceDto);
      expect(result).toEqual({ realBalance: 0, bonusBalance: 50 });
    });

    it('deve retornar 0 quando bonusBalance é null', async () => {
      const mockSession = { partnerId: 'partner-1', playerId: 'player-1' };
      entityManager.findOne.mockResolvedValue(mockSession);
      walletService.getBalanceByPlayerWithBonus.mockResolvedValue({
        realBalance: { balance: 200 },
        bonusBalance: null,
      });
      const result = await service.getDetailedBalance(mockGetBalanceDto);
      expect(result).toEqual({ realBalance: 200, bonusBalance: 0 });
    });

    it('deve retornar 0 quando realBalance.balance é undefined', async () => {
      const mockSession = { partnerId: 'partner-1', playerId: 'player-1' };
      entityManager.findOne.mockResolvedValue(mockSession);
      walletService.getBalanceByPlayerWithBonus.mockResolvedValue({
        realBalance: { balance: undefined },
        bonusBalance: { balance: 50 },
      });
      const result = await service.getDetailedBalance(mockGetBalanceDto);
      expect(result).toEqual({ realBalance: 0, bonusBalance: 50 });
    });

    it('deve lançar HttpException quando sessão não encontrada', async () => {
      entityManager.findOne.mockResolvedValue(undefined);
      // O erro é capturado e relançado como erro genérico no catch
      await expect(
        service.getDetailedBalance(mockGetBalanceDto),
      ).rejects.toThrow('Erro ao buscar saldo detalhado.');
    });

    it('deve lançar HttpException quando ocorre erro', async () => {
      const mockSession = { partnerId: 'partner-1', playerId: 'player-1' };
      entityManager.findOne.mockResolvedValue(mockSession);
      walletService.getBalanceByPlayerWithBonus.mockRejectedValue(
        new Error('Erro externo'),
      );
      await expect(
        service.getDetailedBalance(mockGetBalanceDto),
      ).rejects.toThrow('Erro ao buscar saldo detalhado.');
    });
  });

  describe('deposit', () => {
    const mockDepositDto = {
      playerId: 'player-1',
      gameId: 'game-1',
      amount: 100.5,
      transactionId: 123,
      typeId: 1,
    };

    it('deve processar depósito com sucesso', async () => {
      const mockSession = { partnerId: 'partner-1' };
      entityManager.findOne.mockResolvedValue(mockSession);
      entityManager.insert.mockResolvedValue({ identifiers: [{ id: 'tx-1' }] });
      walletService.credit.mockResolvedValue({
        transactionId: 'wallet-tx-1',
        amount: 100.5,
      });

      const result = await service.deposit(mockDepositDto);
      expect(result).toEqual({
        transactionId: 'wallet-tx-1',
        amount: 100.5,
      });
      expect(entityManager.findOne).toHaveBeenCalled();
      expect(entityManager.insert).toHaveBeenCalled();
      expect(walletService.credit).toHaveBeenCalled();
    });

    it('deve lançar HttpException quando sessão não encontrada', async () => {
      entityManager.findOne.mockResolvedValue(undefined);
      await expect(service.deposit(mockDepositDto)).rejects.toThrow(
        'Sessão não encontrada.',
      );
    });

    it('deve relançar HttpException quando erro é HttpException', async () => {
      const mockSession = { partnerId: 'partner-1' };
      entityManager.findOne.mockResolvedValue(mockSession);
      const httpError = new HttpException(
        'Erro específico',
        HttpStatus.BAD_REQUEST,
      );
      walletService.credit.mockRejectedValue(httpError);
      await expect(service.deposit(mockDepositDto)).rejects.toThrow(
        'Erro específico',
      );
    });

    it('deve lançar HttpException genérico quando erro não é HttpException', async () => {
      const mockSession = { partnerId: 'partner-1' };
      entityManager.findOne.mockResolvedValue(mockSession);
      walletService.credit.mockRejectedValue(new Error('Erro genérico'));
      await expect(service.deposit(mockDepositDto)).rejects.toThrow(
        'Erro ao processar depósito.',
      );
    });
  });
});
