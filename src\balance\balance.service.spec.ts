import { Test, TestingModule } from '@nestjs/testing';
import { BalanceService } from './balance.service';
import { WalletService } from '../wallet/wallet.service';
import { EntityManager } from 'typeorm';

const mockGetBalanceDto = {
  playerId: 'player-1',
  sessionId: 'session-1',
};

describe('BalanceService', () => {
  let service: BalanceService;
  let entityManager: any;
  let walletService: any;

  const mockEntityManager = {
    findOne: jest.fn(),
  };
  const mockWalletService = {
    getBalanceByPlayer: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BalanceService,
        { provide: EntityManager, useValue: mockEntityManager },
        { provide: WalletService, useValue: mockWalletService },
      ],
    }).compile();

    service = module.get<BalanceService>(BalanceService);
    entityManager = module.get<EntityManager>(EntityManager);
    walletService = module.get<WalletService>(WalletService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getBalance', () => {
    it('deve retornar o balance com sucesso', async () => {
      const mockSession = { partnerId: 'partner-1', playerId: 'player-1' };
      entityManager.findOne.mockResolvedValue(mockSession);
      jest.spyOn(service, 'getBalanceInWallet').mockResolvedValue(100);
      const result = await service.getBalance(mockGetBalanceDto);
      expect(result).toEqual({ errorCode: 0, balance: 100 });
      expect(entityManager.findOne).toHaveBeenCalled();
      expect(service.getBalanceInWallet).toHaveBeenCalledWith('partner-1', 'player-1');
    });

    it('deve retornar erro se sessão não encontrada', async () => {
      entityManager.findOne.mockResolvedValue(undefined);
      const result = await service.getBalance(mockGetBalanceDto);
      expect(result).toEqual({ errorCode: 1112, errorMsg: 'Session not found.' });
    });
  });

  describe('getBalanceInWallet', () => {
    it('deve retornar o balance do wallet com sucesso', async () => {
      walletService.getBalanceByPlayer.mockResolvedValue({ balance: 200 });
      const result = await service.getBalanceInWallet('partner-1', 'player-1');
      expect(result).toBe(200);
    });

    it('deve lançar HttpException se não encontrar balance', async () => {
      walletService.getBalanceByPlayer.mockResolvedValue({});
      await expect(service.getBalanceInWallet('partner-1', 'player-1')).rejects.toHaveProperty('response.errorCode', 2);
    });

    it('deve lançar HttpException se ocorrer erro', async () => {
      walletService.getBalanceByPlayer.mockRejectedValue(new Error('Erro externo'));
      await expect(service.getBalanceInWallet('partner-1', 'player-1')).rejects.toHaveProperty('response.errorCode', 2);
    });
  });
}); 