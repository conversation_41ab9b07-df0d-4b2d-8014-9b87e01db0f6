import { Modu<PERSON> } from '@nestjs/common';
import { NewSessionService } from './new-session.service';
import { NewSessionController } from './new-session.controller';
import { DatabaseModule } from '../common/database/database.module';
import { HMACModule } from '../hmac/hmac.module';
import { WalletModule } from '../wallet/wallet.module';

@Module({
  imports: [DatabaseModule, HMACModule, WalletModule],
  controllers: [NewSessionController],
  providers: [NewSessionService],
})
export class NewSessionModule {}
