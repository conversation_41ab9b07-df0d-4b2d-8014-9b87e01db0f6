import { Modu<PERSON> } from '@nestjs/common';
import { NewSessionService } from './new-session.service';
import { NewSessionController } from './new-session.controller';
import { DatabaseModule } from '../common/database/database.module';
import { WalletModule } from '../wallet/wallet.module';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { HMACModule } from '../hmac/hmac.module';
@Module({
  imports: [DatabaseModule, HMACModule, JwtModule, WalletModule, ConfigModule],
  controllers: [NewSessionController],
  providers: [NewSessionService],
})
export class NewSessionModule {}
