import { RabbitmqService } from '@h2/rabbitmq';
import { Test, TestingModule } from '@nestjs/testing';
import { EntityManager } from 'typeorm';
import { BalanceService } from '../balance/balance.service';
import { WalletService } from '../wallet/wallet.service';
import { BetSettleService } from './betsettle.service';

const mockCreateBetSettleDto: any = {
  sessionId: 'session-123',
  playerId: 'player-123',
  roundId: 'round-123',
  gameId: 'e3e54764-96b9-484e-946f-fb576e43850f',
  roundEnded: true,
  aggregatorCode: 'AGG',
  currency: 'BRL',
  transactions: [
    { externalId: 'ext-1', amount: '100.5', type: 'bet' },
    { externalId: 'ext-2', amount: '150.75', type: 'win' },
  ],
};

describe('BetSettleService', () => {
  let service: BetSettleService;
  let entityManager: any;
  let walletService: any;

  const mockEntityManager = {
    findOne: jest.fn(),
    find: jest.fn(),
    insert: jest.fn(),
    update: jest.fn(),
    transaction: jest.fn(),
  };
  const mockWalletService = {
    debit: jest.fn(),
    credit: jest.fn(),
    batchOperations: jest.fn(),
    getBalanceByPlayer: jest.fn(),
  };
  const mockRabbitmqService = {
    sendMessage: jest.fn(),
    publish: jest.fn(),
    publishToQueue: jest.fn().mockResolvedValue(undefined),
    connect: jest.fn(),
    disconnect: jest.fn(),
  };
  const mockBalanceService = {
    getBalance: jest.fn(),
    getBalanceInWallet: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BetSettleService,
        { provide: EntityManager, useValue: mockEntityManager },
        { provide: WalletService, useValue: mockWalletService },
        { provide: RabbitmqService, useValue: mockRabbitmqService },
        { provide: BalanceService, useValue: mockBalanceService },
      ],
    }).compile();

    service = module.get<BetSettleService>(BetSettleService);
    entityManager = module.get<EntityManager>(EntityManager);
    walletService = module.get<WalletService>(WalletService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('deve criar um bet settle com winAmount', async () => {
    const mockSession = { partnerId: 'partner-1', id: 'session-123' };
    const mockGame = {
      id: 'e3e54764-96b9-484e-946f-fb576e43850f',
      gameProvider: 'provider-1',
      name: 'Test Game',
      gameProviderId: 'provider-id-1',
      category: { name: 'Slots' },
    };

    entityManager.findOne
      .mockResolvedValueOnce(mockSession) // busca session
      .mockResolvedValueOnce(null) // existingTransaction não existe
      .mockResolvedValueOnce(mockGame); // busca game

    // Mock da transação do TypeORM
    entityManager.transaction.mockImplementation(async callback => {
      const mockTransactionalManager = {
        insert: jest
          .fn()
          .mockResolvedValueOnce({
            generatedMaps: [{ id: 'tx-999' }],
          })
          .mockResolvedValueOnce({
            identifiers: [{ id: 'detail-1' }, { id: 'detail-2' }],
            generatedMaps: [{ id: 'detail-1' }, { id: 'detail-2' }],
          }),
      };
      return callback(mockTransactionalManager);
    });

    entityManager.find.mockResolvedValue([]); // transaction details não existem

    walletService.batchOperations.mockResolvedValue({
      initialBalance: 100,
      endBalance: 200,
      subAccount: { balance: 200, wallet: { currency: 'BRL' } },
    });

    const result = await service.create(mockCreateBetSettleDto, 'aggregator');
    expect(result).toMatchObject({
      id: 'tx-999',
      balance: 200,
      externalId: 'round-123',
    });
    expect(result.transactions).toBeDefined();
    expect(entityManager.findOne).toHaveBeenCalled();
    expect(entityManager.transaction).toHaveBeenCalled();
    expect(walletService.batchOperations).toHaveBeenCalled();
  });

  it('deve criar um bet settle sem winAmount', async () => {
    const mockSession = { partnerId: 'partner-1', id: 'session-123' };
    const mockGame = {
      id: 'e3e54764-96b9-484e-946f-fb576e43850f',
      gameProvider: 'provider-1',
      name: 'Test Game',
      gameProviderId: 'provider-id-1',
      category: { name: 'Slots' },
    };

    entityManager.findOne
      .mockResolvedValueOnce(mockSession)
      .mockResolvedValueOnce(null)
      .mockResolvedValueOnce(mockGame);

    // Mock da transação do TypeORM
    entityManager.transaction.mockImplementation(async callback => {
      const mockTransactionalManager = {
        insert: jest
          .fn()
          .mockResolvedValueOnce({
            generatedMaps: [{ id: 'tx-999' }],
          })
          .mockResolvedValueOnce({
            identifiers: [{ id: 'detail-1' }],
            generatedMaps: [{ id: 'detail-1' }],
          }),
      };
      return callback(mockTransactionalManager);
    });

    entityManager.find.mockResolvedValue([]);

    walletService.batchOperations.mockResolvedValue({
      initialBalance: 100,
      endBalance: 50,
      subAccount: { balance: 50, wallet: { currency: 'BRL' } },
    });

    const dto = {
      ...mockCreateBetSettleDto,
      transactions: [{ externalId: 'ext-1', amount: '100.5', type: 'bet' }],
    };
    const result = await service.create(dto, 'aggregator');
    expect(result).toMatchObject({
      id: 'tx-999',
      balance: 50,
      externalId: 'round-123',
    });
    expect(walletService.batchOperations).toHaveBeenCalled();
  });

  it('deve retornar erro se ocorrer exception', async () => {
    const mockSession = { partnerId: 'partner-1', id: 'session-123' };
    entityManager.findOne
      .mockResolvedValueOnce(mockSession)
      .mockRejectedValue(new Error('DB error'));
    await expect(
      service.create(mockCreateBetSettleDto, 'aggregator'),
    ).rejects.toThrow('DB error');
  });

  it('deve retornar erro customizado se exception tiver response', async () => {
    const mockSession = { partnerId: 'partner-1', id: 'session-123' };
    const error: any = new Error('Erro customizado');
    error.status = 400;
    entityManager.findOne
      .mockResolvedValueOnce(mockSession)
      .mockRejectedValue(error);
    await expect(
      service.create(mockCreateBetSettleDto, 'aggregator'),
    ).rejects.toThrow('Erro customizado');
  });

  it('deve processar quando existingTransaction existe mas não está cancelada', async () => {
    const mockSession = { partnerId: 'partner-1', id: 'session-123' };
    const mockExistingTransaction = {
      id: 'existing-tx-1',
      status: 'pending', // Status diferente de Canceled
    };
    const mockGame = {
      id: 'e3e54764-96b9-484e-946f-fb576e43850f',
      gameProvider: 'provider-1',
      name: 'Test Game',
      gameProviderId: 'provider-id-1',
      category: { name: 'Slots' },
    };

    entityManager.findOne
      .mockResolvedValueOnce(mockSession)
      .mockResolvedValueOnce(mockExistingTransaction) // existingTransaction existe mas não está cancelada
      .mockResolvedValueOnce(mockGame);

    // Quando existingTransaction existe, não cria nova transação (casinoTransactionId != null)
    // Apenas insere os detalhes das transações
    entityManager.transaction.mockImplementation(async callback => {
      const mockTransactionalManager = {
        insert: jest.fn().mockResolvedValueOnce({
          identifiers: [{ id: 'detail-1' }, { id: 'detail-2' }],
          generatedMaps: [{ id: 'detail-1' }, { id: 'detail-2' }],
        }),
      };
      await callback(mockTransactionalManager);
      return {
        casinoTransactionId: 'existing-tx-1',
        detailsResult: {
          identifiers: [{ id: 'detail-1' }, { id: 'detail-2' }],
          generatedMaps: [{ id: 'detail-1' }, { id: 'detail-2' }],
        },
      };
    });

    entityManager.find.mockResolvedValue([]);

    walletService.batchOperations.mockResolvedValue({
      initialBalance: 100,
      endBalance: 200,
      subAccount: { balance: 200, wallet: { currency: 'BRL' } },
    });

    const result = await service.create(mockCreateBetSettleDto, 'aggregator');
    expect(result).toMatchObject({
      id: 'existing-tx-1',
      balance: 200,
      externalId: 'round-123',
    });
    // Não deve chamar createTransactionCanceled
    expect(entityManager.transaction).toHaveBeenCalled();
  });

  it('deve lançar erro quando pendingTransactions está vazio', async () => {
    const mockSession = { partnerId: 'partner-1', id: 'session-123' };

    entityManager.findOne
      .mockResolvedValueOnce(mockSession)
      .mockResolvedValueOnce(null); // existingTransaction não existe

    const dtoWithoutTransactions = {
      ...mockCreateBetSettleDto,
      transactions: [],
    };

    await expect(
      service.create(dtoWithoutTransactions, 'aggregator'),
    ).rejects.toThrow('Nenhuma transação fornecida para liquidação de aposta.');
  });

  it('deve lançar erro quando pendingTransactions é null', async () => {
    const mockSession = { partnerId: 'partner-1', id: 'session-123' };

    entityManager.findOne
      .mockResolvedValueOnce(mockSession)
      .mockResolvedValueOnce(null); // existingTransaction não existe

    const dtoWithNullTransactions = {
      ...mockCreateBetSettleDto,
      transactions: null,
    };

    // O erro ocorre antes da verificação, quando tenta fazer .map() em null
    await expect(
      service.create(dtoWithNullTransactions, 'aggregator'),
    ).rejects.toThrow();
  });

  it('deve processar quando casinoTransactionId existe e há existingTransactionDetails que cobrem todas as transações', async () => {
    const mockSession = { partnerId: 'partner-1', id: 'session-123' };
    const mockExistingTransaction = {
      id: 'existing-tx-1',
      status: 'pending',
    };
    const mockGame = {
      id: 'e3e54764-96b9-484e-946f-fb576e43850f',
      gameProvider: 'provider-1',
      name: 'Test Game',
      gameProviderId: 'provider-id-1',
      category: { name: 'Slots' },
    };
    const mockExistingDetails = [
      { id: 'detail-1', transactionId: 'ext-1' },
      { id: 'detail-2', transactionId: 'ext-2' },
    ];

    entityManager.findOne
      .mockResolvedValueOnce(mockSession)
      .mockResolvedValueOnce(mockExistingTransaction)
      .mockResolvedValueOnce(mockGame);

    // Quando todas as transações já existem, find retorna os detalhes existentes
    entityManager.find.mockResolvedValue(mockExistingDetails);

    mockBalanceService.getBalanceInWallet.mockResolvedValue(150);

    const dtoWithAllTransactionsProcessed = {
      ...mockCreateBetSettleDto,
      transactions: [
        { externalId: 'ext-1', amount: '100.5', type: 'bet' },
        { externalId: 'ext-2', amount: '150.75', type: 'win' },
      ],
    };

    const result = await service.create(
      dtoWithAllTransactionsProcessed,
      'aggregator',
    );
    expect(result).toMatchObject({
      id: 'existing-tx-1',
      balance: 150,
      externalId: 'round-123',
    });
    expect(mockBalanceService.getBalanceInWallet).toHaveBeenCalledWith(
      'partner-1',
      'player-123',
    );
  });
});
