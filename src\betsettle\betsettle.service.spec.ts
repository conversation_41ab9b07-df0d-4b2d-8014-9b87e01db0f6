import { Test, TestingModule } from '@nestjs/testing';
import { BetSettleService } from './betsettle.service';
import { WalletService } from '../wallet/wallet.service';
import { EntityManager } from 'typeorm';

const mockCreateBetSettleDto = {
  sessionId: 'session-123',
  playerId: 'player-123',
  transactionId: 'tx-123',
  requestType: 'RealMoney',
  amount: 100.5,
  winAmount: 150.75,
  gameId: 123,
  roundId: 'round-123',
  roundEnded: true,
};

describe('BetSettleService', () => {
  let service: BetSettleService;
  let entityManager: any;
  let walletService: any;

  const mockEntityManager = {
    findOne: jest.fn(),
    insert: jest.fn(),
  };
  const mockWalletService = {
    debit: jest.fn(),
    credit: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BetSettleService,
        { provide: EntityManager, useValue: mockEntityManager },
        { provide: WalletService, useValue: mockWalletService },
      ],
    }).compile();

    service = module.get<BetSettleService>(BetSettleService);
    entityManager = module.get<EntityManager>(EntityManager);
    walletService = module.get<WalletService>(WalletService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('deve criar um bet settle com winAmount', async () => {
    entityManager.findOne
      .mockResolvedValueOnce({ partnerId: 'partner-1' }) // busca partner
      .mockResolvedValueOnce({ gameProvider: 'provider-1' }); // busca gameProvider
    entityManager.insert.mockResolvedValue({ generatedMaps: [{ id_transaction: 'tx-999' }] });
    walletService.debit.mockResolvedValue({});
    walletService.credit.mockResolvedValue({ subAccount: { balance: 200 } });

    const result = await service.create(mockCreateBetSettleDto);
    expect(result).toEqual({
      balance: 200,
      errorCode: 0,
      casinoTransactionId: 'tx-999',
      reconcileAmount: 100.5,
      reconcileWinAmount: 150.75,
    });
    expect(entityManager.findOne).toHaveBeenCalledTimes(2);
    expect(entityManager.insert).toHaveBeenCalled();
    expect(walletService.debit).toHaveBeenCalled();
    expect(walletService.credit).toHaveBeenCalled();
  });

  it('deve criar um bet settle sem winAmount', async () => {
    entityManager.findOne
      .mockResolvedValueOnce({ partnerId: 'partner-1' })
      .mockResolvedValueOnce({ gameProvider: 'provider-1' });
    entityManager.insert.mockResolvedValue({ generatedMaps: [{ id_transaction: 'tx-999' }] });
    walletService.debit.mockResolvedValue({ subAccount: { balance: 100 } });

    const dto = { ...mockCreateBetSettleDto, winAmount: 0 };
    const result = await service.create(dto);
    expect(result).toEqual({
      balance: 100,
      errorCode: 0,
      casinoTransactionId: 'tx-999',
      reconcileAmount: 100.5,
      reconcileWinAmount: 0,
    });
    expect(walletService.credit).not.toHaveBeenCalled();
  });

  it('deve retornar erro se ocorrer exception', async () => {
    entityManager.findOne.mockRejectedValue(new Error('DB error'));
    const result = await service.create(mockCreateBetSettleDto);
    expect(result.errorCode).toBe(2);
    expect(result.errorMsg).toBe('DB error');
  });

  it('deve retornar erro customizado se exception tiver response', async () => {
    entityManager.findOne.mockRejectedValue({ response: { errorCode: 99, errorMsg: 'Erro customizado' } });
    const result = await service.create(mockCreateBetSettleDto);
    expect(result.errorCode).toBe(99);
    expect(result.errorMsg).toBe('Erro customizado');
  });
}); 