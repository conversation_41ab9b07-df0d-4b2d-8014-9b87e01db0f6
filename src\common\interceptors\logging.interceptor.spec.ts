import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, CallHandler } from '@nestjs/common';
import { LoggingInterceptor } from './logging.interceptor';
import { of, throwError } from 'rxjs';

describe('LoggingInterceptor', () => {
  let interceptor: LoggingInterceptor;

  const createMockExecutionContext = (
    method: string = 'GET',
    url: string = '/test',
    body: any = {},
  ): ExecutionContext => {
    return {
      switchToHttp: () => ({
        getRequest: () => ({
          method,
          url,
          body,
        }),
      }),
    } as any;
  };

  const createMockCallHandler = (response: any): CallHandler => {
    return {
      handle: () => of(response),
    } as CallHandler;
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [LoggingInterceptor],
    }).compile();

    interceptor = module.get<LoggingInterceptor>(LoggingInterceptor);

    // Spy on logger methods
    jest.spyOn(interceptor['logger'], 'log').mockImplementation();
    jest.spyOn(interceptor['logger'], 'error').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(interceptor).toBeDefined();
  });

  describe('intercept', () => {
    it('deve logar início da requisição', done => {
      const context = createMockExecutionContext('GET', '/api/test');
      const next = createMockCallHandler({ data: 'test' });

      interceptor.intercept(context, next).subscribe(() => {
        expect(interceptor['logger'].log).toHaveBeenCalledWith(
          '[GET] /api/test - Started',
        );
        done();
      });
    });

    it('deve logar conclusão da requisição com sucesso', done => {
      const context = createMockExecutionContext('POST', '/api/users');
      const response = { status: 200, data: 'success' };
      const next = createMockCallHandler(response);

      interceptor.intercept(context, next).subscribe(() => {
        expect(interceptor['logger'].log).toHaveBeenCalledWith(
          expect.stringContaining('[POST] /api/users - Completed'),
        );
        expect(interceptor['logger'].log).toHaveBeenCalledWith(
          expect.stringContaining('Status: \n  200'),
        );
        done();
      });
    });

    it('deve logar duração da requisição', done => {
      const context = createMockExecutionContext('GET', '/api/data');
      const next = createMockCallHandler({ data: 'test' });

      interceptor.intercept(context, next).subscribe(() => {
        expect(interceptor['logger'].log).toHaveBeenCalledWith(
          expect.stringMatching(/\[GET\] \/api\/data - Completed \(\d+ms\)/),
        );
        done();
      });
    });

    it('deve logar erro quando requisição falha', done => {
      const context = createMockExecutionContext('DELETE', '/api/resource');
      const error = new Error('Test error');
      const next = {
        handle: () => throwError(() => error),
      } as CallHandler;

      interceptor.intercept(context, next).subscribe({
        error: () => {
          expect(interceptor['logger'].error).toHaveBeenCalledWith(
            expect.stringContaining('[DELETE] /api/resource - Failed'),
          );
          expect(interceptor['logger'].error).toHaveBeenCalledWith(
            expect.stringContaining('Error: Test error'),
          );
          done();
        },
      });
    });

    it('deve logar duração mesmo quando há erro', done => {
      const context = createMockExecutionContext('PUT', '/api/update');
      const error = new Error('Update failed');
      const next = {
        handle: () => throwError(() => error),
      } as CallHandler;

      interceptor.intercept(context, next).subscribe({
        error: () => {
          expect(interceptor['logger'].error).toHaveBeenCalledWith(
            expect.stringMatching(/\[PUT\] \/api\/update - Failed \(\d+ms\)/),
          );
          done();
        },
      });
    });

    it('deve logar diferentes métodos HTTP corretamente', done => {
      const methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
      let completed = 0;

      methods.forEach(method => {
        const context = createMockExecutionContext(method, '/api/test');
        const next = createMockCallHandler({ data: 'test' });

        interceptor.intercept(context, next).subscribe(() => {
          expect(interceptor['logger'].log).toHaveBeenCalledWith(
            expect.stringContaining(`[${method}]`),
          );
          completed++;
          if (completed === methods.length) {
            done();
          }
        });
      });
    });

    it('deve logar URLs diferentes corretamente', done => {
      const urls = ['/api/users', '/api/products', '/api/orders'];
      let completed = 0;

      urls.forEach(url => {
        const context = createMockExecutionContext('GET', url);
        const next = createMockCallHandler({ data: 'test' });

        interceptor.intercept(context, next).subscribe(() => {
          expect(interceptor['logger'].log).toHaveBeenCalledWith(
            expect.stringContaining(url),
          );
          completed++;
          if (completed === urls.length) {
            done();
          }
        });
      });
    });

    it('deve usar status 200 como padrão quando response não tem status', done => {
      const context = createMockExecutionContext('GET', '/api/test');
      const response = { data: 'test' }; // Sem status
      const next = createMockCallHandler(response);

      interceptor.intercept(context, next).subscribe(() => {
        expect(interceptor['logger'].log).toHaveBeenCalledWith(
          expect.stringContaining('Status: \n  200'),
        );
        done();
      });
    });

    it('deve logar status customizado quando presente na resposta', done => {
      const context = createMockExecutionContext('POST', '/api/create');
      const response = { status: 201, data: 'created' };
      const next = createMockCallHandler(response);

      interceptor.intercept(context, next).subscribe(() => {
        expect(interceptor['logger'].log).toHaveBeenCalledWith(
          expect.stringContaining('Status: \n  201'),
        );
        done();
      });
    });

    it('deve retornar o observable sem modificar a resposta', done => {
      const context = createMockExecutionContext('GET', '/api/test');
      const expectedResponse = { data: 'test', id: 123 };
      const next = createMockCallHandler(expectedResponse);

      interceptor.intercept(context, next).subscribe(response => {
        expect(response).toEqual(expectedResponse);
        done();
      });
    });

    it('deve propagar erro após logar', done => {
      const context = createMockExecutionContext('GET', '/api/error');
      const error = new Error('Expected error');
      const next = {
        handle: () => throwError(() => error),
      } as CallHandler;

      interceptor.intercept(context, next).subscribe({
        error: err => {
          expect(err).toBe(error);
          expect(interceptor['logger'].error).toHaveBeenCalled();
          done();
        },
      });
    });
  });
});
