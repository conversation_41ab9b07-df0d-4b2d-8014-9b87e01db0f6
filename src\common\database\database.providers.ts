import { DataSource } from 'typeorm';

export const databaseProviders = [
  {
    provide: 'CASINO_CONNECTION',
    useFactory: () => {
      return new DataSource({
        type: 'postgres',
        host: process.env.DATABASE_HOST,
        port: +process.env.DATABASE_PORT,
        username: process.env.DATABASE_USER,
        password: process.env.DATABASE_PASSWORD,
        database: process.env.DATABASE_DB_CASINO,
        entities: [__dirname + '/../../**/*.entity{.ts,.js}'],
        synchronize: false,
        ssl: true,
        extra: {
          ssl: {
            rejectUnauthorized: false,
          },
        },
      });
    },
  },
  {
    provide: 'CASINO_ENTITY_MANAGER',
    useFactory: (casinoDataSource: DataSource) => casinoDataSource.manager,
    inject: ['CASINO_CONNECTION'],
  },
];
