import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsString, IsUUID } from 'class-validator';

export class ResponsePromoWinDto {
  @ApiProperty({ example: '0.01' })
  @IsString()
  balance: string;

  @ApiProperty({ example: '0.01' })
  @IsString()
  bonus_amount: string;

  @ApiProperty({ example: '6a73e309-4ec8-4b3b-8082-612ba5329f4c' })
  @IsUUID()
  id: string;

  @ApiProperty({ example: '6a19b7f5-6ef8-4a0f-9375-b2832869713f' })
  @IsUUID()
  id_casino: string;

  @ApiProperty({ example: '2022-06-15T15:30:00.000054Z' })
  @IsDateString()
  processed_at: string;
}
