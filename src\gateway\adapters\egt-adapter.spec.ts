import { Test, TestingModule } from '@nestjs/testing';
import { EgtAdapter } from './egt-adapter';
import { IProviderConfig, ProviderOperation } from '../interfaces/provider.interface';

describe('EgtAdapter', () => {
  let adapter: EgtAdapter;
  let config: IProviderConfig;

  beforeEach(async () => {
    config = {
      name: 'EGT',
      version: '1.0.0',
      enabled: true,
      endpoints: {
        balance: '/api/egt/balance',
        bet: '/api/egt/bet',
        win: '/api/egt/win',
      },
      timeout: 5000,
      retries: 3,
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: EgtAdapter,
          useFactory: () => new EgtAdapter(config),
        },
      ],
    }).compile();

    adapter = module.get<EgtAdapter>(EgtAdapter);
  });

  it('should be defined', () => {
    expect(adapter).toBeDefined();
  });

  describe('transformRequest', () => {
    it('should transform EGT balance request correctly', () => {
      const rawRequest = {
        user_id: 'player123',
        game_id: 'game456',
        currency: 'BRL',
        session_id: 'session789',
        timestamp: '2024-01-01T10:00:00Z',
      };

      const result = adapter.transformRequest(rawRequest, ProviderOperation.BALANCE);

      expect(result).toEqual({
        playerId: 'player123',
        gameId: 'game456',
        currency: 'BRL',
        sessionId: 'session789',
        timestamp: new Date('2024-01-01T10:00:00Z'),
        metadata: {
          provider: 'EGT',
          originalRequest: rawRequest,
        },
      });
    });

    it('should transform EGT bet request correctly', () => {
      const rawRequest = {
        user_id: 'player123',
        game_id: 'game456',
        amount: '10.50',
        currency: 'BRL',
        round_id: 'round789',
        session_id: 'session123',
        bet_type: 'normal',
      };

      const result = adapter.transformRequest(rawRequest, ProviderOperation.BET);

      expect(result).toEqual({
        playerId: 'player123',
        gameId: 'game456',
        amount: 10.5,
        currency: 'BRL',
        roundId: 'round789',
        sessionId: 'session123',
        timestamp: expect.any(Date),
        metadata: {
          provider: 'EGT',
          betType: 'normal',
          originalRequest: rawRequest,
        },
      });
    });

    it('should transform EGT win request correctly', () => {
      const rawRequest = {
        user_id: 'player123',
        game_id: 'game456',
        amount: '25.75',
        currency: 'BRL',
        round_id: 'round789',
        bet_transaction_id: 'bet123',
        session_id: 'session123',
        win_type: 'normal',
      };

      const result = adapter.transformRequest(rawRequest, ProviderOperation.WIN);

      expect(result).toEqual({
        playerId: 'player123',
        gameId: 'game456',
        amount: 25.75,
        currency: 'BRL',
        roundId: 'round789',
        betTransactionId: 'bet123',
        sessionId: 'session123',
        timestamp: expect.any(Date),
        metadata: {
          provider: 'EGT',
          winType: 'normal',
          originalRequest: rawRequest,
        },
      });
    });

    it('should throw error for unsupported operation', () => {
      const rawRequest = { user_id: 'player123' };

      expect(() => {
        adapter.transformRequest(rawRequest, 'unsupported');
      }).toThrow('Unsupported operation: unsupported');
    });
  });

  describe('transformResponse', () => {
    it('should transform balance response to EGT format', () => {
      const standardResponse = {
        playerId: 'player123',
        balance: 100.50,
        currency: 'BRL',
        timestamp: new Date('2024-01-01T10:00:00Z'),
        success: true,
      };

      const result = adapter.transformResponse(standardResponse, ProviderOperation.BALANCE);

      expect(result).toEqual({
        status: 'success',
        user_id: 'player123',
        balance: 100.50,
        currency: 'BRL',
        timestamp: '2024-01-01T10:00:00.000Z',
        error_code: null,
        error_message: null,
      });
    });

    it('should transform bet response to EGT format', () => {
      const standardResponse = {
        playerId: 'player123',
        transactionId: 'txn456',
        balance: 90.50,
        currency: 'BRL',
        timestamp: new Date('2024-01-01T10:00:00Z'),
        success: true,
      };

      const result = adapter.transformResponse(standardResponse, ProviderOperation.BET);

      expect(result).toEqual({
        status: 'success',
        user_id: 'player123',
        transaction_id: 'txn456',
        balance: 90.50,
        currency: 'BRL',
        timestamp: '2024-01-01T10:00:00.000Z',
        error_code: null,
        error_message: null,
      });
    });

    it('should transform error response correctly', () => {
      const standardResponse = {
        playerId: 'player123',
        balance: 0,
        currency: 'BRL',
        timestamp: new Date('2024-01-01T10:00:00Z'),
        success: false,
        error: 'Insufficient balance',
      };

      const result = adapter.transformResponse(standardResponse, ProviderOperation.BALANCE);

      expect(result).toEqual({
        status: 'error',
        user_id: 'player123',
        balance: 0,
        currency: 'BRL',
        timestamp: '2024-01-01T10:00:00.000Z',
        error_code: 'BALANCE_ERROR',
        error_message: 'Insufficient balance',
      });
    });
  });

  describe('validateRequest', () => {
    it('should validate balance request correctly', () => {
      const validRequest = {
        user_id: 'player123',
        game_id: 'game456',
      };

      const result = adapter.validateRequest(validRequest, ProviderOperation.BALANCE);
      expect(result).toBe(true);
    });

    it('should reject invalid balance request', () => {
      const invalidRequest = {
        user_id: 'player123',
        // missing game_id
      };

      const result = adapter.validateRequest(invalidRequest, ProviderOperation.BALANCE);
      expect(result).toBe(false);
    });

    it('should validate bet request correctly', () => {
      const validRequest = {
        user_id: 'player123',
        game_id: 'game456',
        amount: '10.50',
        round_id: 'round789',
      };

      const result = adapter.validateRequest(validRequest, ProviderOperation.BET);
      expect(result).toBe(true);
    });

    it('should reject empty request', () => {
      const result = adapter.validateRequest(null, ProviderOperation.BALANCE);
      expect(result).toBe(false);
    });

    it('should reject invalid operation', () => {
      const validRequest = {
        user_id: 'player123',
        game_id: 'game456',
      };

      const result = adapter.validateRequest(validRequest, 'invalid_operation');
      expect(result).toBe(false);
    });
  });

  describe('getProviderInfo', () => {
    it('should return correct provider info', () => {
      const info = adapter.getProviderInfo();

      expect(info).toEqual({
        name: 'EGT',
        version: '1.0.0',
      });
    });
  });
});
