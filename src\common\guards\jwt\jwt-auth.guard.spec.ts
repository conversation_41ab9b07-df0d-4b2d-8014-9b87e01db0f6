import { Test, TestingModule } from '@nestjs/testing';
import { JwtAuthGuard } from './jwt-auth.guard';

describe('JwtAuthGuard', () => {
  let guard: JwtAuthGuard;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [JwtAuthGuard],
    }).compile();

    guard = module.get<JwtAuthGuard>(JwtAuthGuard);
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  it('deve ser uma instância de AuthGuard', () => {
    expect(guard).toBeInstanceOf(JwtAuthGuard);
  });

  it('deve usar estratégia jwt', () => {
    // O guard estende AuthGuard('jwt')
    // Verificamos que foi criado corretamente
    expect(guard.constructor.name).toBe('JwtAuthGuard');
  });
});
