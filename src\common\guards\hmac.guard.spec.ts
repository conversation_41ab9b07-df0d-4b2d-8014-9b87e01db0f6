import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, HttpException, HttpStatus } from '@nestjs/common';
import { HMACGuard } from './hmac.guard';
import { HMACService } from '../../hmac/service/hmac.service';

describe('HMACGuard', () => {
  let guard: HMACGuard;
  let hmacService: HMACService;

  const mockHMACService = {
    validateHMAC: jest.fn(),
  };

  const createMockExecutionContext = (
    rawBody: string,
    checksum?: string,
  ): ExecutionContext => {
    return {
      switchToHttp: () => ({
        getRequest: () => ({
          rawBody,
          headers: {
            'x-checksum': checksum,
          },
        }),
      }),
    } as any;
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HMACGuard,
        { provide: HMACService, useValue: mockHMACService },
      ],
    }).compile();

    guard = module.get<HMACGuard>(HMACGuard);
    hmacService = module.get<HMACService>(HMACService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  describe('canActivate', () => {
    it('deve retornar true quando HMAC é válido', () => {
      const rawBody = '{"playerId":"123","amount":100}';
      const checksum = 'valid-hmac-checksum';

      mockHMACService.validateHMAC.mockReturnValue(true);

      const context = createMockExecutionContext(rawBody, checksum);
      const result = guard.canActivate(context);

      expect(result).toBe(true);
      expect(hmacService.validateHMAC).toHaveBeenCalledWith(rawBody, checksum);
    });

    it('deve lançar HttpException com erro 1102 quando HMAC é inválido', () => {
      const rawBody = '{"playerId":"123","amount":100}';
      const checksum = 'invalid-hmac-checksum';

      mockHMACService.validateHMAC.mockReturnValue(false);

      const context = createMockExecutionContext(rawBody, checksum);

      expect(() => guard.canActivate(context)).toThrow(
        new HttpException(
          {
            errorCode: 1102,
            errorMsg: 'HMAC Unauthorized.',
          },
          HttpStatus.UNAUTHORIZED,
        ),
      );
      expect(hmacService.validateHMAC).toHaveBeenCalledWith(rawBody, checksum);
    });

    it('deve lançar HttpException quando header x-checksum está ausente', () => {
      const rawBody = '{"playerId":"123","amount":100}';

      mockHMACService.validateHMAC.mockReturnValue(false);

      const context = createMockExecutionContext(rawBody, undefined);

      expect(() => guard.canActivate(context)).toThrow(HttpException);
      expect(hmacService.validateHMAC).toHaveBeenCalledWith(rawBody, undefined);
    });

    it('deve validar HMAC com rawBody vazio', () => {
      const rawBody = '';
      const checksum = 'checksum-for-empty-body';

      mockHMACService.validateHMAC.mockReturnValue(true);

      const context = createMockExecutionContext(rawBody, checksum);
      const result = guard.canActivate(context);

      expect(result).toBe(true);
      expect(hmacService.validateHMAC).toHaveBeenCalledWith(rawBody, checksum);
    });

    it('deve validar HMAC com diferentes payloads', () => {
      const testCases = [
        {
          rawBody: '{"test":"data1"}',
          checksum: 'checksum1',
          valid: true,
        },
        {
          rawBody: '{"test":"data2"}',
          checksum: 'checksum2',
          valid: false,
        },
        {
          rawBody: '{"complex":{"nested":"value"}}',
          checksum: 'checksum3',
          valid: true,
        },
      ];

      testCases.forEach(({ rawBody, checksum, valid }) => {
        mockHMACService.validateHMAC.mockReturnValue(valid);

        const context = createMockExecutionContext(rawBody, checksum);

        if (valid) {
          const result = guard.canActivate(context);
          expect(result).toBe(true);
        } else {
          expect(() => guard.canActivate(context)).toThrow(HttpException);
        }

        expect(hmacService.validateHMAC).toHaveBeenCalledWith(
          rawBody,
          checksum,
        );

        jest.clearAllMocks();
      });
    });

    it('deve retornar erro com código de status UNAUTHORIZED', () => {
      const rawBody = '{"data":"test"}';
      const checksum = 'bad-checksum';

      mockHMACService.validateHMAC.mockReturnValue(false);

      const context = createMockExecutionContext(rawBody, checksum);

      try {
        guard.canActivate(context);
        fail('Should have thrown an exception');
      } catch (error) {
        expect(error).toBeInstanceOf(HttpException);
        expect(error.getStatus()).toBe(HttpStatus.UNAUTHORIZED);
        expect(error.getResponse()).toEqual({
          errorCode: 1102,
          errorMsg: 'HMAC Unauthorized.',
        });
      }
    });

    it('deve chamar validateHMAC exatamente uma vez por requisição', () => {
      const rawBody = '{"userId":"user-123"}';
      const checksum = 'valid-checksum';

      mockHMACService.validateHMAC.mockReturnValue(true);

      const context = createMockExecutionContext(rawBody, checksum);
      guard.canActivate(context);

      expect(hmacService.validateHMAC).toHaveBeenCalledTimes(1);
    });

    it('deve validar HMAC com caracteres especiais no body', () => {
      const rawBody = '{"special":"ação€£¥"}';
      const checksum = 'special-checksum';

      mockHMACService.validateHMAC.mockReturnValue(true);

      const context = createMockExecutionContext(rawBody, checksum);
      const result = guard.canActivate(context);

      expect(result).toBe(true);
      expect(hmacService.validateHMAC).toHaveBeenCalledWith(rawBody, checksum);
    });
  });
});
