import { BalanceModule } from '@/balance/balance.module';
import { CasinoGames } from '@/casino-games/entities/casino-game.entity';
import { CasinoTransactionDetailsEntity } from '@/casino-transaction/controller/entities/casino-transaction-details.entity';
import { CasinoTransactionEntity } from '@/casino-transaction/controller/entities/casino-transactions.entity';
import { WalletModule } from '@/wallet/wallet.module';
import { RabbitmqModule } from '@h2/rabbitmq';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RollbackController } from './rollback.controller';
import { RollbackService } from './rollback.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      CasinoTransactionEntity,
      CasinoTransactionDetailsEntity,
      CasinoGames,
    ]),
    WalletModule,
    RabbitmqModule,
    BalanceModule,
  ],
  controllers: [RollbackController],
  providers: [RollbackService],
  exports: [RollbackService],
})
export class RollbackModule {}
