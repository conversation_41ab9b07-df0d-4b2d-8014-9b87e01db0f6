import { Test, TestingModule } from '@nestjs/testing';
import { DailyReportService } from './daily-report.service';
import { EntityManager } from 'typeorm';
import { DateUtilsService } from '@/common/utils/date';

describe('DailyReportService', () => {
  let service: DailyReportService;
  let entityManager: any;
  let dateService: any;

  const mockEntityManager = {
    createQueryBuilder: jest.fn(),
    findOne: jest.fn(),
    findOneBy: jest.fn(),
    update: jest.fn(),
    insert: jest.fn(),
    find: jest.fn(),
    count: jest.fn(),
    query: jest.fn(),
  };

  const mockDateService = {
    getDateRange: jest.fn(),
  };

  const mockRequest = {
    headers: {
      'partner-id': 'partner-1',
    },
  } as any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DailyReportService,
        { provide: EntityManager, useValue: mockEntityManager },
        { provide: DateUtilsService, useValue: mockDateService },
      ],
    }).compile();

    service = module.get<DailyReportService>(DailyReportService);
    entityManager = module.get<EntityManager>(EntityManager);
    dateService = module.get<DateUtilsService>(DateUtilsService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('saveDailyReportGame', () => {
    it('deve salvar relatório diário de jogos com sucesso', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            gameId: 'game-1',
            gameName: 'Game 1',
            gameProvider: 'provider-1',
            partnerId: 'partner-1',
            totalBetAmount: '1000',
            totalWinAmount: '800',
            totalBets: '10',
            totalPlayer: '5',
          },
        ]),
      };

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-01'),
      });
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockEntityManager.findOne.mockResolvedValue(null);
      mockEntityManager.insert.mockResolvedValue({});

      await service.saveDailyReportGame();

      expect(mockEntityManager.insert).toHaveBeenCalled();
    });

    it('deve atualizar relatório existente', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            gameId: 'game-1',
            gameName: 'Game 1',
            gameProvider: 'provider-1',
            partnerId: 'partner-1',
            totalBetAmount: '1000',
            totalWinAmount: '800',
            totalBets: '10',
            totalPlayer: '5',
          },
        ]),
      };

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-01'),
      });
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockEntityManager.findOne.mockResolvedValue({ id: 'existing-id' });
      mockEntityManager.update.mockResolvedValue({});

      await service.saveDailyReportGame();

      expect(mockEntityManager.update).toHaveBeenCalled();
    });

    it('deve tratar erro durante salvamento', async () => {
      mockDateService.getDateRange.mockImplementation(() => {
        throw new Error('Erro no date service');
      });

      await expect(service.saveDailyReportGame()).resolves.not.toThrow();
    });
  });

  describe('getDailyReportGame', () => {
    it('deve retornar relatório de jogos com dados históricos', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
      };

      mockEntityManager.find.mockResolvedValue([
        {
          gameId: 'game-1',
          gameName: 'Game 1',
          totalBet: 1000,
          totalSettle: 800,
        },
      ]);
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await service.getDailyReportGame(
        '2024-01-01',
        '2024-01-02',
        mockRequest
      );

      expect(result).toBeDefined();
    });

    it('deve retornar relatório com filtro de provider', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
      };

      mockEntityManager.find.mockResolvedValue([]);
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await service.getDailyReportGame(
        '2024-01-01',
        '2024-01-02',
        mockRequest,
        'provider-1'
      );

      expect(result).toBeDefined();
    });
  });

  describe('getDailyReportGamePagination', () => {
    it('deve retornar relatório paginado de jogos', async () => {
      const filter = {
        page: 1,
        limit: 10,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        gameId: '',
        provider: '',
        accountType: '',
        orderBy: '',
        sortOrder: 'ASC' as any,
      };

      const mockDataQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            partnerId: 'partner-1',
            gameId: 'game-1',
            gameName: 'Game 1',
            gameProvider: 'provider-1',
            currency: 'BRL',
            totalBet: '1000',
            totalSettle: '800',
            ggr: '200',
            totalRound: '10',
            totalPlayers: '5',
          },
        ]),
      };

      const mockCountQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ totalItems: '1' }),
      };

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-02'),
      });

      // Mock para retornar diferentes QueryBuilders baseado no que é chamado
      mockEntityManager.createQueryBuilder
        .mockReturnValueOnce(mockDataQueryBuilder)
        .mockReturnValueOnce(mockCountQueryBuilder);

      const result = await service.getDailyReportGamePagination(filter, mockRequest);

      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('totalItems');
      expect(result).toHaveProperty('totalPages');
    });
  });

  describe('saveDailyReportGamePlayerPartner', () => {
    it('deve salvar relatório de jogos por jogador e parceiro', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            gameId: 'game-1',
            playerId: 'player-1',
            partnerId: 'partner-1',
            totalBetAmount: '1000',
            totalWinAmount: '800',
          },
        ]),
      };

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-01'),
      });
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockEntityManager.findOneBy.mockResolvedValue(null);
      mockEntityManager.insert.mockResolvedValue({});

      await service.saveDailyReportGamePlayerPartner();

      expect(mockEntityManager.insert).toHaveBeenCalled();
    });
  });

  describe('getDailyReportGamePlayerPartner', () => {
    it('deve retornar relatório de jogos por jogador e parceiro', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
      };

      mockEntityManager.find.mockResolvedValue([
        {
          gameId: 'game-1',
          playerId: 'player-1',
          totalBet: 1000,
        },
      ]);
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await service.getDailyReportGamePlayerPartner(
        '2024-01-01',
        '2024-01-02',
        mockRequest
      );

      expect(result).toBeDefined();
    });
  });

  describe('getDailyReportGamePlayerPartnerPagination', () => {
    it('deve retornar relatório paginado de jogos por jogador e parceiro', async () => {
      const filter = {
        page: 1,
        limit: 10,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        gameId: '',
        provider: '',
        playerId: '',
        orderBy: '',
        sortOrder: 'ASC' as any,
      };

      const mockDataQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            playerId: 'player-1',
            gameId: 'game-1',
            gameName: 'Game 1',
            gameProvider: 'provider-1',
            totalBet: '1000',
            totalSettle: '800',
          },
        ]),
      };

      const mockCountQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ totalItems: '1' }),
      };

      // Mock para retornar diferentes QueryBuilders baseado no que é chamado
      mockEntityManager.createQueryBuilder
        .mockReturnValueOnce(mockDataQueryBuilder)
        .mockReturnValueOnce(mockCountQueryBuilder);

      const result = await service.getDailyReportGamePlayerPartnerPagination(
        filter,
        mockRequest
      );

      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('totalItems');
    });
  });

  describe('saveDailyReportPlayer', () => {
    it('deve salvar relatório diário de jogadores', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            playerId: 'player-1',
            partnerId: 'partner-1',
            totalBetAmount: '1000',
            totalWinAmount: '800',
            totalBets: '10',
          },
        ]),
      };

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-01'),
      });
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockEntityManager.findOneBy.mockResolvedValue(null);
      mockEntityManager.insert.mockResolvedValue({});

      await service.saveDailyReportPlayer();

      expect(mockEntityManager.insert).toHaveBeenCalled();
    });
  });

  describe('getDailyReportPlayer', () => {
    it('deve retornar relatório de jogadores', async () => {
      const filter = {
        page: 1,
        limit: 10,
        pageSize: 10,
        partners: 'partner-1',
        orderBy: '',
        sortOrder: 'ASC' as any,
      };

      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            playerId: 'player-1',
            totalBet: '1000',
            totalSettle: '800',
          },
        ]),
        getRawOne: jest.fn().mockResolvedValue({
          playerId: 'player-1',
          totalBet: '1000',
          totalSettle: '800',
        }),
      };

      mockEntityManager.find.mockResolvedValue([
        {
          playerId: 'player-1',
          totalBet: 1000,
          totalSettle: 800,
        },
      ]);
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await service.getDailyReportPlayer(
        '2024-01-01',
        '2024-01-02',
        filter,
        mockRequest,
        'player-1',
        '<EMAIL>'
      );

      expect(result).toBeDefined();
    });
  });

  describe('getDailyReportPlayerById', () => {
    it('deve retornar relatório de jogador específico', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            playerId: 'player-1',
            totalBet: 1000,
            totalSettle: 800,
          },
        ]),
      };

      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await service.getDailyReportPlayerById('player-1', mockRequest);

      expect(result).toBeDefined();
    });
  });

  describe('saveDailyReportProviders', () => {
    it('deve salvar relatório diário de provedores', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            provider: 'prov-1',
            partnerId: 'partner-1',
            totalBetAmount: '1000',
            totalWinAmount: '800',
          },
        ]),
      };
      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-01'),
      });
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockEntityManager.findOneBy.mockResolvedValue(null);
      mockEntityManager.insert.mockResolvedValue({});
      await service.saveDailyReportProviders();
      expect(mockEntityManager.insert).toHaveBeenCalled();
    });
  });

  describe('getDailyReportProviders', () => {
    it('deve retornar relatório de provedores', async () => {
      mockEntityManager.find.mockResolvedValue([
        { provider: 'prov-1', totalBet: 1000, totalSettle: 800 },
      ]);
      const result = await service.getDailyReportProviders('2024-01-01', '2024-01-02', mockRequest);
      expect(result).toBeDefined();
    });
  });

  describe('getDailyReportProvidersPagination', () => {
    it('deve lançar BadRequestException se datas não forem fornecidas', async () => {
      const filter = { page: 1, pageSize: 10, partners: 'p1' };
      await expect(
        service.getDailyReportProvidersPagination('', '', filter, mockRequest)
      ).rejects.toThrow('É necessário informar a data inicial e final');
    });
    it('deve lançar erro se a query falhar', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        orderBy: '',
        sortOrder: 'ASC' as any,
      };
      mockEntityManager.query.mockRejectedValueOnce(new Error('Erro DB'));
      await expect(
        service.getDailyReportProvidersPagination('2024-01-01', '2024-01-02', filter, mockRequest)
      ).rejects.toThrow('Erro DB');
    });
    it('deve usar orderBy padrão se valor não aceito', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        orderBy: 'invalido',
        sortOrder: 'ASC' as any,
      };
      mockEntityManager.query.mockResolvedValue([{ totalItems: '1' }]);
      mockEntityManager.query.mockResolvedValueOnce([]);
      mockEntityManager.query.mockResolvedValueOnce([{ totalItems: '1' }]);
      await service.getDailyReportProvidersPagination('2024-01-01', '2024-01-02', filter, mockRequest);
      expect(filter.orderBy).toBe('gameProvider');
    });
  });

  describe('saveDailyReportPartner', () => {
    it('deve logar erro se ocorrer exceção', async () => {
      mockEntityManager.createQueryBuilder.mockImplementation(() => { throw new Error('Erro'); });
      const loggerSpy = jest.spyOn(service['logger'], 'error').mockImplementation();
      await service.saveDailyReportPartner();
      expect(loggerSpy).toHaveBeenCalled();
      loggerSpy.mockRestore();
    });
  });

  describe('getDailyReportPartner', () => {
    it('deve retornar array vazio se não houver dados', async () => {
      mockEntityManager.find.mockResolvedValue([]);
      const result = await service.getDailyReportPartner('2024-01-01', '2024-01-02', mockRequest);
      expect(result).toEqual([]);
    });
  });

  describe('getDailyReportPartnerPagination', () => {
    it('deve lançar BadRequestException se datas não forem fornecidas', async () => {
      const filter = { page: 1, pageSize: 10, partners: 'p1' };
      await expect(
        service.getDailyReportPartnerPagination('', '', filter, mockRequest)
      ).rejects.toThrow('É necessário informar a data inicial e final');
    });
    it('deve lançar erro se a query falhar', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        orderBy: '',
        sortOrder: 'ASC' as any,
      };
      mockEntityManager.query.mockRejectedValueOnce(new Error('Erro DB'));
      await expect(
        service.getDailyReportPartnerPagination('2024-01-01', '2024-01-02', filter, mockRequest)
      ).rejects.toThrow('Erro DB');
    });
    it('deve usar orderBy padrão se valor não aceito', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        orderBy: 'invalido',
        sortOrder: 'ASC' as any,
      };
      mockEntityManager.query.mockResolvedValue([{ totalItems: '1' }]);
      mockEntityManager.query.mockResolvedValueOnce([]);
      mockEntityManager.query.mockResolvedValueOnce([{ totalItems: '1' }]);
      await service.getDailyReportPartnerPagination('2024-01-01', '2024-01-02', filter, mockRequest);
      expect(filter.orderBy).toBe('partnerId');
    });
  });

  describe('findGameProfitability', () => {
    it('deve retornar dados de rentabilidade de jogos', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        orderBy: '',
        sortOrder: 'ASC' as any,
        dateRange: { from: '2024-01-01', to: '2024-01-02' },
      };
      const mockDataQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          { gameId: 'game-1', ggr: 200 },
        ]),
      };
      const mockCountQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ totalItems: '1' }),
      };
      mockEntityManager.createQueryBuilder
        .mockReturnValueOnce(mockDataQueryBuilder)
        .mockReturnValueOnce(mockCountQueryBuilder);
      mockEntityManager.query.mockResolvedValue([{ totalItems: '1' }]);
      const result = await service.findGameProfitability(filter, 'partner-1');
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('totalItems');
    });
  });
}); 