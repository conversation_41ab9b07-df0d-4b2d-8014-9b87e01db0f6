import { Test, TestingModule } from '@nestjs/testing';
import { DailyReportService } from './daily-report.service';
import { EntityManager } from 'typeorm';
import { DateUtilsService } from '@/common/utils/date';

describe('DailyReportService', () => {
  let service: DailyReportService;

  const mockEntityManager = {
    createQueryBuilder: jest.fn(),
    findOne: jest.fn(),
    findOneBy: jest.fn(),
    update: jest.fn(),
    insert: jest.fn(),
    find: jest.fn(),
    count: jest.fn(),
    query: jest.fn(),
  };

  const mockDateService = {
    getDateRange: jest.fn(),
  };

  const mockRequest = {
    headers: {
      'partner-id': 'partner-1',
    },
  } as any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DailyReportService,
        { provide: EntityManager, useValue: mockEntityManager },
        { provide: DateUtilsService, useValue: mockDateService },
      ],
    }).compile();

    service = module.get<DailyReportService>(DailyReportService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('saveDailyReportGame', () => {
    it('deve salvar relatório diário de jogos com sucesso', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            gameId: 'game-1',
            gameName: 'Game 1',
            gameProvider: 'provider-1',
            partnerId: 'partner-1',
            totalBetAmount: '1000',
            totalWinAmount: '800',
            totalBets: '10',
            totalPlayer: '5',
          },
        ]),
      };

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-01'),
      });
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockEntityManager.findOne.mockResolvedValue(null);
      mockEntityManager.insert.mockResolvedValue({});

      await service.saveDailyReportGame();

      expect(mockEntityManager.insert).toHaveBeenCalled();
    });

    it('deve atualizar relatório existente', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            gameId: 'game-1',
            gameName: 'Game 1',
            gameProvider: 'provider-1',
            partnerId: 'partner-1',
            totalBetAmount: '1000',
            totalWinAmount: '800',
            totalBets: '10',
            totalPlayer: '5',
          },
        ]),
      };

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-01'),
      });
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockEntityManager.findOne.mockResolvedValue({ id: 'existing-id' });
      mockEntityManager.update.mockResolvedValue({});

      await service.saveDailyReportGame();

      expect(mockEntityManager.update).toHaveBeenCalled();
    });

    it('deve tratar erro durante salvamento', async () => {
      mockDateService.getDateRange.mockImplementation(() => {
        throw new Error('Erro no date service');
      });

      await expect(service.saveDailyReportGame()).resolves.not.toThrow();
    });

    it('deve processar múltiplos jogos no relatório', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            gameId: 'game-1',
            gameName: 'Game 1',
            gameProvider: 'provider-1',
            partnerId: 'partner-1',
            totalBetAmount: '1000',
            totalWinAmount: '800',
            totalBets: '10',
            totalPlayer: '5',
          },
          {
            gameId: 'game-2',
            gameName: 'Game 2',
            gameProvider: 'provider-2',
            partnerId: 'partner-1',
            totalBetAmount: '2000',
            totalWinAmount: '1500',
            totalBets: '20',
            totalPlayer: '8',
          },
        ]),
      };

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-01'),
      });
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockEntityManager.findOne.mockResolvedValue(null);
      mockEntityManager.insert.mockResolvedValue({});

      await service.saveDailyReportGame();

      expect(mockEntityManager.insert).toHaveBeenCalledTimes(2);
    });

    it('deve calcular GGR corretamente', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            gameId: 'game-1',
            gameName: 'Game 1',
            gameProvider: 'provider-1',
            partnerId: 'partner-1',
            totalBetAmount: '1000',
            totalWinAmount: '800',
            totalBets: '10',
            totalPlayer: '5',
          },
        ]),
      };

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-01'),
      });
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockEntityManager.findOne.mockResolvedValue(null);
      mockEntityManager.insert.mockResolvedValue({});

      await service.saveDailyReportGame();

      expect(mockEntityManager.insert).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          ggr: 200, // 1000 - 800
        })
      );
    });

    it('deve tratar valores nulos nos dados do relatório', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            gameId: 'game-1',
            gameName: null,
            gameProvider: null,
            partnerId: 'partner-1',
            totalBetAmount: null,
            totalWinAmount: null,
            totalBets: null,
            totalPlayer: null,
          },
        ]),
      };

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-01'),
      });
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockEntityManager.findOne.mockResolvedValue(null);
      mockEntityManager.insert.mockResolvedValue({});

      await service.saveDailyReportGame();

      expect(mockEntityManager.insert).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          totalBet: 0,
          totalSettle: 0,
          ggr: 0,
          totalRound: 0,
          totalPlayers: 0,
        })
      );
    });

    it('deve tratar erro durante inserção no banco', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            gameId: 'game-1',
            gameName: 'Game 1',
            gameProvider: 'provider-1',
            partnerId: 'partner-1',
            totalBetAmount: '1000',
            totalWinAmount: '800',
            totalBets: '10',
            totalPlayer: '5',
          },
        ]),
      };

      const loggerSpy = jest.spyOn(service['logger'], 'error').mockImplementation();

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-01'),
      });
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockEntityManager.findOne.mockResolvedValue(null);
      mockEntityManager.insert.mockRejectedValue(new Error('Database error'));

      await service.saveDailyReportGame();

      expect(loggerSpy).toHaveBeenCalled();
      loggerSpy.mockRestore();
    });
  });

  describe('getDailyReportGame', () => {
    it('deve retornar relatório de jogos com dados históricos', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
      };

      mockEntityManager.find.mockResolvedValue([
        {
          gameId: 'game-1',
          gameName: 'Game 1',
          totalBet: 1000,
          totalSettle: 800,
        },
      ]);
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await service.getDailyReportGame(
        '2024-01-01',
        '2024-01-02',
        mockRequest
      );

      expect(result).toBeDefined();
    });

    it('deve retornar relatório com filtro de provider', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
      };

      mockEntityManager.find.mockResolvedValue([]);
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await service.getDailyReportGame(
        '2024-01-01',
        '2024-01-02',
        mockRequest,
        'provider-1'
      );

      expect(result).toBeDefined();
    });

    it('deve tratar erro durante consulta', async () => {
      // Mock para simular erro na busca de dados históricos
      mockEntityManager.find.mockRejectedValue(new Error('Database error'));

      // Mock para simular que não é hoje
      const pastDate = '2024-01-01';

      await expect(
        service.getDailyReportGame(pastDate, pastDate, mockRequest)
      ).rejects.toThrow('Erro ao buscar report game');
    });

    it('deve combinar dados históricos e do dia atual', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            gameId: 'game-today',
            gameName: 'Today Game',
            gameProvider: 'provider-1',
            partnerId: 'partner-1',
            totalBetAmount: '500',
            totalWinAmount: '400',
            totalBets: '5',
            totalPlayer: '3',
          },
        ]),
      };

      const historicalData = [
        {
          gameId: 'game-historical',
          gameName: 'Historical Game',
          totalBet: 1000,
          totalSettle: 800,
        },
      ];

      mockEntityManager.find.mockResolvedValue(historicalData);
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      // Simular período que inclui ontem e hoje
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const today = new Date();

      const result = await service.getDailyReportGame(
        yesterday.toISOString().split('T')[0],
        today.toISOString().split('T')[0],
        mockRequest
      );

      expect(result).toHaveLength(2); // 1 histórico + 1 do dia atual
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ gameId: 'game-historical' }),
          expect.objectContaining({ gameId: 'game-today' }),
        ])
      );
    });

    it('deve aplicar filtro de gameProvider corretamente', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
      };

      mockEntityManager.find.mockResolvedValue([]);
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      // Simular período que inclui hoje para ativar a query com gameProvider
      const today = new Date().toISOString().split('T')[0];

      await service.getDailyReportGame(
        today,
        today,
        mockRequest,
        'PRAGMATIC'
      );

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'game.gameProvider = :gameProvider',
        { gameProvider: 'PRAGMATIC' }
      );
    });

    it('deve retornar array vazio quando não há dados', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
      };

      mockEntityManager.find.mockResolvedValue([]);
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await service.getDailyReportGame(
        '2024-01-01',
        '2024-01-02',
        mockRequest
      );

      expect(result).toEqual([]);
    });
  });

  describe('getDailyReportGamePagination', () => {
    it('deve retornar relatório paginado de jogos', async () => {
      const filter = {
        page: 1,
        limit: 10,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        gameId: '',
        provider: '',
        accountType: '',
        orderBy: '',
        sortOrder: 'ASC' as any,
      };

      const mockDataQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            partnerId: 'partner-1',
            gameId: 'game-1',
            gameName: 'Game 1',
            gameProvider: 'provider-1',
            currency: 'BRL',
            totalBet: '1000',
            totalSettle: '800',
            ggr: '200',
            totalRound: '10',
            totalPlayers: '5',
          },
        ]),
      };

      const mockCountQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ totalItems: '1' }),
      };

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-02'),
      });

      // Mock para retornar diferentes QueryBuilders baseado no que é chamado
      mockEntityManager.createQueryBuilder
        .mockReturnValueOnce(mockDataQueryBuilder)
        .mockReturnValueOnce(mockCountQueryBuilder);

      const result = await service.getDailyReportGamePagination(filter, mockRequest);

      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('totalItems');
      expect(result).toHaveProperty('totalPages');
    });

    it('deve lançar BadRequestException quando fromDate não for fornecida', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        fromDate: '',
        toDate: '2024-01-02',
        gameId: '',
        provider: '',
        accountType: '',
        orderBy: '',
        sortOrder: 'ASC' as any,
        partners: 'partner-1',
      };

      await expect(
        service.getDailyReportGamePagination(filter, mockRequest)
      ).rejects.toThrow('É necessário informar a data inicial e final');
    });

    it('deve lançar BadRequestException quando toDate não for fornecida', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        fromDate: '2024-01-01',
        toDate: '',
        gameId: '',
        provider: '',
        accountType: '',
        orderBy: '',
        sortOrder: 'ASC' as any,
        partners: 'partner-1',
      };

      await expect(
        service.getDailyReportGamePagination(filter, mockRequest)
      ).rejects.toThrow('É necessário informar a data inicial e final');
    });

    it('deve usar valores padrão para paginação quando não fornecidos', async () => {
      const filter = {
        page: 0, // Valor inválido
        pageSize: 0, // Valor inválido
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        gameId: '',
        provider: '',
        accountType: '',
        orderBy: 'invalidField', // Campo inválido
        sortOrder: undefined as any,
        partners: 'partner-1',
      };

      const mockDataQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
      };

      const mockCountQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ totalItems: '0' }),
      };

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-02'),
      });

      mockEntityManager.createQueryBuilder
        .mockReturnValueOnce(mockDataQueryBuilder)
        .mockReturnValueOnce(mockCountQueryBuilder);

      const result = await service.getDailyReportGamePagination(filter, mockRequest);

      expect(result.currentPage).toBe(1); // Valor padrão
      expect(result.pageSize).toBe(10); // Valor padrão
      expect(mockDataQueryBuilder.orderBy).toHaveBeenCalledWith('"gameId"', 'ASC'); // Campo e ordem padrão
    });

    it('deve aplicar filtros opcionais corretamente', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        gameId: 'game-123',
        provider: 'PRAGMATIC',
        accountType: 'REAL',
        orderBy: 'gameName',
        sortOrder: 'DESC' as any,
        partners: 'partner-1',
      };

      const mockDataQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
      };

      const mockCountQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ totalItems: '0' }),
      };

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-02'),
      });

      mockEntityManager.createQueryBuilder
        .mockReturnValueOnce(mockDataQueryBuilder)
        .mockReturnValueOnce(mockCountQueryBuilder);

      await service.getDailyReportGamePagination(filter, mockRequest);

      expect(mockDataQueryBuilder.andWhere).toHaveBeenCalledWith('drg.gameId = :gameId', { gameId: 'game-123' });
      expect(mockDataQueryBuilder.andWhere).toHaveBeenCalledWith('drg.gameProvider = :provider', { provider: 'PRAGMATIC' });
      expect(mockDataQueryBuilder.andWhere).toHaveBeenCalledWith('drg.accountType = :accountType', { accountType: 'REAL' });
      expect(mockDataQueryBuilder.orderBy).toHaveBeenCalledWith('"gameName"', 'DESC');
    });
  });

  describe('saveDailyReportGamePlayerPartner', () => {
    it('deve salvar relatório de jogos por jogador e parceiro', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            gameId: 'game-1',
            playerId: 'player-1',
            partnerId: 'partner-1',
            totalBetAmount: '1000',
            totalWinAmount: '800',
          },
        ]),
      };

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-01'),
      });
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockEntityManager.findOneBy.mockResolvedValue(null);
      mockEntityManager.insert.mockResolvedValue({});

      await service.saveDailyReportGamePlayerPartner();

      expect(mockEntityManager.insert).toHaveBeenCalled();
    });
  });

  describe('getDailyReportGamePlayerPartner', () => {
    it('deve retornar relatório de jogos por jogador e parceiro', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
      };

      mockEntityManager.find.mockResolvedValue([
        {
          gameId: 'game-1',
          playerId: 'player-1',
          totalBet: 1000,
        },
      ]);
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await service.getDailyReportGamePlayerPartner(
        '2024-01-01',
        '2024-01-02',
        mockRequest
      );

      expect(result).toBeDefined();
    });
  });

  describe('getDailyReportGamePlayerPartnerPagination', () => {
    it('deve retornar relatório paginado de jogos por jogador e parceiro', async () => {
      const filter = {
        page: 1,
        limit: 10,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        gameId: '',
        provider: '',
        playerId: '',
        orderBy: '',
        sortOrder: 'ASC' as any,
      };

      const mockDataQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            playerId: 'player-1',
            gameId: 'game-1',
            gameName: 'Game 1',
            gameProvider: 'provider-1',
            totalBet: '1000',
            totalSettle: '800',
          },
        ]),
      };

      const mockCountQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ totalItems: '1' }),
      };

      // Mock para retornar diferentes QueryBuilders baseado no que é chamado
      mockEntityManager.createQueryBuilder
        .mockReturnValueOnce(mockDataQueryBuilder)
        .mockReturnValueOnce(mockCountQueryBuilder);

      const result = await service.getDailyReportGamePlayerPartnerPagination(
        filter,
        mockRequest
      );

      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('totalItems');
    });
    it('deve usar orderBy padrão se valor não aceito', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        orderBy: 'invalido',
        sortOrder: 'ASC' as any,
        gameId: '',
        provider: '',
        playerId: '',
      };
      const mockQb = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
      };
      const mockCountQb = {
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ totalItems: '0' }),
      };
      mockEntityManager.createQueryBuilder
        .mockReturnValueOnce(mockQb)
        .mockReturnValueOnce(mockCountQb);
      const result = await service.getDailyReportGamePlayerPartnerPagination(filter, mockRequest);
      expect(result.data).toEqual([]);
      expect(result.totalItems).toBe(0);
    });
    it('deve usar sortOrder padrão se não informado', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        orderBy: 'playerId',
        gameId: '',
        provider: '',
        playerId: '',
      };
      const mockQb = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
      };
      const mockCountQb = {
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ totalItems: '0' }),
      };
      mockEntityManager.createQueryBuilder
        .mockReturnValueOnce(mockQb)
        .mockReturnValueOnce(mockCountQb);
      const result = await service.getDailyReportGamePlayerPartnerPagination(filter, mockRequest);
      expect(result.currentPage).toBe(1);
      expect(result.pageSize).toBe(10);
      expect(result.data).toEqual([]);
    });
    it('deve usar page e pageSize padrão se valores inválidos', async () => {
      const filter = {
        page: 0,
        pageSize: 0,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        orderBy: 'playerId',
        sortOrder: 'ASC' as any,
        gameId: '',
        provider: '',
        playerId: '',
      };
      const mockQb = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
      };
      const mockCountQb = {
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ totalItems: '0' }),
      };
      mockEntityManager.createQueryBuilder
        .mockReturnValueOnce(mockQb)
        .mockReturnValueOnce(mockCountQb);
      const result = await service.getDailyReportGamePlayerPartnerPagination(filter, mockRequest);
      expect(result.currentPage).toBe(1);
      expect(result.pageSize).toBe(10);
    });
    it('deve aplicar filtros opcionais gameId, provider e playerId', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        orderBy: 'playerId',
        sortOrder: 'ASC' as any,
        gameId: 'g1',
        provider: 'prov1',
        playerId: 'p1',
      };
      const mockQb = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
      };
      const mockCountQb = {
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ totalItems: '0' }),
      };
      mockEntityManager.createQueryBuilder
        .mockReturnValueOnce(mockQb)
        .mockReturnValueOnce(mockCountQb);
      await service.getDailyReportGamePlayerPartnerPagination(filter, mockRequest);
      expect(mockQb.andWhere).toHaveBeenCalled();
    });
    it('deve lançar HttpException se countQb lançar erro', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        orderBy: 'playerId',
        sortOrder: 'ASC' as any,
        gameId: '',
        provider: '',
        playerId: '',
      };
      const mockQb = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
      };
      const mockCountQb = {
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockRejectedValue(new Error('Erro countQb')),
      };
      mockEntityManager.createQueryBuilder
        .mockReturnValueOnce(mockQb)
        .mockReturnValueOnce(mockCountQb);
      await expect(service.getDailyReportGamePlayerPartnerPagination(filter, mockRequest)).rejects.toThrow();
    });
    it('deve lançar BadRequestException quando fromDate não for fornecida', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '',
        toDate: '2024-01-02',
        gameId: '',
        provider: '',
        playerId: '',
        orderBy: '',
        sortOrder: 'ASC' as any,
      };
      // Mock para getDateRange retornar valores falsos
      mockDateService.getDateRange.mockReturnValue({ fromDate: undefined, toDate: new Date('2024-01-02') });
      await expect(
        service.getDailyReportGamePlayerPartnerPagination(filter, mockRequest)
      ).rejects.toThrow('É necessário informar a data inicial e final');
    });
    it('deve lançar BadRequestException quando toDate não for fornecida', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '',
        gameId: '',
        provider: '',
        playerId: '',
        orderBy: '',
        sortOrder: 'ASC' as any,
      };
      mockDateService.getDateRange.mockReturnValue({ fromDate: new Date('2024-01-01'), toDate: undefined });
      await expect(
        service.getDailyReportGamePlayerPartnerPagination(filter, mockRequest)
      ).rejects.toThrow('É necessário informar a data inicial e final');
    });
  });

  describe('saveDailyReportPlayer', () => {
    it('deve salvar relatório diário de jogadores', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            playerId: 'player-1',
            partnerId: 'partner-1',
            totalBetAmount: '1000',
            totalWinAmount: '800',
            totalBets: '10',
          },
        ]),
      };

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-01'),
      });
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockEntityManager.findOneBy.mockResolvedValue(null);
      mockEntityManager.insert.mockResolvedValue({});

      await service.saveDailyReportPlayer();

      expect(mockEntityManager.insert).toHaveBeenCalled();
    });
  });

  describe('getDailyReportPlayer', () => {
    it('deve retornar relatório de jogadores', async () => {
      const filter = {
        page: 1,
        limit: 10,
        pageSize: 10,
        partners: 'partner-1',
        orderBy: '',
        sortOrder: 'ASC' as any,
      };

      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            playerId: 'player-1',
            totalBet: '1000',
            totalSettle: '800',
          },
        ]),
        getRawOne: jest.fn().mockResolvedValue({
          playerId: 'player-1',
          totalBet: '1000',
          totalSettle: '800',
        }),
      };

      mockEntityManager.find.mockResolvedValue([
        {
          playerId: 'player-1',
          totalBet: 1000,
          totalSettle: 800,
        },
      ]);
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await service.getDailyReportPlayer(
        '2024-01-01',
        '2024-01-02',
        filter,
        mockRequest,
        'player-1',
        '<EMAIL>'
      );

      expect(result).toBeDefined();
    });

    it('deve tratar erro durante consulta de relatório de jogadores', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        orderBy: 'partnerId',
        sortOrder: 'ASC' as any,
        partners: 'partner-1',
      };

      mockEntityManager.createQueryBuilder.mockImplementation(() => {
        throw new Error('Database connection failed');
      });

      await expect(
        service.getDailyReportPlayer(
          '2024-01-01',
          '2024-01-02',
          filter,
          mockRequest,
          'player-1',
          '<EMAIL>'
        )
      ).rejects.toThrow('Erro ao buscar report game');
    });

    it('deve aplicar filtros de playerId e email corretamente', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        orderBy: 'partnerId',
        sortOrder: 'ASC' as any,
        partners: 'partner-1',
      };

      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
        getRawOne: jest.fn().mockResolvedValue({ count: '0' }),
      };

      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      await service.getDailyReportPlayer(
        '2024-01-01',
        '2024-01-02',
        filter,
        mockRequest,
        'player-123',
        '<EMAIL>'
      );

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('player_id = :playerId', { playerId: 'player-123' });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('email = :email', { email: '<EMAIL>' });
    });

    it('deve usar orderBy padrão quando campo inválido for fornecido', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        orderBy: 'invalidField',
        sortOrder: 'ASC' as any,
        partners: 'partner-1',
      };

      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
        getRawOne: jest.fn().mockResolvedValue({ count: '0' }),
      };

      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      await service.getDailyReportPlayer(
        '2024-01-01',
        '2024-01-02',
        filter,
        mockRequest,
        undefined,
        undefined
      );

      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('"partnerId"', 'ASC');
    });
  });

  describe('getDailyReportPlayerById', () => {
    it('deve retornar relatório de jogador específico', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            playerId: 'player-1',
            totalBet: 1000,
            totalSettle: 800,
          },
        ]),
      };

      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await service.getDailyReportPlayerById('player-1', mockRequest);

      expect(result).toBeDefined();
    });

    it('deve retornar array vazio quando jogador não existe', async () => {
      mockEntityManager.find.mockResolvedValue([]);

      const result = await service.getDailyReportPlayerById('nonexistent-player', mockRequest);

      expect(result).toEqual([]);
      expect(mockEntityManager.find).toHaveBeenCalledWith(
        expect.anything(),
        {
          where: {
            partnerId: 'partner-1',
            playerId: 'nonexistent-player',
          },
        }
      );
    });

    it('deve usar partnerId do header da requisição', async () => {
      const customRequest = {
        headers: {
          'partner-id': 'custom-partner-123',
        },
      } as any;

      mockEntityManager.find.mockResolvedValue([]);

      await service.getDailyReportPlayerById('player-1', customRequest);

      expect(mockEntityManager.find).toHaveBeenCalledWith(
        expect.anything(),
        {
          where: {
            partnerId: 'custom-partner-123',
            playerId: 'player-1',
          },
        }
      );
    });

    it('deve retornar múltiplos registros para o mesmo jogador', async () => {
      const multipleRecords = [
        {
          playerId: 'player-1',
          partnerId: 'partner-1',
          totalBet: 1000,
          date: new Date('2024-01-01'),
        },
        {
          playerId: 'player-1',
          partnerId: 'partner-1',
          totalBet: 2000,
          date: new Date('2024-01-02'),
        },
      ];

      mockEntityManager.find.mockResolvedValue(multipleRecords);

      const result = await service.getDailyReportPlayerById('player-1', mockRequest);

      expect(result).toHaveLength(2);
      expect(result).toEqual(multipleRecords);
    });
  });

  describe('saveDailyReportProviders', () => {
    it('deve salvar relatório diário de provedores', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            provider: 'prov-1',
            partnerId: 'partner-1',
            totalBetAmount: '1000',
            totalWinAmount: '800',
          },
        ]),
      };
      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-01'),
      });
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockEntityManager.findOneBy.mockResolvedValue(null);
      mockEntityManager.insert.mockResolvedValue({});
      await service.saveDailyReportProviders();
      expect(mockEntityManager.insert).toHaveBeenCalled();
    });
  });

  describe('getDailyReportProviders', () => {
    it('deve retornar relatório de provedores', async () => {
      mockEntityManager.find.mockResolvedValue([
        { provider: 'prov-1', totalBet: 1000, totalSettle: 800 },
      ]);
      const result = await service.getDailyReportProviders('2024-01-01', '2024-01-02', mockRequest);
      expect(result).toBeDefined();
    });
  });

  describe('getDailyReportProvidersPagination', () => {
    it('deve lançar BadRequestException se datas não forem fornecidas', async () => {
      const filter = { page: 1, pageSize: 10, partners: 'p1' };
      await expect(
        service.getDailyReportProvidersPagination('', '', filter, mockRequest)
      ).rejects.toThrow('É necessário informar a data inicial e final');
    });
    it('deve lançar erro se a query falhar', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        orderBy: '',
        sortOrder: 'ASC' as any,
      };
      mockEntityManager.query.mockRejectedValueOnce(new Error('Erro DB'));
      await expect(
        service.getDailyReportProvidersPagination('2024-01-01', '2024-01-02', filter, mockRequest)
      ).rejects.toThrow('Erro DB');
    });
    it('deve usar orderBy padrão se valor não aceito', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        orderBy: 'invalido',
        sortOrder: 'ASC' as any,
      };
      mockEntityManager.query.mockResolvedValue([{ totalItems: '1' }]);
      mockEntityManager.query.mockResolvedValueOnce([]);
      mockEntityManager.query.mockResolvedValueOnce([{ totalItems: '1' }]);
      await service.getDailyReportProvidersPagination('2024-01-01', '2024-01-02', filter, mockRequest);
      expect(filter.orderBy).toBe('gameProvider');
    });
  });

  describe('saveDailyReportPartner', () => {
    it('deve logar erro se ocorrer exceção', async () => {
      mockEntityManager.createQueryBuilder.mockImplementation(() => { throw new Error('Erro'); });
      const loggerSpy = jest.spyOn(service['logger'], 'error').mockImplementation();
      await service.saveDailyReportPartner();
      expect(loggerSpy).toHaveBeenCalled();
      loggerSpy.mockRestore();
    });
  });

  describe('getDailyReportPartner', () => {
    it('deve retornar array vazio se não houver dados', async () => {
      mockEntityManager.find.mockResolvedValue([]);
      const result = await service.getDailyReportPartner('2024-01-01', '2024-01-02', mockRequest);
      expect(result).toEqual([]);
    });
  });

  describe('getDailyReportPartnerPagination', () => {
    it('deve lançar BadRequestException se datas não forem fornecidas', async () => {
      const filter = { page: 1, pageSize: 10, partners: 'p1' };
      await expect(
        service.getDailyReportPartnerPagination('', '', filter, mockRequest)
      ).rejects.toThrow('É necessário informar a data inicial e final');
    });
    it('deve lançar erro se a query falhar', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        orderBy: '',
        sortOrder: 'ASC' as any,
      };
      mockEntityManager.query.mockRejectedValueOnce(new Error('Erro DB'));
      await expect(
        service.getDailyReportPartnerPagination('2024-01-01', '2024-01-02', filter, mockRequest)
      ).rejects.toThrow('Erro DB');
    });
    it('deve usar orderBy padrão se valor não aceito', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        orderBy: 'invalido',
        sortOrder: 'ASC' as any,
      };
      mockEntityManager.query.mockResolvedValue([{ totalItems: '1' }]);
      mockEntityManager.query.mockResolvedValueOnce([]);
      mockEntityManager.query.mockResolvedValueOnce([{ totalItems: '1' }]);
      await service.getDailyReportPartnerPagination('2024-01-01', '2024-01-02', filter, mockRequest);
      expect(filter.orderBy).toBe('partnerId');
    });
  });

  describe('findGameProfitability', () => {
    it('deve retornar dados de rentabilidade de jogos', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        partners: 'partner-1',
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        orderBy: '',
        sortOrder: 'ASC' as any,
        dateRange: { from: '2024-01-01', to: '2024-01-02' },
      };
      const mockDataQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          { gameId: 'game-1', ggr: 200 },
        ]),
      };
      const mockCountQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ totalItems: '1' }),
      };
      mockEntityManager.createQueryBuilder
        .mockReturnValueOnce(mockDataQueryBuilder)
        .mockReturnValueOnce(mockCountQueryBuilder);
      mockEntityManager.query.mockResolvedValue([{ totalItems: '1' }]);
      const result = await service.findGameProfitability(filter, 'partner-1');
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('totalItems');
    });

    it('deve lançar HttpException quando dateRange não for fornecido', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        orderBy: 'profitability',
        sortOrder: 'ASC' as any,
        dateRange: undefined,
        partners: 'partner-1',
      };

      await expect(
        service.findGameProfitability(filter, 'partner-1')
      ).rejects.toThrow('É necessário informar o período de datas');
    });

    it('deve lançar HttpException quando dateRange.from não for fornecido', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        orderBy: 'profitability',
        sortOrder: 'ASC' as any,
        dateRange: { from: '', to: '2024-01-02' },
        partners: 'partner-1',
      };

      await expect(
        service.findGameProfitability(filter, 'partner-1')
      ).rejects.toThrow('É necessário informar o período de datas');
    });

    it('deve processar dados do dia atual quando data inclui hoje', async () => {
      const today = new Date().toISOString().split('T')[0];
      const filter = {
        page: 1,
        pageSize: 10,
        orderBy: 'profitability',
        sortOrder: 'ASC' as any,
        dateRange: { from: today, to: today }, partners: 'partner-1',
        gameProvider: 'PRAGMATIC',
      };

      // Mock do getDailyReportGame para simular dados do dia atual
      const mockTodayData = [
        {
          gameId: 'game-1',
          gameName: 'Game 1',
          gameProvider: 'PRAGMATIC',
          totalBet: 1000,
          totalSettle: 800,
          ggr: 200,
        },
        {
          gameId: 'game-1', // Mesmo jogo, deve ser agrupado
          gameName: 'Game 1',
          gameProvider: 'PRAGMATIC',
          totalBet: 500,
          totalSettle: 400,
          ggr: 100,
        },
      ];

      jest.spyOn(service, 'getDailyReportGame').mockResolvedValue(mockTodayData);

      const result = await service.findGameProfitability(filter, 'partner-1');

      expect(result.data).toHaveLength(1); // Dados agrupados por gameId
      expect(result.data[0]).toEqual({
        gameId: 'game-1',
        gameName: 'Game 1',
        gameProvider: 'PRAGMATIC',
        totalBet: 1500, // 1000 + 500
        totalSettle: 1200, // 800 + 400
        ggr: 300, // 200 + 100
        profitability: 20, // (300 / 1500) * 100
      });
    });

    it('deve usar valores padrão para paginação e ordenação', async () => {
      const filter = {
        page: 0, // Valor inválido
        pageSize: 0, // Valor inválido
        orderBy: undefined,
        sortOrder: undefined,
        dateRange: { from: '2024-01-01', to: '2024-01-02' }, partners: 'partner-1',
      };

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-02'),
      });

      mockEntityManager.query
        .mockResolvedValueOnce([]) // Dados
        .mockResolvedValueOnce([{ totalItems: '0' }]); // Count

      const result = await service.findGameProfitability(filter, 'partner-1');

      expect(result.currentPage).toBe(1);
      expect(result.pageSize).toBe(10);
    });

    it('deve aplicar filtro de gameProvider na query SQL', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        orderBy: 'profitability',
        sortOrder: 'ASC' as any,
        dateRange: { from: '2024-01-01', to: '2024-01-02' }, partners: 'partner-1',
        gameProvider: 'EVOLUTION',
      };

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-02'),
      });

      mockEntityManager.query
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([{ totalItems: '0' }]);

      await service.findGameProfitability(filter, 'partner-1');

      expect(mockEntityManager.query).toHaveBeenCalledWith(
        expect.stringContaining('AND game_provider = $6'),
        expect.arrayContaining(['EVOLUTION'])
      );
    });

    it('deve calcular profitabilidade corretamente', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        orderBy: 'profitability',
        sortOrder: 'ASC' as any,
        dateRange: { from: '2024-01-01', to: '2024-01-02' }, partners: 'partner-1',
      };

      const mockQueryResult = [
        {
          gameId: 'game-1',
          gameName: 'High Profit Game',
          gameProvider: 'PRAGMATIC',
          totalBet: '1000',
          totalSettle: '700',
          ggr: '300',
          profitability: '30.00', // 30%
        },
        {
          gameId: 'game-2',
          gameName: 'Low Profit Game',
          gameProvider: 'EVOLUTION',
          totalBet: '2000',
          totalSettle: '1900',
          ggr: '100',
          profitability: '5.00', // 5%
        },
      ];

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-02'),
      });

      mockEntityManager.query
        .mockResolvedValueOnce(mockQueryResult)
        .mockResolvedValueOnce([{ totalItems: '2' }]);

      const result = await service.findGameProfitability(filter, 'partner-1');

      expect(result.data).toHaveLength(2);
      expect(result.data[0].profitability).toBe(30);
      expect(result.data[1].profitability).toBe(5);
      expect(result.totalItems).toBe(2);
    });
  });

  describe('saveDailyReportGamePlayerPartner', () => {
    it('deve logar erro se findOneBy lançar exceção', async () => {
      const loggerSpy = jest.spyOn(service['logger'], 'error').mockImplementation();
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            gameId: 'game-1',
            playerId: 'player-1',
            partnerId: 'partner-1',
            totalBetAmount: '1000',
            totalWinAmount: '800',
          },
        ]),
      };
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockEntityManager.findOneBy.mockImplementation(() => { throw new Error('Erro findOneBy'); });
      await service.saveDailyReportGamePlayerPartner();
      expect(loggerSpy).toHaveBeenCalled();
      loggerSpy.mockRestore();
    });
  });

  describe('saveDailyReportPlayer', () => {
    it('deve logar erro se ocorrer exceção', async () => {
      mockEntityManager.createQueryBuilder.mockImplementation(() => { throw new Error('Erro'); });
      const loggerSpy = jest.spyOn(service['logger'], 'error').mockImplementation();
      await service.saveDailyReportPlayer();
      expect(loggerSpy).toHaveBeenCalled();
      loggerSpy.mockRestore();
    });
  });

  describe('findGameProfitability', () => {
    it('deve lançar HttpException se a query falhar', async () => {
      mockEntityManager.query.mockRejectedValueOnce(new Error('Erro DB'));
      const filter = {
        page: 1, pageSize: 10, partners: 'partner-1', fromDate: '2024-01-01', toDate: '2024-01-02',
        orderBy: '', sortOrder: 'ASC' as any, dateRange: { from: '2024-01-01', to: '2024-01-02' }
      };
      await expect(service.findGameProfitability(filter, 'partner-1')).rejects.toThrow();
    });
  });

  describe('getDailyReportGamePagination', () => {
    it('deve lançar erro se a query falhar', async () => {
      const filter = { page: 1, pageSize: 10, partners: 'p1', fromDate: '2024-01-01', toDate: '2024-01-02', orderBy: '', sortOrder: 'ASC' as any, gameId: '', provider: '', accountType: '' };
      mockEntityManager.createQueryBuilder.mockImplementation(() => { throw new Error('Erro'); });
      await expect(service.getDailyReportGamePagination(filter, mockRequest)).rejects.toThrow();
    });

    it('deve aplicar filtros corretamente na query', async () => {
      const filter = {
        page: 1,
        pageSize: 10,
        fromDate: '2024-01-01',
        toDate: '2024-01-02',
        gameId: 'game-123',
        provider: 'PRAGMATIC',
        accountType: 'REAL',
        orderBy: 'gameName',
        sortOrder: 'DESC' as any,
        partners: 'partner-1',
      };

      const mockDataQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
      };

      const mockCountQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ totalItems: '0' }),
      };

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-02'),
      });

      mockEntityManager.createQueryBuilder
        .mockReturnValueOnce(mockDataQueryBuilder)
        .mockReturnValueOnce(mockCountQueryBuilder);

      await service.getDailyReportGamePagination(filter, mockRequest);

      expect(mockDataQueryBuilder.andWhere).toHaveBeenCalledWith('drg.gameId = :gameId', { gameId: 'game-123' });
      expect(mockDataQueryBuilder.andWhere).toHaveBeenCalledWith('drg.gameProvider = :provider', { provider: 'PRAGMATIC' });
      expect(mockDataQueryBuilder.andWhere).toHaveBeenCalledWith('drg.accountType = :accountType', { accountType: 'REAL' });
      expect(mockDataQueryBuilder.orderBy).toHaveBeenCalledWith('"gameName"', 'DESC');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('deve tratar erro de conexão com banco de dados', async () => {
      const connectionError = new Error('Connection lost');
      mockEntityManager.createQueryBuilder.mockImplementation(() => {
        throw connectionError;
      });

      const loggerSpy = jest.spyOn(service['logger'], 'error').mockImplementation();

      await service.saveDailyReportGame();

      expect(loggerSpy).toHaveBeenCalledWith(connectionError);

      loggerSpy.mockRestore();
    });

    it('deve tratar dados com valores extremos', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        addGroupBy: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            gameId: 'game-extreme',
            gameName: 'Extreme Game',
            gameProvider: 'provider-1',
            partnerId: 'partner-1',
            totalBetAmount: '999999999999', // Valor muito alto
            totalWinAmount: '0', // Sem ganhos
            totalBets: '1000000',
            totalPlayer: '50000',
          },
        ]),
      };

      mockDateService.getDateRange.mockReturnValue({
        fromDate: new Date('2024-01-01'),
        toDate: new Date('2024-01-01'),
      });
      mockEntityManager.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockEntityManager.findOne.mockResolvedValue(null);
      mockEntityManager.insert.mockResolvedValue({});

      await service.saveDailyReportGame();

      expect(mockEntityManager.insert).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          totalBet: 999999999999,
          totalSettle: 0,
          ggr: 999999999999, // totalBet - totalSettle
          totalRound: 1000000,
          totalPlayers: 50000,
        })
      );
    });

    it('deve tratar timeout na query do banco', async () => {
      const timeoutError = new Error('Query timeout');
      timeoutError.name = 'QueryTimeoutError';

      mockEntityManager.query.mockRejectedValue(timeoutError);

      const filter = {
        page: 1,
        pageSize: 10,
        dateRange: { from: '2024-01-01', to: '2024-01-02' }, partners: 'partner-1',
      };

      await expect(
        service.findGameProfitability(filter, 'partner-1')
      ).rejects.toThrow('Query timeout');
    });

    it('deve tratar dados inconsistentes na agregação', async () => {
      const today = new Date().toISOString().split('T')[0];
      const filter = {
        page: 1,
        pageSize: 10,
        dateRange: { from: today, to: today }, partners: 'partner-1',
      };

      // Mock com dados inconsistentes (totalBet menor que totalSettle)
      const mockInconsistentData = [
        {
          gameId: 'game-inconsistent',
          gameName: 'Inconsistent Game',
          gameProvider: 'PROVIDER',
          totalBet: 100,
          totalSettle: 200, // Maior que totalBet
          ggr: -100, // GGR negativo
        },
      ];

      jest.spyOn(service, 'getDailyReportGame').mockResolvedValue(mockInconsistentData);

      const result = await service.findGameProfitability(filter, 'partner-1');

      expect(result.data).toHaveLength(1);
      expect(result.data[0].ggr).toBe(-100);
      expect(result.data[0].profitability).toBe(-100); // (-100 / 100) * 100
    });

    it('deve tratar divisão por zero no cálculo de profitabilidade', async () => {
      const today = new Date().toISOString().split('T')[0];
      const filter = {
        page: 1,
        pageSize: 10,
        dateRange: { from: today, to: today }, partners: 'partner-1',
      };

      // Mock com totalBet = 0
      const mockZeroBetData = [
        {
          gameId: 'game-zero-bet',
          gameName: 'Zero Bet Game',
          gameProvider: 'PROVIDER',
          totalBet: 0,
          totalSettle: 0,
          ggr: 0,
        },
      ];

      jest.spyOn(service, 'getDailyReportGame').mockResolvedValue(mockZeroBetData);

      const result = await service.findGameProfitability(filter, 'partner-1');

      expect(result.data).toHaveLength(1);
      expect(result.data[0].profitability).toBe(0); // Deve tratar divisão por zero
    });
  });
});
