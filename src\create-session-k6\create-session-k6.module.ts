import { DatabaseModule } from '@/common/database/database.module';
import { HMACModule } from '@/hmac/hmac.module';
import { PartnerService } from '@/partner/partner.service';
import { PlayerService } from '@/player/player.service';
import { WalletService } from '@/wallet/wallet.service';
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { CreateSessionK6Controller } from './create-session-k6.controller';
import { CreateSessionK6Service } from './create-session-k6.service';

const modules = [HMACModule, JwtModule, DatabaseModule, HttpModule];

@Module({
  imports: [...modules],
  providers: [
    CreateSessionK6Service,
    WalletService,
    ConfigService,
    PlayerService,
    PartnerService,
  ],
  controllers: [CreateSessionK6Controller],
})
export class CreateSessionK6Module {}
