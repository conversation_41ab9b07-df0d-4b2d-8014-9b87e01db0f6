import { ApiPartnerIdHeader } from "@/common/decorators/swagger.decorator";
import { InternalApiKeyGuard } from "@/common/guards/internal/internal.guard";
import { Controller, Get, Req, UseGuards } from "@nestjs/common";
import {
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { CasinoTransactionService } from "../services/casino-transaction.service";
import { CasinoTransactionEntity } from "./entities/casino-transactions.entity";

@ApiTags("BetCasinoTransaction")
@ApiPartnerIdHeader()
@ApiForbiddenResponse()
@ApiInternalServerErrorResponse()
@Controller("bet-casino-transaction")
@ApiBearerAuth("access-token")
export class BetCasinoTransactionController {
  constructor(
    private readonly casinoTransactionService: CasinoTransactionService
  ) {}

  @UseGuards(InternalApiKeyGuard)
  @Get("today")
  @ApiOperation({
    summary: "Obter maiores ganhos do dia",
    description: "Endpoint para obter os maiores ganhos do dia",
  })
  @ApiResponse({
    status: 200,
    description: "Retorna os maiores ganhos do dia",
    type: CasinoTransactionEntity,
  })
  async getTodayWinnings(@Req() req) {
    return this.casinoTransactionService.getToday(req);
  }

  @UseGuards(InternalApiKeyGuard)
  @Get("latest")
  @ApiOperation({
    summary: "Obter últimos ganhos",
    description: "Endpoint para obter os últimos ganhos registrados",
  })
  @ApiResponse({
    status: 200,
    description: "Retorna os últimos ganhos registrados",
    type: CasinoTransactionEntity,
  })
  async getLatestWinnings(@Req() req) {
    return this.casinoTransactionService.getLatest(req);
  }

  @UseGuards(InternalApiKeyGuard)
  @Get("top-winners-last-7-days")
  @ApiOperation({
    summary: "Obter maiores ganhadores dos últimos 7 dias",
    description: "Endpoint para obter os maiores ganhadores dos últimos 7 dias",
  })
  @ApiResponse({
    status: 200,
    description: "Retorna os maiores ganhadores dos últimos 7 dias",
    type: CasinoTransactionEntity,
  })
  async getTopWinnersLast7Days(@Req() req) {
    return this.casinoTransactionService.getTopWinnersLast7Days(req);
  }
}
